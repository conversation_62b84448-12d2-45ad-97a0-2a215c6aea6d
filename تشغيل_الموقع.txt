🐍 متجر كوبرا - تعليمات التشغيل السريع

========================================
📋 خطوات التشغيل (3 خطوات فقط!)
========================================

1️⃣ تثبيت Node.js:
   • اذهب إلى: https://nodejs.org/
   • حمل النسخة LTS (الموصى بها)
   • ثبت البرنامج وأعد تشغيل الكمبيوتر

2️⃣ تشغيل الموقع:
   • اضغط مرتين على ملف: start-cobra.bat
   • أو افتح PowerShell واكتب: .\start-cobra.ps1

3️⃣ فتح الموقع:
   • سيفتح المتصفح تلقائياً
   • أو اذهب إلى: http://localhost:3000

========================================
🚀 طرق التشغيل البديلة
========================================

الطريقة الأولى - ملف Batch:
   • اضغط مرتين على: start-cobra.bat

الطريقة الثانية - PowerShell:
   • افتح PowerShell في مجلد المشروع
   • اكتب: .\start-cobra.ps1

الطريقة الثالثة - يدوياً:
   • افتح Command Prompt
   • اكتب: npm install
   • ثم: npm run dev

========================================
🌐 صفحات الموقع
========================================

الصفحة الرئيسية:
http://localhost:3000/

المنتجات:
http://localhost:3000/products

الخدمات:
http://localhost:3000/services

سلة التسوق:
http://localhost:3000/cart

من نحن:
http://localhost:3000/about

اتصل بنا:
http://localhost:3000/contact

الطلبات (بعد تسجيل الدخول):
http://localhost:3000/orders

الملف الشخصي (بعد تسجيل الدخول):
http://localhost:3000/profile

========================================
🎯 اختبار الميزات
========================================

1. تسجيل الدخول:
   • اضغط "تسجيل الدخول" في الأعلى
   • أنشئ حساب جديد أو سجل دخول

2. تحديد الموقع:
   • سيطلب منك تحديد موقعك عند أول زيارة
   • يمكنك السماح بالوصول للموقع أو اختيار المدينة

3. تصفح المنتجات:
   • اذهب لصفحة "المنتجات"
   • جرب البحث والفلترة
   • أضف منتجات للسلة

4. طلب خدمة:
   • اذهب لصفحة "الخدمات"
   • اختر خدمة واضغط "اطلب الخدمة"
   • املأ النموذج

5. سلة التسوق:
   • اضغط على أيقونة السلة
   • عدل الكميات أو احذف منتجات
   • اضغط "إتمام الطلب"

========================================
🔧 حل المشاكل الشائعة
========================================

المشكلة: "npm is not recognized"
الحل: ثبت Node.js من https://nodejs.org/

المشكلة: "Port 3000 is already in use"
الحل: أغلق أي برامج تستخدم المنفذ 3000

المشكلة: الموقع لا يحمل
الحل: تأكد من تشغيل npm run dev بنجاح

المشكلة: أخطاء في التثبيت
الحل: احذف مجلد node_modules واكتب npm install

========================================
📞 الدعم والمساعدة
========================================

البريد الإلكتروني: <EMAIL>
الهاتف: +966 50 123 4567

الملفات المفيدة:
• README.md - دليل شامل
• QUICK_START.md - بدء سريع
• DEVELOPER_GUIDE.md - دليل المطور
• INSTALL_NODEJS.md - تثبيت Node.js

========================================
✅ قائمة التحقق
========================================

□ تثبيت Node.js
□ تشغيل start-cobra.bat أو start-cobra.ps1
□ فتح http://localhost:3000
□ اختبار تسجيل الدخول
□ اختبار إضافة منتجات للسلة
□ اختبار طلب خدمة
□ تصفح جميع الصفحات

========================================

🎉 مبروك! موقع متجر كوبرا جاهز للاستخدام!

للتوقف عن تشغيل الموقع:
اضغط Ctrl+C في نافذة Command Prompt
