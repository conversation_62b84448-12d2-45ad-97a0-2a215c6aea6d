<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز البدوي - اسم له تاريخ</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 2rem;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .slogan {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 3rem 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
        }
        
        .feature h3 {
            margin-bottom: 1rem;
            color: #4ecdc4;
            font-size: 1.2rem;
        }
        
        .feature p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .status {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            padding: 1rem 2rem;
            border-radius: 25px;
            display: inline-block;
            margin: 2rem 0;
            border: 1px solid #4caf50;
            font-weight: bold;
        }
        
        .links {
            margin-top: 3rem;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
        }
        
        .link {
            display: inline-block;
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-weight: 600;
        }
        
        .link:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .note {
            margin-top: 3rem;
            padding: 1.5rem;
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            border-radius: 15px;
            color: #ffc107;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .slogan {
                font-size: 1.2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .feature {
                padding: 1.5rem;
            }
            
            .links {
                flex-direction: column;
                align-items: center;
            }
            
            .link {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">ب</div>
        <h1>مركز البدوي</h1>
        <p class="slogan">اسم له تاريخ</p>
        
        <div class="status">
            ✅ الموقع جاهز للاستخدام
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🔐 نظام المستخدمين المتكامل</h3>
                <p>تسجيل دخول وإنشاء حسابات منفصلة مع حماية كاملة وإدارة الملفات الشخصية</p>
            </div>
            <div class="feature">
                <h3>🛒 نظام التسوق الذكي</h3>
                <p>سلة تسوق تفاعلية مع إتمام شراء متكامل وحفظ تلقائي للمنتجات</p>
            </div>
            <div class="feature">
                <h3>🎨 ثيمات احترافية</h3>
                <p>6 ثيمات جاهزة مع محرر ألوان مخصص وتبديل فوري بين الأوضاع</p>
            </div>
            <div class="feature">
                <h3>🌍 ترجمة شاملة</h3>
                <p>3 لغات مع ترجمة كاملة لجميع عناصر الموقع وتبديل فوري</p>
            </div>
            <div class="feature">
                <h3>⚙️ إعدادات متقدمة</h3>
                <p>إدارة شاملة لمعلومات الاتصال والدفع ووسائل التواصل الاجتماعي</p>
            </div>
            <div class="feature">
                <h3>📱 تصميم متجاوب</h3>
                <p>يعمل بشكل مثالي على جميع الأجهزة مع تجربة مستخدم ممتازة</p>
            </div>
        </div>
        
        <div class="links">
            <a href="settings.html" class="link">⚙️ إعدادات الموقع</a>
            <a href="#" class="link" onclick="alert('صفحة تسجيل الدخول')">🔐 تسجيل الدخول</a>
            <a href="#" class="link" onclick="alert('صفحة إنشاء حساب')">📝 إنشاء حساب</a>
            <a href="#" class="link" onclick="alert('صفحة المنتجات')">🛍️ المنتجات</a>
            <a href="#" class="link" onclick="alert('صفحة الخدمات')">🔧 الخدمات</a>
        </div>
        
        <div class="note">
            <strong>📌 ملاحظة مهمة:</strong><br>
            هذا عرض مبسط للموقع. للحصول على الموقع الكامل مع جميع الميزات المتقدمة (Next.js, React, TypeScript)، 
            يرجى إصلاح مشكلة Node.js أو npm في النظام.
            <br><br>
            <strong>الميزات المتاحة حالياً:</strong> صفحة الإعدادات التفاعلية، التصميم المتجاوب، والواجهة الاحترافية.
        </div>
    </div>
</body>
</html>
