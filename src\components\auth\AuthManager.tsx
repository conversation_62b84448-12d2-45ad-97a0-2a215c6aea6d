'use client'

import { useState, useEffect } from 'react'
import { User, LogOut, Crown, Settings, ShoppingCart, Heart, Package } from 'lucide-react'
import UserLogin from './UserLogin'
import UserRegistration from './UserRegistration'
import { getCurrentUserSession, updateUserSession } from '@/components/products/PriceDisplay'

interface UserSession {
  isLoggedIn: boolean
  userId?: string
  name?: string
  email?: string
  userType: 'retail' | 'wholesale'
  isWholesaleVerified: boolean
}

interface AuthManagerProps {
  showLoginButton?: boolean
  showUserMenu?: boolean
  className?: string
}

export default function AuthManager({ 
  showLoginButton = true, 
  showUserMenu = true, 
  className = '' 
}: AuthManagerProps) {
  const [userSession, setUserSession] = useState<UserSession>({
    isLoggedIn: false,
    userType: 'retail',
    isWholesaleVerified: false
  })
  
  const [showLogin, setShowLogin] = useState(false)
  const [showRegister, setShowRegister] = useState(false)
  const [showUserDropdown, setShowUserDropdown] = useState(false)

  useEffect(() => {
    // Load user session on component mount
    const session = getCurrentUserSession()
    setUserSession(session)

    // Listen for session updates
    const handleSessionUpdate = () => {
      const updatedSession = getCurrentUserSession()
      setUserSession(updatedSession)
    }

    window.addEventListener('userSessionUpdated', handleSessionUpdate)
    return () => window.removeEventListener('userSessionUpdated', handleSessionUpdate)
  }, [])

  const handleLoginSuccess = () => {
    setShowLogin(false)
    const session = getCurrentUserSession()
    setUserSession(session)
  }

  const handleRegisterSuccess = (userType: 'retail' | 'wholesale') => {
    setShowRegister(false)
    const session = getCurrentUserSession()
    setUserSession(session)
  }

  const handleLogout = () => {
    // Clear user session
    updateUserSession({
      isLoggedIn: false,
      userType: 'retail',
      isWholesaleVerified: false
    })
    
    setUserSession({
      isLoggedIn: false,
      userType: 'retail',
      isWholesaleVerified: false
    })
    
    setShowUserDropdown(false)
    alert('تم تسجيل الخروج بنجاح')
  }

  const getUserTypeLabel = () => {
    if (userSession.userType === 'wholesale') {
      return userSession.isWholesaleVerified ? 'تاجر مفعل' : 'تاجر (غير مفعل)'
    }
    return 'مستخدم عادي'
  }

  const getUserTypeColor = () => {
    if (userSession.userType === 'wholesale' && userSession.isWholesaleVerified) {
      return 'text-yellow-600'
    }
    if (userSession.userType === 'wholesale' && !userSession.isWholesaleVerified) {
      return 'text-orange-600'
    }
    return 'text-blue-600'
  }

  if (!userSession.isLoggedIn) {
    // Show login/register buttons for guests
    return (
      <>
        {showLoginButton && (
          <div className={`flex items-center gap-2 ${className}`}>
            <button
              onClick={() => setShowLogin(true)}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm"
            >
              <User className="w-4 h-4" />
              تسجيل الدخول
            </button>
            <button
              onClick={() => setShowRegister(true)}
              className="border border-primary-600 text-primary-600 hover:bg-primary-50 px-4 py-2 rounded-lg text-sm"
            >
              إنشاء حساب
            </button>
          </div>
        )}

        {/* Login Modal */}
        {showLogin && (
          <UserLogin
            onClose={() => setShowLogin(false)}
            onSuccess={handleLoginSuccess}
            onSwitchToRegister={() => {
              setShowLogin(false)
              setShowRegister(true)
            }}
          />
        )}

        {/* Register Modal */}
        {showRegister && (
          <UserRegistration
            onClose={() => setShowRegister(false)}
            onSuccess={handleRegisterSuccess}
          />
        )}
      </>
    )
  }

  // Show user menu for logged-in users
  return (
    <>
      {showUserMenu && (
        <div className={`relative ${className}`}>
          <button
            onClick={() => setShowUserDropdown(!showUserDropdown)}
            className="flex items-center gap-2 bg-white border border-gray-200 hover:border-gray-300 px-3 py-2 rounded-lg text-sm"
          >
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              {userSession.userType === 'wholesale' && userSession.isWholesaleVerified ? (
                <Crown className="w-4 h-4 text-yellow-600" />
              ) : (
                <User className="w-4 h-4 text-primary-600" />
              )}
            </div>
            <div className="text-right">
              <div className="font-medium text-gray-900">{userSession.name}</div>
              <div className={`text-xs ${getUserTypeColor()}`}>
                {getUserTypeLabel()}
              </div>
            </div>
          </button>

          {/* User Dropdown Menu */}
          {showUserDropdown && (
            <div className="absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
              {/* User Info Header */}
              <div className="p-4 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    {userSession.userType === 'wholesale' && userSession.isWholesaleVerified ? (
                      <Crown className="w-6 h-6 text-yellow-600" />
                    ) : (
                      <User className="w-6 h-6 text-primary-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{userSession.name}</div>
                    <div className="text-sm text-gray-600">{userSession.email}</div>
                    <div className={`text-xs ${getUserTypeColor()} font-medium`}>
                      {getUserTypeLabel()}
                    </div>
                  </div>
                </div>

                {/* Wholesale Status */}
                {userSession.userType === 'wholesale' && (
                  <div className="mt-3">
                    {userSession.isWholesaleVerified ? (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                        <div className="flex items-center gap-2 text-green-800 text-xs">
                          <Crown className="w-3 h-3" />
                          <span className="font-medium">حساب جملة مفعل</span>
                        </div>
                        <div className="text-green-700 text-xs mt-1">
                          تستفيد من أسعار الجملة الخاصة
                        </div>
                      </div>
                    ) : (
                      <div className="bg-orange-50 border border-orange-200 rounded-lg p-2">
                        <div className="flex items-center gap-2 text-orange-800 text-xs">
                          <Crown className="w-3 h-3" />
                          <span className="font-medium">حساب جملة غير مفعل</span>
                        </div>
                        <div className="text-orange-700 text-xs mt-1">
                          اتصل بالإدارة للحصول على كود التفعيل
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Menu Items */}
              <div className="py-2">
                <button className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3">
                  <Package className="w-4 h-4" />
                  طلباتي
                </button>
                <button className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3">
                  <Heart className="w-4 h-4" />
                  المفضلة
                </button>
                <button className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3">
                  <ShoppingCart className="w-4 h-4" />
                  سلة التسوق
                </button>
                <button className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3">
                  <Settings className="w-4 h-4" />
                  إعدادات الحساب
                </button>
              </div>

              {/* Logout */}
              <div className="border-t border-gray-100 py-2">
                <button
                  onClick={handleLogout}
                  className="w-full px-4 py-2 text-right text-sm text-red-600 hover:bg-red-50 flex items-center gap-3"
                >
                  <LogOut className="w-4 h-4" />
                  تسجيل الخروج
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showUserDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserDropdown(false)}
        />
      )}
    </>
  )
}
