# Simple HTTP Server for Al-Badawi Center
param(
    [int]$Port = 3000
)

Write-Host "Starting Al-Badawi Center Server..." -ForegroundColor Green
Write-Host "Port: $Port" -ForegroundColor Yellow
Write-Host "URL: http://localhost:$Port" -ForegroundColor Cyan
Write-Host "Settings: http://localhost:$Port/settings.html" -ForegroundColor Cyan
Write-Host "To stop: Press Ctrl+C" -ForegroundColor Red
Write-Host ""

# Create HTTP listener
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$Port/")

try {
    $listener.Start()
    Write-Host "Server is running successfully!" -ForegroundColor Green
    Write-Host ""

    # Open browser after 2 seconds
    Start-Job -ScriptBlock {
        param($url)
        Start-Sleep -Seconds 2
        Start-Process $url
    } -ArgumentList "http://localhost:$Port" | Out-Null

    while ($listener.IsListening) {
        # Wait for request
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response

        # Get requested path
        $path = $request.Url.LocalPath
        Write-Host "Request: $($request.HttpMethod) $path" -ForegroundColor Blue
        
        # Set response headers
        $response.Headers.Add("Access-Control-Allow-Origin", "*")
        $response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE")
        $response.Headers.Add("Access-Control-Allow-Headers", "Content-Type")
        
        # Route handling
        $filePath = ""
        $contentType = "text/html; charset=utf-8"
        
        switch ($path) {
            "/" { $filePath = "demo.html" }
            "/index.html" { $filePath = "demo.html" }
            "/demo.html" { $filePath = "demo.html" }
            "/settings.html" { $filePath = "settings.html" }
            "/settings" { $filePath = "settings.html" }
            "/admin/settings" { $filePath = "settings.html" }
            default {
                # Try to serve file directly
                $requestedFile = $path.TrimStart('/')
                if ($requestedFile -and (Test-Path $requestedFile)) {
                    $filePath = $requestedFile
                    # Set content type based on extension
                    $extension = [System.IO.Path]::GetExtension($requestedFile).ToLower()
                    switch ($extension) {
                        ".css" { $contentType = "text/css; charset=utf-8" }
                        ".js" { $contentType = "application/javascript; charset=utf-8" }
                        ".json" { $contentType = "application/json; charset=utf-8" }
                        ".png" { $contentType = "image/png" }
                        ".jpg" { $contentType = "image/jpeg" }
                        ".jpeg" { $contentType = "image/jpeg" }
                        ".gif" { $contentType = "image/gif" }
                        ".svg" { $contentType = "image/svg+xml" }
                        ".ico" { $contentType = "image/x-icon" }
                        ".woff" { $contentType = "font/woff" }
                        ".woff2" { $contentType = "font/woff2" }
                        ".ttf" { $contentType = "font/ttf" }
                        ".eot" { $contentType = "application/vnd.ms-fontobject" }
                    }
                }
            }
        }
        
        # Serve file or 404
        if ($filePath -and (Test-Path $filePath)) {
            try {
                if ($contentType.StartsWith("image/") -or $contentType.StartsWith("font/") -or $contentType.Contains("fontobject")) {
                    # Binary files
                    $content = [System.IO.File]::ReadAllBytes($filePath)
                    $response.ContentType = $contentType
                    $response.ContentLength64 = $content.Length
                    $response.StatusCode = 200
                    $response.OutputStream.Write($content, 0, $content.Length)
                } else {
                    # Text files
                    $content = Get-Content $filePath -Raw -Encoding UTF8
                    $buffer = [System.Text.Encoding]::UTF8.GetBytes($content)
                    $response.ContentType = $contentType
                    $response.ContentLength64 = $buffer.Length
                    $response.StatusCode = 200
                    $response.OutputStream.Write($buffer, 0, $buffer.Length)
                }
                Write-Host "Sent: $filePath" -ForegroundColor Green
            }
            catch {
                Write-Host "Error reading file: $filePath - $($_.Exception.Message)" -ForegroundColor Red
                $response.StatusCode = 500
                $errorHtml = "<html><body><h1>500 - Internal Server Error</h1><p>Error reading file: $filePath</p></body></html>"
                $buffer = [System.Text.Encoding]::UTF8.GetBytes($errorHtml)
                $response.ContentType = "text/html; charset=utf-8"
                $response.ContentLength64 = $buffer.Length
                $response.OutputStream.Write($buffer, 0, $buffer.Length)
            }
        }
        else {
            # 404 Not Found
            $notFoundHtml = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - مركز البدوي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            margin: 0;
        }
        .container {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
        }
        h1 { font-size: 4rem; margin-bottom: 1rem; color: #ff6b6b; }
        p { font-size: 1.2rem; margin-bottom: 2rem; }
        a {
            color: #4ecdc4;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        a:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container">
        <h1>404</h1>
        <p>Page Not Found</p>
        <p style="font-size: 1rem; opacity: 0.8;">Requested path: $path</p>
        <a href="/">Back to Home</a>
    </div>
</body>
</html>
"@
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($notFoundHtml)
            $response.ContentType = "text/html; charset=utf-8"
            $response.ContentLength64 = $buffer.Length
            $response.StatusCode = 404
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
            Write-Host "404: $path" -ForegroundColor Yellow
        }
        
        $response.Close()
    }
}
catch {
    Write-Host "Server error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.ToString())" -ForegroundColor Red
}
finally {
    if ($listener -and $listener.IsListening) {
        $listener.Stop()
        Write-Host "Server stopped" -ForegroundColor Red
    }
    Write-Host "Press any key to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
