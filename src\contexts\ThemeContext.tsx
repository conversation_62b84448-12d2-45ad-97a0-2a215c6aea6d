'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

export interface Theme {
  id: string
  name: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
    textSecondary: string
    border: string
    success: string
    warning: string
    error: string
    info: string
  }
  isDark: boolean
}

export const themes: Theme[] = [
  {
    id: 'light',
    name: 'الوضع الفاتح',
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
      textSecondary: '#64748b',
      border: '#e2e8f0',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },
    isDark: false
  },
  {
    id: 'dark',
    name: 'الوضع الداكن',
    colors: {
      primary: '#3b82f6',
      secondary: '#94a3b8',
      accent: '#fbbf24',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
      textSecondary: '#94a3b8',
      border: '#334155',
      success: '#22c55e',
      warning: '#fbbf24',
      error: '#f87171',
      info: '#60a5fa'
    },
    isDark: true
  },
  {
    id: 'blue',
    name: 'الأزرق الاحترافي',
    colors: {
      primary: '#1e40af',
      secondary: '#6b7280',
      accent: '#06b6d4',
      background: '#f8fafc',
      surface: '#ffffff',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#d1d5db',
      success: '#059669',
      warning: '#d97706',
      error: '#dc2626',
      info: '#0284c7'
    },
    isDark: false
  },
  {
    id: 'green',
    name: 'الأخضر الطبيعي',
    colors: {
      primary: '#059669',
      secondary: '#6b7280',
      accent: '#84cc16',
      background: '#f9fafb',
      surface: '#ffffff',
      text: '#111827',
      textSecondary: '#6b7280',
      border: '#d1d5db',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },
    isDark: false
  },
  {
    id: 'purple',
    name: 'البنفسجي الملكي',
    colors: {
      primary: '#7c3aed',
      secondary: '#6b7280',
      accent: '#ec4899',
      background: '#faf9ff',
      surface: '#ffffff',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#8b5cf6'
    },
    isDark: false
  },
  {
    id: 'orange',
    name: 'البرتقالي الدافئ',
    colors: {
      primary: '#ea580c',
      secondary: '#6b7280',
      accent: '#facc15',
      background: '#fffbf5',
      surface: '#ffffff',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#fed7aa',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },
    isDark: false
  }
]

interface ThemeContextType {
  currentTheme: Theme
  setTheme: (themeId: string) => void
  toggleDarkMode: () => void
  customColors: Partial<Theme['colors']>
  updateCustomColors: (colors: Partial<Theme['colors']>) => void
  resetCustomColors: () => void
  isCustomTheme: boolean
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [currentThemeId, setCurrentThemeId] = useState('light')
  const [customColors, setCustomColors] = useState<Partial<Theme['colors']>>({})
  const [isCustomTheme, setIsCustomTheme] = useState(false)

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme')
    const savedCustomColors = localStorage.getItem('customColors')
    const savedIsCustom = localStorage.getItem('isCustomTheme')

    if (savedTheme) {
      setCurrentThemeId(savedTheme)
    }
    if (savedCustomColors) {
      try {
        setCustomColors(JSON.parse(savedCustomColors))
      } catch (e) {
        console.error('Error parsing custom colors:', e)
      }
    }
    if (savedIsCustom) {
      setIsCustomTheme(savedIsCustom === 'true')
    }
  }, [])

  // Apply theme to document
  useEffect(() => {
    const theme = getCurrentTheme()
    const root = document.documentElement

    // Apply CSS variables
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })

    // Apply dark class
    if (theme.isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [currentThemeId, customColors, isCustomTheme])

  const getCurrentTheme = (): Theme => {
    const baseTheme = themes.find(t => t.id === currentThemeId) || themes[0]
    
    if (isCustomTheme && Object.keys(customColors).length > 0) {
      return {
        ...baseTheme,
        id: 'custom',
        name: 'ثيم مخصص',
        colors: { ...baseTheme.colors, ...customColors }
      }
    }
    
    return baseTheme
  }

  const setTheme = (themeId: string) => {
    setCurrentThemeId(themeId)
    setIsCustomTheme(false)
    localStorage.setItem('theme', themeId)
    localStorage.setItem('isCustomTheme', 'false')
  }

  const toggleDarkMode = () => {
    const currentTheme = getCurrentTheme()
    const newThemeId = currentTheme.isDark ? 'light' : 'dark'
    setTheme(newThemeId)
  }

  const updateCustomColors = (colors: Partial<Theme['colors']>) => {
    const newCustomColors = { ...customColors, ...colors }
    setCustomColors(newCustomColors)
    setIsCustomTheme(true)
    localStorage.setItem('customColors', JSON.stringify(newCustomColors))
    localStorage.setItem('isCustomTheme', 'true')
  }

  const resetCustomColors = () => {
    setCustomColors({})
    setIsCustomTheme(false)
    localStorage.removeItem('customColors')
    localStorage.setItem('isCustomTheme', 'false')
  }

  const value: ThemeContextType = {
    currentTheme: getCurrentTheme(),
    setTheme,
    toggleDarkMode,
    customColors,
    updateCustomColors,
    resetCustomColors,
    isCustomTheme
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
