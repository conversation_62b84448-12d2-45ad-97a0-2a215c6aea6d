'use client'

import { useState } from 'react'
import { Mail, Lock, Eye, EyeOff, LogIn, AlertCircle, Crown, User } from 'lucide-react'
import { db } from '@/lib/database'
import { updateUserSession } from '@/components/products/PriceDisplay'

interface UserLoginProps {
  onClose: () => void
  onSuccess: () => void
  onSwitchToRegister: () => void
}

interface LoginFormData {
  email: string
  password: string
  rememberMe: boolean
}

export default function UserLogin({ onClose, onSuccess, onSwitchToRegister }: UserLoginProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false
  })

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      // Validation
      if (!formData.email || !formData.password) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة')
      }

      // Find customer by email
      const customer = db.getCustomerByEmail(formData.email)
      if (!customer) {
        throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة')
      }

      if (!customer.isActive) {
        throw new Error('تم إيقاف هذا الحساب. يرجى التواصل مع الإدارة')
      }

      // In a real application, you would verify the password hash
      // For demo purposes, we'll accept any password for existing users
      
      // Update last login date
      db.updateCustomer(customer.id, {
        lastOrderDate: new Date().toISOString()
      })

      // Create user session
      const userSession = {
        isLoggedIn: true,
        userId: customer.id,
        name: customer.name,
        email: customer.email,
        userType: customer.userType,
        isWholesaleVerified: customer.isWholesaleVerified
      }

      // Save session
      updateUserSession(userSession)

      // Show success message based on user type
      if (customer.userType === 'wholesale' && customer.isWholesaleVerified) {
        alert(`مرحباً ${customer.name}! تم تسجيل الدخول بنجاح. ستظهر لك الآن أسعار الجملة الخاصة.`)
      } else if (customer.userType === 'wholesale' && !customer.isWholesaleVerified) {
        alert(`مرحباً ${customer.name}! حسابك كتاجر لم يتم تفعيله بعد. يرجى التواصل مع الإدارة للحصول على كود التفعيل.`)
      } else {
        alert(`مرحباً ${customer.name}! تم تسجيل الدخول بنجاح.`)
      }

      onSuccess()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في تسجيل الدخول')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Demo accounts for testing
  const demoAccounts = [
    {
      email: '<EMAIL>',
      type: 'مستخدم عادي',
      icon: User,
      color: 'blue'
    },
    {
      email: '<EMAIL>',
      type: 'تاجر مفعل',
      icon: Crown,
      color: 'yellow'
    }
  ]

  const fillDemoAccount = (email: string) => {
    setFormData({
      ...formData,
      email,
      password: 'demo123'
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">تسجيل الدخول</h2>
              <p className="text-gray-600">ادخل إلى حسابك في مركز البدوي</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <span className="text-red-800">{error}</span>
            </div>
          )}

          {/* Demo Accounts */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-3">حسابات تجريبية للاختبار</h4>
            <div className="space-y-2">
              {demoAccounts.map((account, index) => {
                const Icon = account.icon
                return (
                  <button
                    key={index}
                    onClick={() => fillDemoAccount(account.email)}
                    className={`w-full p-2 bg-${account.color}-100 hover:bg-${account.color}-200 border border-${account.color}-200 rounded text-${account.color}-800 text-sm flex items-center gap-2 transition-colors`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{account.type}: {account.email}</span>
                  </button>
                )
              })}
            </div>
            <p className="text-xs text-blue-700 mt-2">
              كلمة المرور لجميع الحسابات التجريبية: demo123
            </p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleLogin} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                البريد الإلكتروني
              </label>
              <div className="relative">
                <input
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="أدخل بريدك الإلكتروني"
                />
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                كلمة المرور
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={(e) => setFormData({...formData, password: e.target.value})}
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="أدخل كلمة المرور"
                />
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="rememberMe"
                  checked={formData.rememberMe}
                  onChange={(e) => setFormData({...formData, rememberMe: e.target.checked})}
                  className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                />
                <label htmlFor="rememberMe" className="mr-2 text-sm text-gray-700">
                  تذكرني
                </label>
              </div>
              <button
                type="button"
                className="text-sm text-primary-600 hover:text-primary-700"
              >
                نسيت كلمة المرور؟
              </button>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white py-2 rounded-lg font-medium flex items-center justify-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  جاري تسجيل الدخول...
                </>
              ) : (
                <>
                  <LogIn className="w-4 h-4" />
                  تسجيل الدخول
                </>
              )}
            </button>
          </form>

          {/* Register Link */}
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              ليس لديك حساب؟{' '}
              <button
                onClick={onSwitchToRegister}
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                إنشاء حساب جديد
              </button>
            </p>
          </div>

          {/* Contact Info */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-xs text-gray-600 text-center">
              للمساعدة في تسجيل الدخول أو تفعيل حساب التاجر
              <br />
              اتصل بنا: +20 100 123 4567
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
