'use client'

import { useState, useEffect, useRef } from 'react'
import { Search, X, Package, Tag, FileText, Phone, Clock, TrendingUp } from 'lucide-react'
import { db } from '@/lib/database'
import Link from 'next/link'

interface SearchResult {
  id: string
  title: string
  description: string
  type: 'product' | 'category' | 'service' | 'page'
  url: string
  price?: number
  wholesalePrice?: number
  image?: string
  category?: string
  relevance: number
}

interface GlobalSearchProps {
  isOpen: boolean
  onClose: () => void
}

export default function GlobalSearch({ isOpen, onClose }: GlobalSearchProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [popularSearches] = useState([
    'هاتف ذكي', 'لابتوب', 'ساعة ذكية', 'سماعات', 'كاميرا', 'تابلت',
    'إلكترونيات', 'إكسسوارات', 'خدمات التوصيل', 'أسعار الجملة'
  ])
  
  const searchInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isOpen])

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved))
      } catch (e) {
        console.error('Error loading recent searches:', e)
      }
    }
  }, [])

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim().length > 0) {
        performSearch(query)
      } else {
        setResults([])
      }
    }, 300) // Debounce search

    return () => clearTimeout(timeoutId)
  }, [query])

  const performSearch = async (searchQuery: string) => {
    setIsLoading(true)
    
    try {
      const searchResults: SearchResult[] = []
      const lowerQuery = searchQuery.toLowerCase()

      // Search Products
      const products = db.getAllProducts()
      products.forEach(product => {
        let relevance = 0
        
        // Title match (highest relevance)
        if (product.name.toLowerCase().includes(lowerQuery)) {
          relevance += 10
        }
        
        // Description match
        if (product.description.toLowerCase().includes(lowerQuery)) {
          relevance += 5
        }
        
        // Brand match
        if (product.brand && product.brand.toLowerCase().includes(lowerQuery)) {
          relevance += 7
        }
        
        // Features match
        if (product.features.some(feature => feature.toLowerCase().includes(lowerQuery))) {
          relevance += 3
        }

        if (relevance > 0) {
          searchResults.push({
            id: product.id,
            title: product.name,
            description: product.description,
            type: 'product',
            url: `/products/${product.id}`,
            price: product.price,
            wholesalePrice: product.wholesalePrice,
            image: product.image,
            category: product.categoryId,
            relevance
          })
        }
      })

      // Search Categories
      const categories = db.getAllCategories()
      categories.forEach(category => {
        let relevance = 0
        
        if (category.name.toLowerCase().includes(lowerQuery)) {
          relevance += 8
        }
        
        if (category.description.toLowerCase().includes(lowerQuery)) {
          relevance += 4
        }

        if (relevance > 0) {
          searchResults.push({
            id: category.id,
            title: category.name,
            description: category.description,
            type: 'category',
            url: `/products?category=${category.id}`,
            relevance
          })
        }
      })

      // Search Services
      const services = db.getAllServices()
      services.forEach(service => {
        let relevance = 0
        
        if (service.name.toLowerCase().includes(lowerQuery)) {
          relevance += 8
        }
        
        if (service.description.toLowerCase().includes(lowerQuery)) {
          relevance += 4
        }

        if (relevance > 0) {
          searchResults.push({
            id: service.id,
            title: service.name,
            description: service.description,
            type: 'service',
            url: `/services/${service.id}`,
            price: service.price,
            wholesalePrice: service.wholesalePrice,
            relevance
          })
        }
      })

      // Search Static Pages
      const staticPages = [
        {
          id: 'about',
          title: 'من نحن',
          description: 'تعرف على مركز البدوي وتاريخنا العريق',
          keywords: ['من نحن', 'تاريخ', 'مركز البدوي', 'أصالة', 'ثقة'],
          url: '/about'
        },
        {
          id: 'contact',
          title: 'اتصل بنا',
          description: 'طرق التواصل مع مركز البدوي',
          keywords: ['اتصل بنا', 'تواصل', 'هاتف', 'عنوان', 'خدمة عملاء'],
          url: '/contact'
        },
        {
          id: 'services',
          title: 'خدماتنا',
          description: 'جميع الخدمات المتاحة في مركز البدوي',
          keywords: ['خدمات', 'توصيل', 'صيانة', 'دعم فني'],
          url: '/services'
        },
        {
          id: 'wholesale',
          title: 'أسعار الجملة',
          description: 'أسعار خاصة للتجار والموزعين',
          keywords: ['جملة', 'تجار', 'موزعين', 'أسعار خاصة', 'خصومات'],
          url: '/wholesale'
        }
      ]

      staticPages.forEach(page => {
        let relevance = 0
        
        if (page.title.toLowerCase().includes(lowerQuery)) {
          relevance += 8
        }
        
        if (page.description.toLowerCase().includes(lowerQuery)) {
          relevance += 4
        }
        
        if (page.keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery))) {
          relevance += 6
        }

        if (relevance > 0) {
          searchResults.push({
            id: page.id,
            title: page.title,
            description: page.description,
            type: 'page',
            url: page.url,
            relevance
          })
        }
      })

      // Sort by relevance
      searchResults.sort((a, b) => b.relevance - a.relevance)
      
      // Limit results
      setResults(searchResults.slice(0, 10))
      
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      // Add to recent searches
      const newRecentSearches = [
        searchQuery,
        ...recentSearches.filter(s => s !== searchQuery)
      ].slice(0, 5)
      
      setRecentSearches(newRecentSearches)
      localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches))
      
      // Perform search
      setQuery(searchQuery)
    }
  }

  const clearRecentSearches = () => {
    setRecentSearches([])
    localStorage.removeItem('recentSearches')
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'product': return <Package className="w-4 h-4" />
      case 'category': return <Tag className="w-4 h-4" />
      case 'service': return <Phone className="w-4 h-4" />
      case 'page': return <FileText className="w-4 h-4" />
      default: return <Search className="w-4 h-4" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'product': return 'منتج'
      case 'category': return 'تصنيف'
      case 'service': return 'خدمة'
      case 'page': return 'صفحة'
      default: return ''
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'product': return 'text-blue-600 bg-blue-100'
      case 'category': return 'text-green-600 bg-green-100'
      case 'service': return 'text-purple-600 bg-purple-100'
      case 'page': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center pt-20">
      <div className="bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden shadow-2xl">
        {/* Search Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                ref={searchInputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && query.trim()) {
                    handleSearch(query)
                  }
                }}
                placeholder="ابحث في المنتجات، الخدمات، والصفحات..."
                className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-lg"
              />
              {query && (
                <button
                  onClick={() => setQuery('')}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Search Content */}
        <div className="max-h-96 overflow-y-auto">
          {query.trim() === '' ? (
            /* No Query - Show Recent and Popular Searches */
            <div className="p-4 space-y-6">
              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      عمليات البحث الأخيرة
                    </h3>
                    <button
                      onClick={clearRecentSearches}
                      className="text-xs text-gray-500 hover:text-gray-700"
                    >
                      مسح الكل
                    </button>
                  </div>
                  <div className="space-y-1">
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => handleSearch(search)}
                        className="w-full text-right px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg flex items-center gap-2"
                      >
                        <Clock className="w-3 h-3 text-gray-400" />
                        {search}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Popular Searches */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  عمليات البحث الشائعة
                </h3>
                <div className="flex flex-wrap gap-2">
                  {popularSearches.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearch(search)}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                    >
                      {search}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          ) : isLoading ? (
            /* Loading */
            <div className="p-8 text-center">
              <div className="w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">جاري البحث...</p>
            </div>
          ) : results.length > 0 ? (
            /* Search Results */
            <div className="p-4">
              <p className="text-sm text-gray-600 mb-4">
                تم العثور على {results.length} نتيجة لـ "{query}"
              </p>
              <div className="space-y-2">
                {results.map((result) => (
                  <Link
                    key={`${result.type}-${result.id}`}
                    href={result.url}
                    onClick={onClose}
                    className="block p-3 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      {/* Icon */}
                      <div className={`p-2 rounded-lg ${getTypeColor(result.type)}`}>
                        {getTypeIcon(result.type)}
                      </div>
                      
                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-gray-900 truncate">{result.title}</h4>
                          <span className={`text-xs px-2 py-1 rounded-full ${getTypeColor(result.type)}`}>
                            {getTypeLabel(result.type)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-2">{result.description}</p>
                        
                        {/* Price for products/services */}
                        {(result.type === 'product' || result.type === 'service') && result.price && (
                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-sm font-medium text-primary-600">
                              {result.price} جنيه
                            </span>
                            {result.wholesalePrice && result.wholesalePrice < result.price && (
                              <span className="text-xs text-green-600">
                                (جملة: {result.wholesalePrice} جنيه)
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ) : (
            /* No Results */
            <div className="p-8 text-center">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">لم يتم العثور على نتائج لـ "{query}"</p>
              <p className="text-sm text-gray-500">جرب كلمات مختلفة أو تحقق من الإملاء</p>
            </div>
          )}
        </div>

        {/* Search Tips */}
        {query.trim() === '' && (
          <div className="p-4 bg-gray-50 border-t border-gray-200">
            <p className="text-xs text-gray-500 text-center">
              💡 نصيحة: يمكنك البحث في المنتجات، التصنيفات، الخدمات، والصفحات
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
