'use client'

import { useState } from 'react'
import { Plus, Edit, Trash2, Eye, Search, Filter, Star, Package, DollarSign } from 'lucide-react'

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  category: string
  categoryId: string
  image: string
  images: string[]
  inStock: boolean
  stockQuantity: number
  rating: number
  reviewCount: number
  features: string[]
  brand: string
  sku: string
  weight: number
  dimensions: string
  isActive: boolean
  isFeatured: boolean
  createdAt: string
  updatedAt: string
}

export default function ProductManager() {
  const [products, setProducts] = useState<Product[]>([
    {
      id: '1',
      name: 'هاتف ذكي متطور',
      description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
      price: 2500,
      originalPrice: 3000,
      category: 'الهواتف الذكية',
      categoryId: '2',
      image: '/images/phone1.jpg',
      images: ['/images/phone1.jpg', '/images/phone1-2.jpg'],
      inStock: true,
      stockQuantity: 25,
      rating: 4.8,
      reviewCount: 124,
      features: ['شاشة OLED', 'كاميرا 108MP', 'بطارية 5000mAh'],
      brand: 'سامسونج',
      sku: 'PHONE-001',
      weight: 0.2,
      dimensions: '15x7x0.8 سم',
      isActive: true,
      isFeatured: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-15'
    },
    {
      id: '2',
      name: 'لابتوب عالي الأداء',
      description: 'لابتوب للألعاب والعمل المكتبي',
      price: 4500,
      originalPrice: 5200,
      category: 'أجهزة الكمبيوتر',
      categoryId: '3',
      image: '/images/laptop1.jpg',
      images: ['/images/laptop1.jpg'],
      inStock: true,
      stockQuantity: 10,
      rating: 4.9,
      reviewCount: 89,
      features: ['معالج Intel i7', 'ذاكرة 16GB', 'SSD 512GB'],
      brand: 'ديل',
      sku: 'LAPTOP-001',
      weight: 2.5,
      dimensions: '35x25x2 سم',
      isActive: true,
      isFeatured: false,
      createdAt: '2024-01-02',
      updatedAt: '2024-01-10'
    },
    {
      id: '3',
      name: 'ساعة ذكية رياضية',
      description: 'ساعة ذكية لمتابعة اللياقة البدنية',
      price: 800,
      originalPrice: 1000,
      category: 'الإكسسوارات',
      categoryId: '4',
      image: '/images/watch1.jpg',
      images: ['/images/watch1.jpg'],
      inStock: false,
      stockQuantity: 0,
      rating: 4.7,
      reviewCount: 156,
      features: ['مقاوم للماء', 'GPS مدمج', 'بطارية 7 أيام'],
      brand: 'أبل',
      sku: 'WATCH-001',
      weight: 0.05,
      dimensions: '4x4x1 سم',
      isActive: true,
      isFeatured: true,
      createdAt: '2024-01-03',
      updatedAt: '2024-01-12'
    }
  ])

  const [showAddModal, setShowAddModal] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'instock' | 'outstock'>('all')

  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    price: 0,
    originalPrice: 0,
    categoryId: '',
    image: '',
    stockQuantity: 0,
    features: '',
    brand: '',
    sku: '',
    weight: 0,
    dimensions: '',
    isActive: true,
    isFeatured: false
  })

  const categories = [
    { id: '1', name: 'الإلكترونيات' },
    { id: '2', name: 'الهواتف الذكية' },
    { id: '3', name: 'أجهزة الكمبيوتر' },
    { id: '4', name: 'الإكسسوارات' },
    { id: '5', name: 'الأزياء والملابس' }
  ]

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = filterCategory === 'all' || product.categoryId === filterCategory

    const matchesStatus = filterStatus === 'all' ||
                         (filterStatus === 'active' && product.isActive) ||
                         (filterStatus === 'inactive' && !product.isActive) ||
                         (filterStatus === 'instock' && product.inStock) ||
                         (filterStatus === 'outstock' && !product.inStock)

    return matchesSearch && matchesCategory && matchesStatus
  })

  const handleAddProduct = () => {
    const product: Product = {
      id: Date.now().toString(),
      name: newProduct.name,
      description: newProduct.description,
      price: newProduct.price,
      originalPrice: newProduct.originalPrice || undefined,
      category: categories.find(cat => cat.id === newProduct.categoryId)?.name || '',
      categoryId: newProduct.categoryId,
      image: newProduct.image || '/images/default-product.jpg',
      images: [newProduct.image || '/images/default-product.jpg'],
      inStock: newProduct.stockQuantity > 0,
      stockQuantity: newProduct.stockQuantity,
      rating: 0,
      reviewCount: 0,
      features: newProduct.features.split(',').map(f => f.trim()).filter(f => f),
      brand: newProduct.brand,
      sku: newProduct.sku,
      weight: newProduct.weight,
      dimensions: newProduct.dimensions,
      isActive: newProduct.isActive,
      isFeatured: newProduct.isFeatured,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    }

    setProducts([...products, product])
    resetForm()
    setShowAddModal(false)
  }

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product)
    setNewProduct({
      name: product.name,
      description: product.description,
      price: product.price,
      originalPrice: product.originalPrice || 0,
      categoryId: product.categoryId,
      image: product.image,
      stockQuantity: product.stockQuantity,
      features: product.features.join(', '),
      brand: product.brand,
      sku: product.sku,
      weight: product.weight,
      dimensions: product.dimensions,
      isActive: product.isActive,
      isFeatured: product.isFeatured
    })
    setShowAddModal(true)
  }

  const handleUpdateProduct = () => {
    if (!editingProduct) return

    const updatedProducts = products.map(product =>
      product.id === editingProduct.id
        ? {
            ...product,
            name: newProduct.name,
            description: newProduct.description,
            price: newProduct.price,
            originalPrice: newProduct.originalPrice || undefined,
            category: categories.find(cat => cat.id === newProduct.categoryId)?.name || '',
            categoryId: newProduct.categoryId,
            image: newProduct.image,
            inStock: newProduct.stockQuantity > 0,
            stockQuantity: newProduct.stockQuantity,
            features: newProduct.features.split(',').map(f => f.trim()).filter(f => f),
            brand: newProduct.brand,
            sku: newProduct.sku,
            weight: newProduct.weight,
            dimensions: newProduct.dimensions,
            isActive: newProduct.isActive,
            isFeatured: newProduct.isFeatured,
            updatedAt: new Date().toISOString().split('T')[0]
          }
        : product
    )

    setProducts(updatedProducts)
    setEditingProduct(null)
    resetForm()
    setShowAddModal(false)
  }

  const handleDeleteProduct = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      setProducts(products.filter(product => product.id !== id))
    }
  }

  const toggleProductStatus = (id: string) => {
    setProducts(products.map(product =>
      product.id === id ? { ...product, isActive: !product.isActive } : product
    ))
  }

  const toggleFeaturedStatus = (id: string) => {
    setProducts(products.map(product =>
      product.id === id ? { ...product, isFeatured: !product.isFeatured } : product
    ))
  }

  const resetForm = () => {
    setNewProduct({
      name: '',
      description: '',
      price: 0,
      originalPrice: 0,
      categoryId: '',
      image: '',
      stockQuantity: 0,
      features: '',
      brand: '',
      sku: '',
      weight: 0,
      dimensions: '',
      isActive: true,
      isFeatured: false
    })
  }

  const getDiscountPercentage = (price: number, originalPrice?: number) => {
    if (!originalPrice || originalPrice <= price) return 0
    return Math.round(((originalPrice - price) / originalPrice) * 100)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة المنتجات</h2>
          <p className="text-gray-600">إدارة منتجات مركز البدوي وتحديث المعلومات</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 animate-bounce-in"
        >
          <Plus className="w-4 h-4" />
          إضافة منتج جديد
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي المنتجات</p>
              <p className="text-2xl font-bold text-gray-900">{products.length}</p>
            </div>
            <Package className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">متوفر</p>
              <p className="text-2xl font-bold text-gray-900">{products.filter(p => p.inStock).length}</p>
            </div>
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 font-bold">✓</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-red-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">غير متوفر</p>
              <p className="text-2xl font-bold text-gray-900">{products.filter(p => !p.inStock).length}</p>
            </div>
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
              <span className="text-red-600 font-bold">✗</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-yellow-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">مميز</p>
              <p className="text-2xl font-bold text-gray-900">{products.filter(p => p.isFeatured).length}</p>
            </div>
            <Star className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="البحث في المنتجات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="all">جميع التصنيفات</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="all">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
            <option value="instock">متوفر</option>
            <option value="outstock">غير متوفر</option>
          </select>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Filter className="w-4 h-4" />
            النتائج: {filteredProducts.length}
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنتج</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التصنيف</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزون</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التقييم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <Package className="w-6 h-6 text-gray-400" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-medium text-gray-900 truncate">{product.name}</p>
                          {product.isFeatured && (
                            <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          )}
                        </div>
                        <p className="text-sm text-gray-500">{product.brand} - {product.sku}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {product.features.slice(0, 2).map((feature, index) => (
                            <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">{product.category}</span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">{product.price.toLocaleString()} جنيه</div>
                      {product.originalPrice && (
                        <div className="flex items-center gap-2">
                          <span className="text-gray-500 line-through text-xs">
                            {product.originalPrice.toLocaleString()} جنيه
                          </span>
                          <span className="bg-red-100 text-red-800 text-xs px-1 rounded">
                            {getDiscountPercentage(product.price, product.originalPrice)}% خصم
                          </span>
                        </div>
                      )}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <div className={`font-medium ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>
                        {product.stockQuantity} قطعة
                      </div>
                      <div className="text-xs text-gray-500">
                        {product.inStock ? 'متوفر' : 'غير متوفر'}
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-900">{product.rating}</span>
                      <span className="text-xs text-gray-500">({product.reviewCount})</span>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col gap-1">
                      <button
                        onClick={() => toggleProductStatus(product.id)}
                        className={`text-xs px-2 py-1 rounded-full font-medium ${
                          product.isActive
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-red-100 text-red-800 hover:bg-red-200'
                        }`}
                      >
                        {product.isActive ? 'نشط' : 'غير نشط'}
                      </button>
                      <button
                        onClick={() => toggleFeaturedStatus(product.id)}
                        className={`text-xs px-2 py-1 rounded-full font-medium ${
                          product.isFeatured
                            ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        {product.isFeatured ? 'مميز' : 'عادي'}
                      </button>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <button className="text-blue-600 hover:text-blue-800 p-1">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditProduct(product)}
                        className="text-green-600 hover:text-green-800 p-1"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteProduct(product.id)}
                        className="text-red-600 hover:text-red-800 p-1"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">
                {editingProduct ? 'تعديل المنتج' : 'إضافة منتج جديد'}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">المعلومات الأساسية</h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">اسم المنتج *</label>
                    <input
                      type="text"
                      value={newProduct.name}
                      onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="أدخل اسم المنتج"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الوصف *</label>
                    <textarea
                      value={newProduct.description}
                      onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      rows={3}
                      placeholder="أدخل وصف المنتج"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">السعر الحالي *</label>
                      <input
                        type="number"
                        value={newProduct.price}
                        onChange={(e) => setNewProduct({ ...newProduct, price: Number(e.target.value) })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">السعر الأصلي</label>
                      <input
                        type="number"
                        value={newProduct.originalPrice}
                        onChange={(e) => setNewProduct({ ...newProduct, originalPrice: Number(e.target.value) })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">التصنيف *</label>
                    <select
                      value={newProduct.categoryId}
                      onChange={(e) => setNewProduct({ ...newProduct, categoryId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر التصنيف</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>{category.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">رابط الصورة</label>
                    <input
                      type="url"
                      value={newProduct.image}
                      onChange={(e) => setNewProduct({ ...newProduct, image: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                </div>

                {/* Additional Information */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">معلومات إضافية</h4>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">العلامة التجارية</label>
                      <input
                        type="text"
                        value={newProduct.brand}
                        onChange={(e) => setNewProduct({ ...newProduct, brand: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="اسم العلامة التجارية"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">رمز المنتج (SKU)</label>
                      <input
                        type="text"
                        value={newProduct.sku}
                        onChange={(e) => setNewProduct({ ...newProduct, sku: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="PROD-001"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">كمية المخزون *</label>
                    <input
                      type="number"
                      value={newProduct.stockQuantity}
                      onChange={(e) => setNewProduct({ ...newProduct, stockQuantity: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المميزات (مفصولة بفاصلة)</label>
                    <textarea
                      value={newProduct.features}
                      onChange={(e) => setNewProduct({ ...newProduct, features: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      rows={2}
                      placeholder="مميزة 1, مميزة 2, مميزة 3"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">الوزن (كجم)</label>
                      <input
                        type="number"
                        step="0.1"
                        value={newProduct.weight}
                        onChange={(e) => setNewProduct({ ...newProduct, weight: Number(e.target.value) })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="0.0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">الأبعاد</label>
                      <input
                        type="text"
                        value={newProduct.dimensions}
                        onChange={(e) => setNewProduct({ ...newProduct, dimensions: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="الطول x العرض x الارتفاع"
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isActive"
                        checked={newProduct.isActive}
                        onChange={(e) => setNewProduct({ ...newProduct, isActive: e.target.checked })}
                        className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                      />
                      <label htmlFor="isActive" className="mr-2 text-sm text-gray-700">
                        منتج نشط
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isFeatured"
                        checked={newProduct.isFeatured}
                        onChange={(e) => setNewProduct({ ...newProduct, isFeatured: e.target.checked })}
                        className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                      />
                      <label htmlFor="isFeatured" className="mr-2 text-sm text-gray-700">
                        منتج مميز
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3 mt-6 pt-4 border-t">
                <button
                  onClick={editingProduct ? handleUpdateProduct : handleAddProduct}
                  className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg font-medium"
                >
                  {editingProduct ? 'تحديث المنتج' : 'إضافة المنتج'}
                </button>
                <button
                  onClick={() => {
                    setShowAddModal(false)
                    setEditingProduct(null)
                    resetForm()
                  }}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded-lg font-medium"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}