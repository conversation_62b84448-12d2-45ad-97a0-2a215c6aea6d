'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Package, Clock, CheckCircle, XCircle, Eye, Truck } from 'lucide-react'
import Link from 'next/link'

interface Order {
  id: string
  orderNumber: string
  date: string
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
  total: number
  items: Array<{
    id: string
    name: string
    quantity: number
    price: number
    image: string
  }>
  shippingAddress: string
  estimatedDelivery?: string
}

// Sample orders data
const sampleOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-2024-001',
    date: '2024-01-15',
    status: 'delivered',
    total: 2850,
    items: [
      { id: '1', name: 'هاتف ذكي متطور', quantity: 1, price: 2500, image: '/images/phone.jpg' },
      { id: '2', name: 'سماعات لاسلكية', quantity: 1, price: 350, image: '/images/headphones.jpg' }
    ],
    shippingAddress: 'الرياض، حي النرجس، شارع الأمير محمد بن عبدالعزيز',
    estimatedDelivery: '2024-01-17'
  },
  {
    id: '2',
    orderNumber: 'ORD-2024-002',
    date: '2024-01-20',
    status: 'shipped',
    total: 800,
    items: [
      { id: '3', name: 'ساعة ذكية رياضية', quantity: 1, price: 800, image: '/images/watch.jpg' }
    ],
    shippingAddress: 'جدة، حي الزهراء، شارع الملك عبدالعزيز',
    estimatedDelivery: '2024-01-22'
  },
  {
    id: '3',
    orderNumber: 'ORD-2024-003',
    date: '2024-01-25',
    status: 'confirmed',
    total: 4500,
    items: [
      { id: '2', name: 'لابتوب عالي الأداء', quantity: 1, price: 4500, image: '/images/laptop.jpg' }
    ],
    shippingAddress: 'الدمام، حي الفيصلية، شارع الخليج العربي'
  }
]

export default function OrdersPage() {
  const { user } = useAuth()
  const [orders, setOrders] = useState<Order[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  useEffect(() => {
    if (user) {
      setOrders(sampleOrders)
    }
  }, [user])

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />
      case 'confirmed':
        return <CheckCircle className="w-5 h-5 text-blue-500" />
      case 'shipped':
        return <Truck className="w-5 h-5 text-purple-500" />
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />
      default:
        return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'confirmed':
        return 'تم التأكيد'
      case 'shipped':
        return 'في الطريق'
      case 'delivered':
        return 'تم التسليم'
      case 'cancelled':
        return 'ملغي'
      default:
        return 'غير معروف'
    }
  }

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'confirmed':
        return 'bg-blue-100 text-blue-800'
      case 'shipped':
        return 'bg-purple-100 text-purple-800'
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredOrders = selectedStatus === 'all' 
    ? orders 
    : orders.filter(order => order.status === selectedStatus)

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <Package className="w-24 h-24 text-gray-300 mx-auto mb-6" />
        <h1 className="text-2xl font-bold text-gray-800 mb-4">يرجى تسجيل الدخول</h1>
        <p className="text-gray-600 mb-8">تحتاج إلى تسجيل الدخول لعرض طلباتك</p>
        <Link
          href="/"
          className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-8 rounded-lg transition-colors duration-200"
        >
          تسجيل الدخول
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">طلباتي</h1>
        <p className="text-gray-600">تتبع جميع طلباتك وحالتها الحالية</p>
      </div>

      {/* Status Filter */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">فلترة حسب الحالة</h2>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedStatus('all')}
            className={`px-4 py-2 rounded-lg transition-colors duration-200 ${
              selectedStatus === 'all'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            جميع الطلبات ({orders.length})
          </button>
          {['pending', 'confirmed', 'shipped', 'delivered', 'cancelled'].map((status) => {
            const count = orders.filter(order => order.status === status).length
            return (
              <button
                key={status}
                onClick={() => setSelectedStatus(status)}
                className={`px-4 py-2 rounded-lg transition-colors duration-200 ${
                  selectedStatus === status
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {getStatusText(status as Order['status'])} ({count})
              </button>
            )
          })}
        </div>
      </div>

      {/* Orders List */}
      {filteredOrders.length === 0 ? (
        <div className="text-center py-12">
          <Package className="w-24 h-24 text-gray-300 mx-auto mb-6" />
          <h2 className="text-xl font-bold text-gray-800 mb-4">لا توجد طلبات</h2>
          <p className="text-gray-600 mb-8">لم تقم بأي طلبات بعد</p>
          <Link
            href="/products"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-8 rounded-lg transition-colors duration-200"
          >
            تصفح المنتجات
          </Link>
        </div>
      ) : (
        <div className="space-y-6">
          {filteredOrders.map((order) => (
            <div key={order.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Order Header */}
              <div className="bg-gray-50 px-6 py-4 border-b">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="flex items-center gap-4 mb-2 md:mb-0">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(order.status)}
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                        {getStatusText(order.status)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      طلب رقم: <span className="font-medium">{order.orderNumber}</span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    تاريخ الطلب: {new Date(order.date).toLocaleDateString('ar-SA')}
                  </div>
                </div>
              </div>

              {/* Order Content */}
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Order Items */}
                  <div className="lg:col-span-2">
                    <h3 className="font-semibold text-gray-800 mb-4">المنتجات</h3>
                    <div className="space-y-3">
                      {order.items.map((item) => (
                        <div key={item.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                          <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span className="text-gray-500 text-xs">صورة</span>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-800">{item.name}</h4>
                            <p className="text-sm text-gray-600">الكمية: {item.quantity}</p>
                          </div>
                          <div className="text-left">
                            <p className="font-bold text-gray-800">{item.price} ريال</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Order Summary */}
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-4">تفاصيل الطلب</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">المجموع الكلي</span>
                        <span className="font-bold text-primary-600">{order.total} ريال</span>
                      </div>
                      
                      <div className="border-t pt-3">
                        <h4 className="font-medium text-gray-800 mb-2">عنوان التوصيل</h4>
                        <p className="text-sm text-gray-600">{order.shippingAddress}</p>
                      </div>

                      {order.estimatedDelivery && (
                        <div className="border-t pt-3">
                          <h4 className="font-medium text-gray-800 mb-2">التوصيل المتوقع</h4>
                          <p className="text-sm text-gray-600">
                            {new Date(order.estimatedDelivery).toLocaleDateString('ar-SA')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Order Actions */}
                <div className="mt-6 pt-6 border-t flex flex-col sm:flex-row gap-3">
                  <button className="flex items-center justify-center gap-2 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-lg transition-colors duration-200">
                    <Eye size={16} />
                    تفاصيل الطلب
                  </button>
                  
                  {order.status === 'shipped' && (
                    <button className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors duration-200">
                      <Truck size={16} />
                      تتبع الشحنة
                    </button>
                  )}
                  
                  {order.status === 'pending' && (
                    <button className="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors duration-200">
                      <XCircle size={16} />
                      إلغاء الطلب
                    </button>
                  )}
                  
                  {order.status === 'delivered' && (
                    <button className="flex items-center justify-center gap-2 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors duration-200">
                      إعادة الطلب
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
