🚀 كيفية تثبيت Node.js وتشغيل موقع كوبرا

========================================
📥 الطريقة الأولى: التحميل المباشر
========================================

1. اذهب إلى الموقع الرسمي:
   https://nodejs.org/

2. اضغط على زر "Download Node.js (LTS)"
   (النسخة الموصى بها)

3. حمل الملف وشغله
   (node-v18.x.x-x64.msi)

4. اتبع خطوات التثبيت:
   ✅ اضغط Next
   ✅ اقبل الشروط
   ✅ اختر مجلد التثبيت
   ✅ اضغط Install

5. أعد تشغيل الكمبيوتر

========================================
🔧 الطريقة الثانية: Chocolatey
========================================

1. افتح PowerShell كمدير (Run as Administrator)

2. ثبت Chocolatey:
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

3. ثبت Node.js:
choco install nodejs

========================================
⚡ الطريقة الثالثة: Winget
========================================

1. افتح Command Prompt أو PowerShell

2. اكتب:
winget install OpenJS.NodeJS

========================================
✅ التحقق من التثبيت
========================================

1. افتح Command Prompt جديد

2. اكتب:
node --version

3. يجب أن تظهر رسالة مثل:
v18.18.0

4. اكتب:
npm --version

5. يجب أن تظهر رسالة مثل:
9.8.1

========================================
🚀 تشغيل الموقع بعد تثبيت Node.js
========================================

1. افتح Command Prompt في مجلد المشروع:
cd "C:\Users\<USER>\Documents\augment-projects\Cobra"

2. ثبت المكتبات:
npm install

3. شغل الموقع:
npm run dev

4. افتح المتصفح على:
http://localhost:3000

========================================
🔧 حل مشاكل التثبيت
========================================

مشكلة: "npm is not recognized"
الحل: أعد تشغيل Command Prompt أو الكمبيوتر

مشكلة: "execution policy"
الحل: افتح PowerShell كمدير واكتب:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

مشكلة: بطء التحميل
الحل: استخدم VPN أو غير DNS إلى *******

========================================
📞 إذا واجهت مشاكل
========================================

تواصل معنا:
📧 <EMAIL>
📱 +966 50 123 4567

أو راجع الملفات:
📄 README.md
📄 QUICK_START.md
📄 DEVELOPER_GUIDE.md
