'use client'

import { useState, useRef, useEffect } from 'react'
import { Search, Mic, Camera, X } from 'lucide-react'
import GlobalSearch from './GlobalSearch'

interface SearchBarProps {
  className?: string
  placeholder?: string
  showVoiceSearch?: boolean
  showImageSearch?: boolean
}

export default function SearchBar({ 
  className = '',
  placeholder = 'ابحث في المنتجات والخدمات...',
  showVoiceSearch = true,
  showImageSearch = false
}: SearchBarProps) {
  const [isGlobalSearchOpen, setIsGlobalSearchOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [isFocused, setIsFocused] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // Voice Search functionality
  const startVoiceSearch = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition
      const recognition = new SpeechRecognition()
      
      recognition.lang = 'ar-EG' // Arabic (Egypt)
      recognition.continuous = false
      recognition.interimResults = false

      recognition.onstart = () => {
        setIsListening(true)
      }

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript
        setQuery(transcript)
        setIsListening(false)
        
        // Auto-open global search with voice result
        setTimeout(() => {
          setIsGlobalSearchOpen(true)
        }, 100)
      }

      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error)
        setIsListening(false)
        alert('حدث خطأ في البحث الصوتي. تأكد من السماح بالوصول للميكروفون.')
      }

      recognition.onend = () => {
        setIsListening(false)
      }

      recognition.start()
    } else {
      alert('البحث الصوتي غير مدعوم في هذا المتصفح')
    }
  }

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K to open search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        setIsGlobalSearchOpen(true)
      }
      
      // Escape to close search
      if (e.key === 'Escape') {
        setIsGlobalSearchOpen(false)
        inputRef.current?.blur()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const handleInputClick = () => {
    setIsGlobalSearchOpen(true)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (query.trim()) {
      setIsGlobalSearchOpen(true)
    }
  }

  const clearSearch = () => {
    setQuery('')
    inputRef.current?.focus()
  }

  return (
    <>
      <div className={`relative ${className}`}>
        <form onSubmit={handleSubmit} className="relative">
          {/* Search Input */}
          <div className={`relative flex items-center bg-white border-2 rounded-lg transition-all duration-200 ${
            isFocused ? 'border-primary-500 shadow-lg' : 'border-gray-200 hover:border-gray-300'
          }`}>
            {/* Search Icon */}
            <div className="absolute right-3 flex items-center">
              <Search className={`w-5 h-5 transition-colors ${
                isFocused ? 'text-primary-600' : 'text-gray-400'
              }`} />
            </div>

            {/* Input Field */}
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={handleInputChange}
              onClick={handleInputClick}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder={placeholder}
              className="w-full pr-12 pl-4 py-3 bg-transparent focus:outline-none text-gray-900 placeholder-gray-500"
            />

            {/* Action Buttons */}
            <div className="absolute left-2 flex items-center gap-1">
              {/* Clear Button */}
              {query && (
                <button
                  type="button"
                  onClick={clearSearch}
                  className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              )}

              {/* Voice Search Button */}
              {showVoiceSearch && (
                <button
                  type="button"
                  onClick={startVoiceSearch}
                  disabled={isListening}
                  className={`p-2 rounded-full transition-colors ${
                    isListening 
                      ? 'bg-red-100 text-red-600 animate-pulse' 
                      : 'text-gray-400 hover:text-primary-600 hover:bg-primary-50'
                  }`}
                  title="البحث الصوتي"
                >
                  <Mic className="w-4 h-4" />
                </button>
              )}

              {/* Image Search Button */}
              {showImageSearch && (
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors"
                  title="البحث بالصورة"
                >
                  <Camera className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>

          {/* Search Suggestions Indicator */}
          {isFocused && !isGlobalSearchOpen && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-10">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>اضغط للبحث الشامل</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl + K</kbd>
              </div>
            </div>
          )}
        </form>

        {/* Voice Search Indicator */}
        {isListening && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-red-50 border border-red-200 rounded-lg p-3 z-10">
            <div className="flex items-center gap-2 text-red-700">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm">جاري الاستماع... تحدث الآن</span>
            </div>
          </div>
        )}
      </div>

      {/* Global Search Modal */}
      <GlobalSearch 
        isOpen={isGlobalSearchOpen}
        onClose={() => setIsGlobalSearchOpen(false)}
      />
    </>
  )
}
