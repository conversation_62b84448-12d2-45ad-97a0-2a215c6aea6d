'use client'

import { useState, useEffect } from 'react'
import { Building, Mail, Phone, MapPin, Check, X, Eye, Clock, AlertCircle, Copy, Send, RefreshCw } from 'lucide-react'
import { db, WholesaleRequest } from '@/lib/database'

export default function WholesaleRequestsManager() {
  const [requests, setRequests] = useState<WholesaleRequest[]>([])
  const [selectedRequest, setSelectedRequest] = useState<WholesaleRequest | null>(null)
  const [showModal, setShowModal] = useState(false)
  const [verificationCode, setVerificationCode] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all')

  useEffect(() => {
    loadRequests()
  }, [])

  const loadRequests = () => {
    const allRequests = db.getAllWholesaleRequests()
    setRequests(allRequests.sort((a, b) => new Date(b.requestDate).getTime() - new Date(a.requestDate).getTime()))
  }

  const filteredRequests = requests.filter(request => {
    if (filterStatus === 'all') return true
    return request.status === filterStatus
  })

  const generateVerificationCode = (): string => {
    return Math.floor(100000 + Math.random() * 900000).toString()
  }

  const handleApprove = (request: WholesaleRequest) => {
    const code = generateVerificationCode()
    setVerificationCode(code)
    setSelectedRequest(request)
    setShowModal(true)
  }

  const confirmApproval = () => {
    if (!selectedRequest || !verificationCode) return

    const success = db.approveWholesaleRequest(selectedRequest.id, verificationCode)
    if (success) {
      loadRequests()
      setShowModal(false)
      setSelectedRequest(null)
      setVerificationCode('')
      
      // Simulate sending verification code to customer
      alert(`تم إرسال كود التفعيل ${verificationCode} إلى العميل ${selectedRequest.customerName}`)
    }
  }

  const handleReject = (request: WholesaleRequest) => {
    if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
      // Update request status to rejected
      const requests = db.getAllWholesaleRequests()
      const index = requests.findIndex(r => r.id === request.id)
      if (index !== -1) {
        requests[index] = { ...requests[index], status: 'rejected' }
        localStorage.setItem('albadawi_wholesale_requests', JSON.stringify(requests))
        loadRequests()
      }
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert('تم نسخ النص بنجاح!')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'approved': return 'bg-green-100 text-green-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار'
      case 'approved': return 'مُفعل'
      case 'rejected': return 'مرفوض'
      default: return status
    }
  }

  const pendingCount = requests.filter(r => r.status === 'pending').length
  const approvedCount = requests.filter(r => r.status === 'approved').length
  const rejectedCount = requests.filter(r => r.status === 'rejected').length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة طلبات حسابات الجملة</h2>
          <p className="text-gray-600">مراجعة وتفعيل طلبات التجار والموزعين</p>
        </div>
        <div className="flex items-center gap-2">
          {pendingCount > 0 && (
            <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
              <Clock className="w-4 h-4" />
              {pendingCount} طلب جديد
            </div>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الطلبات</p>
              <p className="text-2xl font-bold text-gray-900">{requests.length}</p>
            </div>
            <Building className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-yellow-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">في الانتظار</p>
              <p className="text-2xl font-bold text-gray-900">{pendingCount}</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">مُفعل</p>
              <p className="text-2xl font-bold text-gray-900">{approvedCount}</p>
            </div>
            <Check className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-red-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">مرفوض</p>
              <p className="text-2xl font-bold text-gray-900">{rejectedCount}</p>
            </div>
            <X className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex items-center gap-4">
          <span className="text-sm font-medium text-gray-700">فلترة حسب الحالة:</span>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="all">جميع الطلبات</option>
            <option value="pending">في الانتظار</option>
            <option value="approved">مُفعل</option>
            <option value="rejected">مرفوض</option>
          </select>
        </div>
      </div>

      {/* Requests List */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">العميل</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">النشاط التجاري</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الطلب</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredRequests.map((request) => (
                <tr key={request.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 font-bold text-sm">
                          {request.customerName.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900">{request.customerName}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <Mail className="w-3 h-3" />
                            {request.customerEmail}
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">{request.businessName}</div>
                      <div className="text-gray-500">{request.businessType}</div>
                      {request.taxNumber && (
                        <div className="text-xs text-gray-400">ض.ر: {request.taxNumber}</div>
                      )}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {new Date(request.requestDate).toLocaleDateString('ar-EG')}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(request.requestDate).toLocaleTimeString('ar-EG')}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                      {getStatusLabel(request.status)}
                    </span>
                    {request.status === 'approved' && request.verificationCode && (
                      <div className="mt-1 text-xs text-gray-500">
                        كود: {request.verificationCode}
                      </div>
                    )}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <button className="text-blue-600 hover:text-blue-800 p-1">
                        <Eye className="w-4 h-4" />
                      </button>
                      
                      {request.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleApprove(request)}
                            className="text-green-600 hover:text-green-800 p-1"
                            title="تفعيل الطلب"
                          >
                            <Check className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleReject(request)}
                            className="text-red-600 hover:text-red-800 p-1"
                            title="رفض الطلب"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </>
                      )}
                      
                      {request.status === 'approved' && request.verificationCode && (
                        <button
                          onClick={() => copyToClipboard(request.verificationCode!)}
                          className="text-purple-600 hover:text-purple-800 p-1"
                          title="نسخ كود التفعيل"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredRequests.length === 0 && (
          <div className="text-center py-12">
            <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">لا توجد طلبات {filterStatus !== 'all' ? getStatusLabel(filterStatus) : ''}</p>
          </div>
        )}
      </div>

      {/* Approval Modal */}
      {showModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">تفعيل حساب الجملة</h3>
            
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">بيانات العميل</h4>
                <div className="space-y-1 text-sm text-blue-800">
                  <p><span className="font-medium">الاسم:</span> {selectedRequest.customerName}</p>
                  <p><span className="font-medium">البريد:</span> {selectedRequest.customerEmail}</p>
                  <p><span className="font-medium">النشاط:</span> {selectedRequest.businessName}</p>
                  <p><span className="font-medium">النوع:</span> {selectedRequest.businessType}</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  كود التفعيل
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 font-mono text-center"
                    placeholder="000000"
                  />
                  <button
                    onClick={() => setVerificationCode(generateVerificationCode())}
                    className="px-3 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200"
                    title="إنشاء كود جديد"
                  >
                    <RefreshCw className="w-4 h-4" />
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  سيتم إرسال هذا الكود للعميل لتفعيل حسابه
                </p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">تأكد من صحة البيانات قبل التفعيل</p>
                    <p>سيحصل العميل على أسعار الجملة فور التفعيل</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={confirmApproval}
                disabled={!verificationCode}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white py-2 rounded-lg flex items-center justify-center gap-2"
              >
                <Check className="w-4 h-4" />
                تفعيل الحساب
              </button>
              <button
                onClick={() => {
                  setShowModal(false)
                  setSelectedRequest(null)
                  setVerificationCode('')
                }}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded-lg"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
