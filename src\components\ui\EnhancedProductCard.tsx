'use client'

import { useState } from 'react'
import { Star, ShoppingCart, Heart, Eye, Share2, Zap } from 'lucide-react'
import { useCart } from '@/contexts/CartContext'
import { useToast } from '@/hooks/useToast'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  rating: number
  reviewCount: number
  category: string
  inStock: boolean
  features?: string[]
  discount?: number
  isNew?: boolean
  isBestSeller?: boolean
}

interface EnhancedProductCardProps {
  product: Product
  className?: string
}

export default function EnhancedProductCard({ product, className = '' }: EnhancedProductCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const { addItem } = useCart()
  const { success } = useToast()

  const handleAddToCart = () => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    })
    success('تم إضافة المنتج للسلة', `تم إضافة ${product.name} بنجاح`)
  }

  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : product.discount || 0

  return (
    <div 
      className={`
        relative bg-white rounded-xl shadow-md overflow-hidden
        transform transition-all duration-300 hover:scale-105 hover:shadow-xl
        border border-gray-200 hover:border-primary-300
        ${className}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Badges */}
      <div className="absolute top-3 right-3 z-10 flex flex-col gap-2">
        {product.isNew && (
          <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-bold animate-pulse">
            جديد
          </span>
        )}
        {product.isBestSeller && (
          <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-bold flex items-center gap-1">
            <Zap className="w-3 h-3" />
            الأكثر مبيعاً
          </span>
        )}
        {discountPercentage > 0 && (
          <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold animate-bounce">
            خصم {discountPercentage}%
          </span>
        )}
      </div>

      {/* Action Buttons */}
      <div className={`
        absolute top-3 left-3 z-10 flex flex-col gap-2
        transform transition-all duration-300
        ${isHovered ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}>
        <button
          onClick={() => setIsLiked(!isLiked)}
          className={`
            w-8 h-8 rounded-full flex items-center justify-center
            transition-all duration-300 backdrop-blur-sm
            ${isLiked 
              ? 'bg-red-500 text-white' 
              : 'bg-white/80 text-gray-600 hover:bg-red-500 hover:text-white'
            }
          `}
        >
          <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
        </button>
        
        <button className="w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 hover:bg-primary-500 hover:text-white transition-all duration-300">
          <Eye className="w-4 h-4" />
        </button>
        
        <button className="w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 hover:bg-blue-500 hover:text-white transition-all duration-300">
          <Share2 className="w-4 h-4" />
        </button>
      </div>

      {/* Product Image */}
      <div className="relative h-48 bg-gray-100 overflow-hidden">
        <div className="w-full h-full flex items-center justify-center text-gray-400 text-4xl">
          📱
        </div>
        
        {/* Shimmer Effect */}
        <div className={`
          absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent
          transform transition-transform duration-1000
          ${isHovered ? 'translate-x-full' : '-translate-x-full'}
        `} />
        
        {/* Stock Status */}
        {!product.inStock && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
              غير متوفر
            </span>
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        {/* Category */}
        <div className="text-xs text-primary-600 font-medium mb-1">
          {product.category}
        </div>

        {/* Product Name */}
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-primary-600 transition-colors">
          {product.name}
        </h3>

        {/* Rating */}
        <div className="flex items-center gap-2 mb-3">
          <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star
                key={star}
                className={`w-4 h-4 ${
                  star <= product.rating 
                    ? 'text-yellow-400 fill-current' 
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-sm text-gray-600">
            ({product.reviewCount.toLocaleString('ar-SA')})
          </span>
        </div>

        {/* Features */}
        {product.features && product.features.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {product.features.slice(0, 2).map((feature, index) => (
                <span
                  key={index}
                  className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                >
                  {feature}
                </span>
              ))}
              {product.features.length > 2 && (
                <span className="text-xs text-gray-500">
                  +{product.features.length - 2} المزيد
                </span>
              )}
            </div>
          </div>
        )}

        {/* Price */}
        <div className="flex items-center gap-2 mb-4">
          <span className="text-lg font-bold text-primary-600">
            {product.price.toLocaleString('ar-SA')} جنيه
          </span>
          {product.originalPrice && (
            <span className="text-sm text-gray-500 line-through">
              {product.originalPrice.toLocaleString('ar-SA')} جنيه
            </span>
          )}
        </div>

        {/* Add to Cart Button */}
        <button
          onClick={handleAddToCart}
          disabled={!product.inStock}
          className={`
            w-full py-2 px-4 rounded-lg font-medium transition-all duration-300
            flex items-center justify-center gap-2
            ${product.inStock
              ? 'bg-primary-600 hover:bg-primary-700 text-white hover:shadow-lg transform hover:-translate-y-0.5'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }
          `}
        >
          <ShoppingCart className="w-4 h-4" />
          {product.inStock ? 'أضف للسلة' : 'غير متوفر'}
        </button>
      </div>

      {/* Hover Overlay */}
      <div className={`
        absolute inset-0 bg-gradient-to-t from-primary-600/10 to-transparent
        transition-opacity duration-300 pointer-events-none
        ${isHovered ? 'opacity-100' : 'opacity-0'}
      `} />

      {/* Special Offer Banner */}
      {discountPercentage >= 20 && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-r from-red-500 to-pink-500 text-white text-center py-1">
          <span className="text-xs font-bold animate-pulse">
            🔥 عرض خاص - وفر {discountPercentage}%
          </span>
        </div>
      )}
    </div>
  )
}
