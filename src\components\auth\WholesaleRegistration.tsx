'use client'

import { useState } from 'react'
import { Building, Mail, Phone, MapPin, FileText, Key, CheckCircle, AlertCircle } from 'lucide-react'
import { db } from '@/lib/database'

interface WholesaleFormData {
  customerName: string
  customerEmail: string
  customerPhone: string
  businessName: string
  businessType: string
  taxNumber: string
  address: string
  city: string
  governorate: string
  verificationCode: string
}

interface WholesaleRegistrationProps {
  onClose: () => void
  onSuccess: () => void
}

export default function WholesaleRegistration({ onClose, onSuccess }: WholesaleRegistrationProps) {
  const [step, setStep] = useState<'form' | 'verification' | 'success'>('form')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  
  const [formData, setFormData] = useState<WholesaleFormData>({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    businessName: '',
    businessType: '',
    taxNumber: '',
    address: '',
    city: '',
    governorate: '',
    verificationCode: ''
  })

  const businessTypes = [
    'متجر تجزئة',
    'سوبر ماركت',
    'مطعم أو كافيه',
    'شركة توزيع',
    'متجر إلكتروني',
    'مكتب أو شركة',
    'أخرى'
  ]

  const egyptianGovernorates = [
    'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
    'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
    'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
    'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
    'قنا', 'شمال سيناء', 'سوهاج'
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      // Validate form
      if (!formData.customerName || !formData.customerEmail || !formData.businessName) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة')
      }

      // Check if email already exists
      const existingCustomer = db.getCustomerByEmail(formData.customerEmail)
      if (existingCustomer) {
        throw new Error('هذا البريد الإلكتروني مسجل مسبقاً')
      }

      // Create customer account first
      const customer = db.addCustomer({
        name: formData.customerName,
        email: formData.customerEmail,
        phone: formData.customerPhone,
        address: formData.address,
        city: formData.city,
        governorate: formData.governorate,
        userType: 'retail', // Will be updated to wholesale after verification
        isWholesaleVerified: false,
        isActive: true
      })

      // Submit wholesale request
      const request = db.addWholesaleRequest({
        customerId: customer.id,
        customerName: formData.customerName,
        customerEmail: formData.customerEmail,
        businessName: formData.businessName,
        businessType: formData.businessType,
        taxNumber: formData.taxNumber || undefined
      })

      console.log('📧 تم إرسال طلب تفعيل حساب الجملة:', request)
      
      setStep('verification')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      if (!formData.verificationCode) {
        throw new Error('يرجى إدخال كود التفعيل')
      }

      // Find the pending request
      const requests = db.getAllWholesaleRequests()
      const pendingRequest = requests.find(r => 
        r.customerEmail === formData.customerEmail && r.status === 'pending'
      )

      if (!pendingRequest) {
        throw new Error('لم يتم العثور على طلب التفعيل')
      }

      // In a real application, you would verify the code with the server
      // For demo purposes, we'll accept any 6-digit code
      if (formData.verificationCode.length !== 6) {
        throw new Error('كود التفعيل يجب أن يكون 6 أرقام')
      }

      // Approve the request
      const success = db.approveWholesaleRequest(pendingRequest.id, formData.verificationCode)
      
      if (success) {
        setStep('success')
        setTimeout(() => {
          onSuccess()
        }, 2000)
      } else {
        throw new Error('فشل في تفعيل الحساب')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في التفعيل')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">تسجيل حساب جملة</h2>
              <p className="text-gray-600">احصل على أسعار خاصة للتجار والموزعين</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step === 'form' ? 'bg-primary-600 text-white' : 'bg-green-600 text-white'
              }`}>
                1
              </div>
              <div className="w-16 h-1 bg-gray-200 mx-2">
                <div className={`h-full bg-primary-600 transition-all duration-300 ${
                  step !== 'form' ? 'w-full' : 'w-0'
                }`}></div>
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step === 'verification' ? 'bg-primary-600 text-white' : 
                step === 'success' ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'
              }`}>
                2
              </div>
              <div className="w-16 h-1 bg-gray-200 mx-2">
                <div className={`h-full bg-primary-600 transition-all duration-300 ${
                  step === 'success' ? 'w-full' : 'w-0'
                }`}></div>
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step === 'success' ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'
              }`}>
                3
              </div>
            </div>
          </div>

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <span className="text-red-800">{error}</span>
            </div>
          )}

          {/* Step 1: Registration Form */}
          {step === 'form' && (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الاسم الكامل *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.customerName}
                    onChange={(e) => setFormData({...formData, customerName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="أدخل اسمك الكامل"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    البريد الإلكتروني *
                  </label>
                  <input
                    type="email"
                    required
                    value={formData.customerEmail}
                    onChange={(e) => setFormData({...formData, customerEmail: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم الهاتف *
                  </label>
                  <input
                    type="tel"
                    required
                    value={formData.customerPhone}
                    onChange={(e) => setFormData({...formData, customerPhone: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="+20 ************"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم النشاط التجاري *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.businessName}
                    onChange={(e) => setFormData({...formData, businessName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="اسم المتجر أو الشركة"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    نوع النشاط التجاري *
                  </label>
                  <select
                    required
                    value={formData.businessType}
                    onChange={(e) => setFormData({...formData, businessType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">اختر نوع النشاط</option>
                    {businessTypes.map((type) => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الرقم الضريبي (اختياري)
                  </label>
                  <input
                    type="text"
                    value={formData.taxNumber}
                    onChange={(e) => setFormData({...formData, taxNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="رقم التسجيل الضريبي"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    العنوان *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.address}
                    onChange={(e) => setFormData({...formData, address: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="العنوان التفصيلي"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    المدينة *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.city}
                    onChange={(e) => setFormData({...formData, city: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="اسم المدينة"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    المحافظة *
                  </label>
                  <select
                    required
                    value={formData.governorate}
                    onChange={(e) => setFormData({...formData, governorate: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">اختر المحافظة</option>
                    {egyptianGovernorates.map((gov) => (
                      <option key={gov} value={gov}>{gov}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Building className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900">مميزات حساب الجملة</h4>
                    <ul className="text-sm text-blue-800 mt-2 space-y-1">
                      <li>• أسعار خاصة للتجار والموزعين</li>
                      <li>• خصومات على الكميات الكبيرة</li>
                      <li>• دعم فني مخصص</li>
                      <li>• شروط دفع مرنة</li>
                    </ul>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white py-3 rounded-lg font-medium flex items-center justify-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    جاري الإرسال...
                  </>
                ) : (
                  <>
                    <Mail className="w-4 h-4" />
                    إرسال طلب التفعيل
                  </>
                )}
              </button>
            </form>
          )}

          {/* Step 2: Verification */}
          {step === 'verification' && (
            <div className="text-center space-y-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <Mail className="w-8 h-8 text-blue-600" />
              </div>
              
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">تم إرسال طلبك بنجاح!</h3>
                <p className="text-gray-600">
                  تم إرسال طلب تفعيل حساب الجملة إلى إدارة مركز البدوي.
                  <br />
                  ستتلقى كود التفعيل خلال 24 ساعة على البريد الإلكتروني أو الهاتف.
                </p>
              </div>

              <form onSubmit={handleVerification} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    كود التفعيل
                  </label>
                  <input
                    type="text"
                    maxLength={6}
                    value={formData.verificationCode}
                    onChange={(e) => setFormData({...formData, verificationCode: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-center text-lg font-mono"
                    placeholder="000000"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    أدخل الكود المكون من 6 أرقام الذي تلقيته من إدارة مركز البدوي
                  </p>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting || !formData.verificationCode}
                  className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white py-3 rounded-lg font-medium flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      جاري التفعيل...
                    </>
                  ) : (
                    <>
                      <Key className="w-4 h-4" />
                      تفعيل الحساب
                    </>
                  )}
                </button>
              </form>

              <div className="text-sm text-gray-500">
                لم تتلقى الكود؟{' '}
                <button className="text-primary-600 hover:text-primary-700 font-medium">
                  اتصل بنا على {'+20 ************'}
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Success */}
          {step === 'success' && (
            <div className="text-center space-y-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">تم تفعيل حسابك بنجاح!</h3>
                <p className="text-gray-600">
                  مبروك! تم تفعيل حساب الجملة الخاص بك.
                  <br />
                  يمكنك الآن الاستفادة من الأسعار الخاصة والخصومات.
                </p>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">ما التالي؟</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• ستظهر لك الأسعار الخاصة عند تسجيل الدخول</li>
                  <li>• يمكنك طلب كميات كبيرة بخصومات إضافية</li>
                  <li>• ستحصل على دعم فني مخصص</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
