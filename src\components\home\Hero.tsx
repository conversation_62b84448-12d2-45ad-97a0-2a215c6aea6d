'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Search, ShoppingBag, Truck, Shield, Clock } from 'lucide-react'

export default function Hero() {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/products?search=${encodeURIComponent(searchQuery)}`
    }
  }

  return (
    <section className="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white">
      <div className="container mx-auto px-4 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-right">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
              مرحباً بكم في
              <span className="block text-yellow-300">متجر كوبرا</span>
            </h1>
            <p className="text-xl mb-8 text-blue-100">
              أفضل المنتجات والخدمات مع توصيل سريع وآمن إلى باب منزلك
            </p>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="mb-8">
              <div className="flex max-w-md mx-auto lg:mx-0">
                <input
                  type="text"
                  placeholder="ابحث عن المنتجات والخدمات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 px-4 py-3 rounded-r-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-yellow-300"
                />
                <button
                  type="submit"
                  className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 px-6 py-3 rounded-l-lg transition-colors duration-200"
                >
                  <Search size={20} />
                </button>
              </div>
            </form>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                href="/products"
                className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-bold py-3 px-8 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <ShoppingBag size={20} />
                تسوق الآن
              </Link>
              <Link
                href="/services"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-primary-600 font-bold py-3 px-8 rounded-lg transition-colors duration-200"
              >
                اطلب خدمة
              </Link>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Truck className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-lg font-semibold mb-2">توصيل سريع</h3>
              <p className="text-blue-100">توصيل في نفس اليوم داخل المدينة</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Shield className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-lg font-semibold mb-2">دفع آمن</h3>
              <p className="text-blue-100">حماية كاملة لبياناتك المالية</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Clock className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-lg font-semibold mb-2">خدمة 24/7</h3>
              <p className="text-blue-100">دعم فني متواصل طوال الأسبوع</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <ShoppingBag className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-lg font-semibold mb-2">منتجات أصلية</h3>
              <p className="text-blue-100">ضمان الجودة والأصالة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-6"></div>
      </div>
    </section>
  )
}
