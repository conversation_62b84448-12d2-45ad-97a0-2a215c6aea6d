'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Search, ShoppingBag, Truck, Shield, Clock, Mic, Sparkles } from 'lucide-react'

export default function Hero() {
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const router = useRouter()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      setIsSearching(true)
      // Use the new search system
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleQuickShop = () => {
    // Add analytics or tracking here if needed
    router.push('/products')
  }

  const handleRequestService = () => {
    // Add analytics or tracking here if needed
    router.push('/services')
  }

  return (
    <section className="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white">
      <div className="container mx-auto px-4 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-right">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight animate-fade-in">
              مرحباً بكم في
              <span className="block text-yellow-300 animate-pulse">مركز البدوي</span>
            </h1>

            {/* الجملة التسويقية الجديدة */}
            <div className="mb-6 animate-fade-in-up delay-300">
              <p className="text-2xl lg:text-3xl font-bold text-yellow-200 mb-2 drop-shadow-lg">
                اسم له تاريخ
              </p>
              <p className="text-lg lg:text-xl text-blue-100 font-medium italic">
                الاسم يعني الثقة والجودة
              </p>
            </div>

            <p className="text-xl mb-8 text-blue-100 animate-fade-in-up delay-500">
              أفضل المنتجات والخدمات مع توصيل سريع وآمن إلى باب منزلك في جميع أنحاء مصر
            </p>

            {/* Enhanced Search Bar */}
            <div className="mb-8 max-w-2xl mx-auto lg:mx-0">
              <div className="relative">
                <form onSubmit={handleSearch} className="flex items-center bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      placeholder="ابحث في أكثر من 1000 منتج وخدمة..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full px-6 py-4 text-gray-800 text-lg focus:outline-none placeholder-gray-500"
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                      <button
                        type="button"
                        className="p-2 text-gray-400 hover:text-primary-600 rounded-full hover:bg-gray-100 transition-colors"
                        title="البحث الصوتي"
                        onClick={() => {
                          // Trigger voice search
                          const event = new CustomEvent('startVoiceSearch')
                          window.dispatchEvent(event)
                        }}
                      >
                        <Mic className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                  <button
                    type="submit"
                    disabled={isSearching}
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-800 font-bold px-8 py-4 transition-all duration-200 flex items-center gap-2 disabled:opacity-50"
                  >
                    {isSearching ? (
                      <div className="w-5 h-5 border-2 border-gray-800 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <Search className="w-5 h-5" />
                    )}
                    <span className="hidden sm:block">بحث</span>
                  </button>
                </form>

                {/* Search Suggestions */}
                <div className="mt-3 flex flex-wrap gap-2 justify-center lg:justify-start">
                  <span className="text-blue-100 text-sm">بحث سريع:</span>
                  {['هواتف ذكية', 'لابتوب', 'ساعات ذكية', 'خدمة التوصيل'].map((suggestion) => (
                    <button
                      key={suggestion}
                      onClick={() => {
                        setSearchQuery(suggestion)
                        router.push(`/search?q=${encodeURIComponent(suggestion)}`)
                      }}
                      className="text-xs bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded-full transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Enhanced CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button
                onClick={handleQuickShop}
                className="group bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-800 font-bold py-4 px-8 rounded-xl transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <ShoppingBag className="w-6 h-6 group-hover:animate-bounce" />
                <span className="text-lg">تسوق الآن</span>
                <Sparkles className="w-5 h-5 text-yellow-600" />
              </button>

              <button
                onClick={handleRequestService}
                className="group bg-transparent border-2 border-white hover:bg-white hover:text-primary-600 font-bold py-4 px-8 rounded-xl transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Truck className="w-6 h-6 group-hover:animate-pulse" />
                <span className="text-lg">اطلب خدمة</span>
              </button>
            </div>

            {/* Quick Stats */}
            <div className="mt-8 flex flex-wrap gap-6 justify-center lg:justify-start text-blue-100">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm">+1000 منتج متاح</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                <span className="text-sm">توصيل لجميع المحافظات</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span className="text-sm">دعم فني 24/7</span>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Truck className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-lg font-semibold mb-2">توصيل سريع</h3>
              <p className="text-blue-100">توصيل في نفس اليوم داخل المدينة</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Shield className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-lg font-semibold mb-2">دفع آمن</h3>
              <p className="text-blue-100">حماية كاملة لبياناتك المالية</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Clock className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-lg font-semibold mb-2">خدمة 24/7</h3>
              <p className="text-blue-100">دعم فني متواصل طوال الأسبوع</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <ShoppingBag className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
              <h3 className="text-lg font-semibold mb-2">منتجات أصلية</h3>
              <p className="text-blue-100">ضمان الجودة والأصالة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-6"></div>
      </div>
    </section>
  )
}
