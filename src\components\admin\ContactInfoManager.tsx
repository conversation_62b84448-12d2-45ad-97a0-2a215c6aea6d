'use client'

import { useState } from 'react'
import { Phone, Mail, MapPin, Save, Edit } from 'lucide-react'

export default function ContactInfoManager() {
  const [isEditing, setIsEditing] = useState(false)
  const [contactInfo, setContactInfo] = useState({
    phone: '+20 ************',
    whatsapp: '+20 ************',
    email: '<EMAIL>',
    address: 'شارع التحرير، المعادي',
    city: 'القاهرة',
    vodafoneCash: '01012345678',
    instaPay: '01012345678',
    orangeMoney: '01112345678',
    etisalatCash: '01212345678'
  })
  
  const [editedInfo, setEditedInfo] = useState(contactInfo)

  const handleSave = () => {
    setContactInfo(editedInfo)
    setIsEditing(false)
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('contactInfo', JSON.stringify(editedInfo))
    }
    
    window.dispatchEvent(new CustomEvent('contactInfoUpdated', { 
      detail: editedInfo 
    }))
    
    alert('تم حفظ المعلومات بنجاح!')
  }

  const handleCancel = () => {
    setEditedInfo(contactInfo)
    setIsEditing(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة معلومات الاتصال</h2>
          <p className="text-gray-600">تحديث أرقام الهواتف والعناوين ومعلومات الدفع</p>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <button
                onClick={handleSave}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                حفظ التغييرات
              </button>
              <button
                onClick={handleCancel}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg"
              >
                إلغاء
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              تعديل المعلومات
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Phone className="w-5 h-5 text-primary-600" />
            معلومات الاتصال الأساسية
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف الرئيسي</label>
              {isEditing ? (
                <input
                  type="tel"
                  value={editedInfo.phone}
                  onChange={(e) => setEditedInfo({...editedInfo, phone: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.phone}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">رقم الواتساب</label>
              {isEditing ? (
                <input
                  type="tel"
                  value={editedInfo.whatsapp}
                  onChange={(e) => setEditedInfo({...editedInfo, whatsapp: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.whatsapp}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
              {isEditing ? (
                <input
                  type="email"
                  value={editedInfo.email}
                  onChange={(e) => setEditedInfo({...editedInfo, email: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.email}</p>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <MapPin className="w-5 h-5 text-primary-600" />
            معلومات العنوان
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.address}
                  onChange={(e) => setEditedInfo({...editedInfo, address: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.address}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">المدينة</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.city}
                  onChange={(e) => setEditedInfo({...editedInfo, city: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.city}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">أرقام المحافظ الإلكترونية</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">فودافون كاش</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.vodafoneCash}
                onChange={(e) => setEditedInfo({...editedInfo, vodafoneCash: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-red-50 px-3 py-2 rounded-lg border border-red-200">{contactInfo.vodafoneCash}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">إنستاباي</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.instaPay}
                onChange={(e) => setEditedInfo({...editedInfo, instaPay: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">{contactInfo.instaPay}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">أورانج موني</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.orangeMoney}
                onChange={(e) => setEditedInfo({...editedInfo, orangeMoney: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-orange-50 px-3 py-2 rounded-lg border border-orange-200">{contactInfo.orangeMoney}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">اتصالات كاش</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.etisalatCash}
                onChange={(e) => setEditedInfo({...editedInfo, etisalatCash: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-green-50 px-3 py-2 rounded-lg border border-green-200">{contactInfo.etisalatCash}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
