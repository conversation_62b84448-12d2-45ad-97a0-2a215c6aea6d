'use client'

import { useState } from 'react'
import { Phone, Mail, MapPin, Clock, Save, Edit, Globe, MessageCircle } from 'lucide-react'

interface ContactInfo {
  phone: string
  whatsapp: string
  email: string
  supportEmail: string
  paymentsEmail: string
  address: string
  city: string
  governorate: string
  workingHours: {
    weekdays: string
    weekend: string
  }
  socialMedia: {
    facebook: string
    instagram: string
    twitter: string
    youtube: string
  }
  paymentNumbers: {
    vodafoneCash: string
    instaPay: string
    orangeMoney: string
    etisalatCash: string
  }
  bankAccounts: {
    nbe: {
      accountNumber: string
      accountName: string
      branch: string
    }
    cib: {
      accountNumber: string
      accountName: string
      branch: string
    }
  }
}

export default function ContactInfoManager() {
  const [isEditing, setIsEditing] = useState(false)

  // Load data from localStorage or use defaults
  const getInitialContactInfo = (): ContactInfo => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('contactInfo')
      if (saved) {
        try {
          return JSON.parse(saved)
        } catch (e) {
          console.error('Error parsing saved contact info:', e)
        }
      }
    }
    return {
    phone: '+20 ************',
    whatsapp: '+20 ************',
    email: '<EMAIL>',
    supportEmail: '<EMAIL>',
    paymentsEmail: '<EMAIL>',
    address: 'شارع التحرير، المعادي',
    city: 'القاهرة',
    governorate: 'القاهرة',
    workingHours: {
      weekdays: 'الأحد - الخميس: 9:00 ص - 9:00 م',
      weekend: 'الجمعة - السبت: 10:00 ص - 8:00 م'
    },
    socialMedia: {
      facebook: 'https://facebook.com/albadawi.center',
      instagram: 'https://instagram.com/albadawi.center',
      twitter: 'https://twitter.com/albadawi_center',
      youtube: 'https://youtube.com/@albadawi-center'
    },
    paymentNumbers: {
      vodafoneCash: '***********',
      instaPay: '***********',
      orangeMoney: '***********',
      etisalatCash: '***********'
    },
    bankAccounts: {
      nbe: {
        accountNumber: '****************',
        accountName: 'مركز البدوي للتجارة',
        branch: 'فرع المعادي'
      },
      cib: {
        accountNumber: '****************',
        accountName: 'مركز البدوي للتجارة',
        branch: 'فرع مصر الجديدة'
      }
    }
  }

  const [contactInfo, setContactInfo] = useState<ContactInfo>(getInitialContactInfo())

  const [editedInfo, setEditedInfo] = useState<ContactInfo>(contactInfo)

  const handleSave = () => {
    setContactInfo(editedInfo)
    setIsEditing(false)

    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('contactInfo', JSON.stringify(editedInfo))
    }

    // Update the website data immediately
    updateWebsiteData(editedInfo)

    alert('تم حفظ المعلومات بنجاح! ستظهر التغييرات في الموقع فوراً.')
  }

  const updateWebsiteData = (newInfo: ContactInfo) => {
    // Update header phone number
    const headerPhone = document.querySelector('[data-header-phone]')
    if (headerPhone) {
      headerPhone.textContent = newInfo.phone
    }

    // Update footer information
    const footerPhone = document.querySelector('[data-footer-phone]')
    if (footerPhone) {
      footerPhone.textContent = newInfo.phone
    }

    // Trigger a custom event to update other components
    window.dispatchEvent(new CustomEvent('contactInfoUpdated', {
      detail: newInfo
    }))
  }

  const handleCancel = () => {
    setEditedInfo(contactInfo)
    setIsEditing(false)
  }

  const updateField = (path: string, value: string) => {
    const keys = path.split('.')
    const newInfo = { ...editedInfo }
    let current: any = newInfo
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]]
    }
    current[keys[keys.length - 1]] = value
    
    setEditedInfo(newInfo)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة معلومات الاتصال</h2>
          <p className="text-gray-600">تحديث أرقام الهواتف والعناوين ومعلومات الدفع</p>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <button
                onClick={handleSave}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                حفظ التغييرات
              </button>
              <button
                onClick={handleCancel}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg"
              >
                إلغاء
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              تعديل المعلومات
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Contact Info */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Phone className="w-5 h-5 text-primary-600" />
            معلومات الاتصال الأساسية
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف الرئيسي</label>
              {isEditing ? (
                <input
                  type="tel"
                  value={editedInfo.phone}
                  onChange={(e) => updateField('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.phone}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">رقم الواتساب</label>
              {isEditing ? (
                <input
                  type="tel"
                  value={editedInfo.whatsapp}
                  onChange={(e) => updateField('whatsapp', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.whatsapp}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني الرئيسي</label>
              {isEditing ? (
                <input
                  type="email"
                  value={editedInfo.email}
                  onChange={(e) => updateField('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.email}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">بريد الدعم الفني</label>
              {isEditing ? (
                <input
                  type="email"
                  value={editedInfo.supportEmail}
                  onChange={(e) => updateField('supportEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.supportEmail}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">بريد المدفوعات</label>
              {isEditing ? (
                <input
                  type="email"
                  value={editedInfo.paymentsEmail}
                  onChange={(e) => updateField('paymentsEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.paymentsEmail}</p>
              )}
            </div>
          </div>
        </div>

        {/* Address Info */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <MapPin className="w-5 h-5 text-primary-600" />
            معلومات العنوان
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.address}
                  onChange={(e) => updateField('address', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.address}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">المدينة</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedInfo.city}
                    onChange={(e) => updateField('city', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                ) : (
                  <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.city}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">المحافظة</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedInfo.governorate}
                    onChange={(e) => updateField('governorate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                ) : (
                  <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.governorate}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">ساعات العمل - أيام الأسبوع</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.workingHours.weekdays}
                  onChange={(e) => updateField('workingHours.weekdays', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.workingHours.weekdays}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">ساعات العمل - نهاية الأسبوع</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.workingHours.weekend}
                  onChange={(e) => updateField('workingHours.weekend', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.workingHours.weekend}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <MessageCircle className="w-5 h-5 text-primary-600" />
          أرقام المحافظ الإلكترونية
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">فودافون كاش</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.paymentNumbers.vodafoneCash}
                onChange={(e) => updateField('paymentNumbers.vodafoneCash', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-red-50 px-3 py-2 rounded-lg border border-red-200">{contactInfo.paymentNumbers.vodafoneCash}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">إنستاباي</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.paymentNumbers.instaPay}
                onChange={(e) => updateField('paymentNumbers.instaPay', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">{contactInfo.paymentNumbers.instaPay}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">أورانج موني</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.paymentNumbers.orangeMoney}
                onChange={(e) => updateField('paymentNumbers.orangeMoney', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-orange-50 px-3 py-2 rounded-lg border border-orange-200">{contactInfo.paymentNumbers.orangeMoney}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">اتصالات كاش</label>
            {isEditing ? (
              <input
                type="tel"
                value={editedInfo.paymentNumbers.etisalatCash}
                onChange={(e) => updateField('paymentNumbers.etisalatCash', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-green-50 px-3 py-2 rounded-lg border border-green-200">{contactInfo.paymentNumbers.etisalatCash}</p>
            )}
          </div>
        </div>
      </div>

      {/* Bank Accounts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Globe className="w-5 h-5 text-primary-600" />
            البنك الأهلي المصري
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">رقم الحساب</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.bankAccounts.nbe.accountNumber}
                  onChange={(e) => updateField('bankAccounts.nbe.accountNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200 font-mono">{contactInfo.bankAccounts.nbe.accountNumber}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">اسم المستفيد</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.bankAccounts.nbe.accountName}
                  onChange={(e) => updateField('bankAccounts.nbe.accountName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.bankAccounts.nbe.accountName}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الفرع</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.bankAccounts.nbe.branch}
                  onChange={(e) => updateField('bankAccounts.nbe.branch', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.bankAccounts.nbe.branch}</p>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Globe className="w-5 h-5 text-primary-600" />
            البنك التجاري الدولي
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">رقم الحساب</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.bankAccounts.cib.accountNumber}
                  onChange={(e) => updateField('bankAccounts.cib.accountNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-purple-50 px-3 py-2 rounded-lg border border-purple-200 font-mono">{contactInfo.bankAccounts.cib.accountNumber}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">اسم المستفيد</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.bankAccounts.cib.accountName}
                  onChange={(e) => updateField('bankAccounts.cib.accountName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.bankAccounts.cib.accountName}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الفرع</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedInfo.bankAccounts.cib.branch}
                  onChange={(e) => updateField('bankAccounts.cib.branch', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              ) : (
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactInfo.bankAccounts.cib.branch}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Social Media */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Globe className="w-5 h-5 text-primary-600" />
          وسائل التواصل الاجتماعي
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">فيسبوك</label>
            {isEditing ? (
              <input
                type="url"
                value={editedInfo.socialMedia.facebook}
                onChange={(e) => updateField('socialMedia.facebook', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200 truncate">{contactInfo.socialMedia.facebook}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">إنستجرام</label>
            {isEditing ? (
              <input
                type="url"
                value={editedInfo.socialMedia.instagram}
                onChange={(e) => updateField('socialMedia.instagram', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-pink-50 px-3 py-2 rounded-lg border border-pink-200 truncate">{contactInfo.socialMedia.instagram}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">تويتر</label>
            {isEditing ? (
              <input
                type="url"
                value={editedInfo.socialMedia.twitter}
                onChange={(e) => updateField('socialMedia.twitter', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-sky-50 px-3 py-2 rounded-lg border border-sky-200 truncate">{contactInfo.socialMedia.twitter}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">يوتيوب</label>
            {isEditing ? (
              <input
                type="url"
                value={editedInfo.socialMedia.youtube}
                onChange={(e) => updateField('socialMedia.youtube', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            ) : (
              <p className="text-gray-900 bg-red-50 px-3 py-2 rounded-lg border border-red-200 truncate">{contactInfo.socialMedia.youtube}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
