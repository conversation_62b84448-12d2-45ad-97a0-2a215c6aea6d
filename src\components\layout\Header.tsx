'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { ShoppingCart, User, Menu, X, MapPin, Phone } from 'lucide-react'
import AuthModal from '@/components/modals/AuthModal'
import { useCart } from '@/hooks/useCart'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [showAuthModal, setShowAuthModal] = useState(false)
  const { user, signOut } = useAuth()
  const { items } = useCart()

  const cartItemsCount = items.reduce((total, item) => total + item.quantity, 0)

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      {/* Top Bar */}
      <div className="bg-primary-600 text-white py-2">
        <div className="container mx-auto px-4 flex justify-between items-center text-sm">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Phone size={14} />
              <span>+966 50 123 4567</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin size={14} />
              <span>توصيل مجاني للطلبات أكثر من 200 ريال</span>
            </div>
          </div>
          <div className="hidden md:block">
            <span>مرحباً بكم في متجر كوبرا</span>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <div className="bg-primary-600 text-white px-4 py-2 rounded-lg font-bold text-xl">
              كوبرا
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
            <Link href="/" className="text-gray-700 hover:text-primary-600 font-medium">
              الرئيسية
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-primary-600 font-medium">
              المنتجات
            </Link>
            <Link href="/services" className="text-gray-700 hover:text-primary-600 font-medium">
              الخدمات
            </Link>
            <Link href="/about" className="text-gray-700 hover:text-primary-600 font-medium">
              من نحن
            </Link>
            <Link href="/contact" className="text-gray-700 hover:text-primary-600 font-medium">
              اتصل بنا
            </Link>
          </nav>

          {/* User Actions */}
          <div className="flex items-center gap-4">
            {/* Cart */}
            <Link href="/cart" className="relative">
              <ShoppingCart className="w-6 h-6 text-gray-700 hover:text-primary-600" />
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartItemsCount}
                </span>
              )}
            </Link>

            {/* User Menu */}
            {user ? (
              <div className="relative group">
                <button className="flex items-center gap-2 text-gray-700 hover:text-primary-600">
                  <User className="w-6 h-6" />
                  <span className="hidden md:block">{user.email}</span>
                </button>
                <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link href="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    الملف الشخصي
                  </Link>
                  <Link href="/orders" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    طلباتي
                  </Link>
                  <button
                    onClick={signOut}
                    className="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    تسجيل الخروج
                  </button>
                </div>
              </div>
            ) : (
              <button
                onClick={() => setShowAuthModal(true)}
                className="flex items-center gap-2 text-gray-700 hover:text-primary-600"
              >
                <User className="w-6 h-6" />
                <span className="hidden md:block">تسجيل الدخول</span>
              </button>
            )}

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t pt-4">
            <div className="flex flex-col space-y-2">
              <Link href="/" className="text-gray-700 hover:text-primary-600 py-2">
                الرئيسية
              </Link>
              <Link href="/products" className="text-gray-700 hover:text-primary-600 py-2">
                المنتجات
              </Link>
              <Link href="/services" className="text-gray-700 hover:text-primary-600 py-2">
                الخدمات
              </Link>
              <Link href="/about" className="text-gray-700 hover:text-primary-600 py-2">
                من نحن
              </Link>
              <Link href="/contact" className="text-gray-700 hover:text-primary-600 py-2">
                اتصل بنا
              </Link>
            </div>
          </nav>
        )}
      </div>

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      )}
    </header>
  )
}
