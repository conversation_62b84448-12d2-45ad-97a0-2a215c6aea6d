'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ShoppingCart, User, Menu, X, MapPin, Phone } from 'lucide-react'
import AuthManager from '@/components/auth/AuthManager'
import SearchBar from '@/components/search/SearchBar'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [contactInfo, setContactInfo] = useState({
    phone: '+20 ************',
    whatsapp: '+20 ************'
  })

  // Simulate cart items count (you can replace this with your cart logic)
  const cartItemsCount = 0

  // Load contact info from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('contactInfo')
      if (saved) {
        try {
          const parsedInfo = JSON.parse(saved)
          setContactInfo({
            phone: parsedInfo.phone || '+20 ************',
            whatsapp: parsedInfo.whatsapp || '+20 ************'
          })
        } catch (e) {
          console.error('Error parsing contact info:', e)
        }
      }
    }

    // Listen for contact info updates
    const handleContactUpdate = (event: any) => {
      const newInfo = event.detail
      setContactInfo({
        phone: newInfo.phone || '+20 ************',
        whatsapp: newInfo.whatsapp || '+20 ************'
      })
    }

    window.addEventListener('contactInfoUpdated', handleContactUpdate)
    return () => window.removeEventListener('contactInfoUpdated', handleContactUpdate)
  }, [])

  return (
    <>
      {/* Top Contact Bar */}
      <div className="bg-primary-600 text-white py-2 text-sm">
        <div className="container mx-auto px-4">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
            <div className="flex items-center gap-4">
              <span data-header-phone className="flex items-center gap-1">
                📞 {contactInfo.phone}
              </span>
              <span className="flex items-center gap-1">
                💬 واتساب: {contactInfo.whatsapp}
              </span>
            </div>
            <div className="text-yellow-300 font-medium">
              مركز البدوي - اسم له تاريخ وأصالة
            </div>
          </div>
        </div>
      </div>

      <header className="bg-white shadow-sm sticky top-0 z-40">
      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center flex-shrink-0">
            <div className="bg-primary-600 text-white px-4 py-2 rounded-lg font-bold text-xl">
              مركز البدوي
            </div>
          </Link>

          {/* Search Bar */}
          <div className="flex-1 max-w-2xl mx-4 hidden md:block">
            <SearchBar
              placeholder="ابحث في المنتجات والخدمات..."
              showVoiceSearch={true}
              showImageSearch={false}
            />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
            <Link href="/" className="text-gray-700 hover:text-primary-600 font-medium">
              الرئيسية
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-primary-600 font-medium">
              المنتجات
            </Link>
            <Link href="/services" className="text-gray-700 hover:text-primary-600 font-medium">
              الخدمات
            </Link>
            <Link href="/about" className="text-gray-700 hover:text-primary-600 font-medium">
              من نحن
            </Link>
            <Link href="/contact" className="text-gray-700 hover:text-primary-600 font-medium">
              اتصل بنا
            </Link>
          </nav>

          {/* User Actions */}
          <div className="flex items-center gap-4">
            {/* Cart */}
            <Link href="/cart" className="relative">
              <ShoppingCart className="w-6 h-6 text-gray-700 hover:text-primary-600" />
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartItemsCount}
                </span>
              )}
            </Link>

            {/* User Authentication */}
            <AuthManager showLoginButton={true} showUserMenu={true} />

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Search Bar */}
        <div className="md:hidden mt-4">
          <SearchBar
            placeholder="ابحث في المنتجات والخدمات..."
            showVoiceSearch={true}
            showImageSearch={false}
          />
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t pt-4">
            <div className="flex flex-col space-y-2">
              <Link href="/" className="text-gray-700 hover:text-primary-600 py-2">
                الرئيسية
              </Link>
              <Link href="/products" className="text-gray-700 hover:text-primary-600 py-2">
                المنتجات
              </Link>
              <Link href="/services" className="text-gray-700 hover:text-primary-600 py-2">
                الخدمات
              </Link>
              <Link href="/about" className="text-gray-700 hover:text-primary-600 py-2">
                من نحن
              </Link>
              <Link href="/contact" className="text-gray-700 hover:text-primary-600 py-2">
                اتصل بنا
              </Link>
            </div>
          </nav>
        )}
      </div>


    </header>
    </>
  )
}
