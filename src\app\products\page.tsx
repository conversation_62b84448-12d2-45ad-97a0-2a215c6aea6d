'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, Grid, List, Star, ShoppingCart, Heart } from 'lucide-react'
import PriceDisplay from '@/components/products/PriceDisplay'

interface Product {
  id: string
  name: string
  price: number
  wholesalePrice: number
  originalPrice?: number
  image: string
  rating: number
  reviews: number
  category: string
  inStock: boolean
  description: string
}

// Sample products data
const sampleProducts: Product[] = [
  {
    id: '1',
    name: 'هاتف ذكي متطور',
    price: 2500,
    wholesalePrice: 2200,
    originalPrice: 3000,
    image: '/images/phone.jpg',
    rating: 4.8,
    reviews: 124,
    category: 'إلكترونيات',
    inStock: true,
    description: 'هاتف ذكي بمواصفات عالية وكاميرا متطورة'
  },
  {
    id: '2',
    name: 'لابتوب عالي الأداء',
    price: 4500,
    wholesalePrice: 4000,
    originalPrice: 5200,
    image: '/images/laptop.jpg',
    rating: 4.9,
    reviews: 89,
    category: 'إلكترونيات',
    inStock: true,
    description: 'لا<PERSON>تو<PERSON> للألعاب والعمل المهني'
  },
  {
    id: '3',
    name: 'ساعة ذكية رياضية',
    price: 800,
    wholesalePrice: 700,
    originalPrice: 1000,
    image: '/images/watch.jpg',
    rating: 4.6,
    reviews: 156,
    category: 'إكسسوارات',
    inStock: true,
    description: 'ساعة ذكية لتتبع اللياقة البدنية'
  },
  {
    id: '4',
    name: 'سماعات لاسلكية',
    price: 350,
    wholesalePrice: 300,
    originalPrice: 450,
    image: '/images/headphones.jpg',
    rating: 4.7,
    reviews: 203,
    category: 'إلكترونيات',
    inStock: true,
    description: 'سماعات بجودة صوت عالية وإلغاء الضوضاء'
  },
  {
    id: '5',
    name: 'كاميرا رقمية احترافية',
    price: 3200,
    wholesalePrice: 2800,
    originalPrice: 3800,
    image: '/images/camera.jpg',
    rating: 4.9,
    reviews: 67,
    category: 'إلكترونيات',
    inStock: true,
    description: 'كاميرا احترافية للتصوير الفوتوغرافي'
  },
  {
    id: '6',
    name: 'جهاز تابلت متطور',
    price: 1800,
    wholesalePrice: 1600,
    originalPrice: 2200,
    image: '/images/tablet.jpg',
    rating: 4.5,
    reviews: 98,
    category: 'إلكترونيات',
    inStock: false,
    description: 'جهاز تابلت للعمل والترفيه'
  }
]

const categories = ['الكل', 'إلكترونيات', 'إكسسوارات', 'أجهزة منزلية', 'ملابس', 'كتب']

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('الكل')
  const [sortBy, setSortBy] = useState('name')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [favorites, setFavorites] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  
  const { addItem } = useCart()

  useEffect(() => {
    setProducts(sampleProducts)
    setFilteredProducts(sampleProducts)
    
    // Load favorites
    const savedFavorites = localStorage.getItem('favorites')
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites))
    }
  }, [])

  useEffect(() => {
    let filtered = products

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'الكل') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // Sort products
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price
        case 'price-high':
          return b.price - a.price
        case 'rating':
          return b.rating - a.rating
        case 'name':
        default:
          return a.name.localeCompare(b.name, 'ar')
      }
    })

    setFilteredProducts(filtered)
  }, [products, searchQuery, selectedCategory, sortBy])

  const toggleFavorite = (productId: string) => {
    const newFavorites = favorites.includes(productId)
      ? favorites.filter(id => id !== productId)
      : [...favorites, productId]
    
    setFavorites(newFavorites)
    localStorage.setItem('favorites', JSON.stringify(newFavorites))
    
    toast.success(
      favorites.includes(productId) 
        ? 'تم إزالة المنتج من المفضلة' 
        : 'تم إضافة المنتج للمفضلة'
    )
  }

  const handleAddToCart = (product: Product) => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    })
    toast.success('تم إضافة المنتج لسلة التسوق')
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">المنتجات</h1>
        <p className="text-gray-600">اكتشف مجموعتنا الواسعة من المنتجات عالية الجودة</p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="ابحث عن المنتجات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="name">ترتيب حسب الاسم</option>
            <option value="price-low">السعر: من الأقل للأعلى</option>
            <option value="price-high">السعر: من الأعلى للأقل</option>
            <option value="rating">التقييم</option>
          </select>

          {/* View Mode */}
          <div className="flex border border-gray-300 rounded-lg">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'text-gray-600'}`}
            >
              <Grid size={20} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'text-gray-600'}`}
            >
              <List size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">
          عرض {filteredProducts.length} من أصل {products.length} منتج
        </p>
      </div>

      {/* Products Grid/List */}
      <div className={viewMode === 'grid' 
        ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
        : 'space-y-4'
      }>
        {filteredProducts.map((product) => (
          <div key={product.id} className={viewMode === 'grid' 
            ? 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300'
            : 'bg-white rounded-lg shadow-md p-4 flex gap-4 hover:shadow-lg transition-shadow duration-300'
          }>
            {/* Product Image */}
            <div className={viewMode === 'grid' ? 'relative' : 'relative w-32 h-32 flex-shrink-0'}>
              <div className={`${viewMode === 'grid' ? 'w-full h-48' : 'w-full h-full'} bg-gray-200 flex items-center justify-center`}>
                <span className="text-gray-500">صورة المنتج</span>
              </div>
              
              {/* Discount Badge */}
              {product.originalPrice && (
                <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-bold">
                  {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% خصم
                </div>
              )}
              
              {/* Favorite Button */}
              <button
                onClick={() => toggleFavorite(product.id)}
                className="absolute top-2 left-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50"
              >
                <Heart
                  size={18}
                  className={favorites.includes(product.id) ? 'text-red-500 fill-current' : 'text-gray-400'}
                />
              </button>
            </div>

            {/* Product Info */}
            <div className={viewMode === 'grid' ? 'p-4' : 'flex-1'}>
              <div className="text-sm text-gray-500 mb-1">{product.category}</div>
              <h3 className="font-semibold text-gray-800 mb-2">{product.name}</h3>
              {viewMode === 'list' && (
                <p className="text-gray-600 text-sm mb-2">{product.description}</p>
              )}
              
              {/* Rating */}
              <div className="flex items-center gap-1 mb-2">
                <div className="flex">{renderStars(product.rating)}</div>
                <span className="text-sm text-gray-600">({product.reviews})</span>
              </div>
              
              {/* Price */}
              <div className="mb-3">
                <PriceDisplay
                  retailPrice={product.price}
                  wholesalePrice={product.wholesalePrice}
                  originalPrice={product.originalPrice}
                  currency="جنيه"
                />
              </div>
              
              {/* Actions */}
              <div className="flex gap-2">
                <button
                  onClick={() => handleAddToCart(product)}
                  disabled={!product.inStock}
                  className="flex-1 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
                >
                  <ShoppingCart size={16} />
                  {product.inStock ? 'أضف للسلة' : 'غير متوفر'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">لم يتم العثور على منتجات مطابقة للبحث</p>
        </div>
      )}
    </div>
  )
}
