'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Star, ShoppingCart, Heart, Minus, Plus, Share2, Shield, Truck, RotateCcw } from 'lucide-react'
import { useCart } from '@/hooks/useCart'
import toast from 'react-hot-toast'
import Link from 'next/link'

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  images: string[]
  rating: number
  reviews: number
  category: string
  inStock: boolean
  stockQuantity: number
  features: string[]
  specifications: { [key: string]: string }
}

// Sample product data
const sampleProduct: Product = {
  id: '1',
  name: 'هاتف ذكي متطور',
  description: 'هاتف ذكي بمواصفات عالية وكاميرا متطورة مع شاشة OLED عالية الدقة وبطارية تدوم طوال اليوم. يتميز بتصميم أنيق ومقاوم للماء مع نظام تشغيل حديث وأداء سريع.',
  price: 2500,
  originalPrice: 3000,
  images: ['/images/phone1.jpg', '/images/phone2.jpg', '/images/phone3.jpg'],
  rating: 4.8,
  reviews: 124,
  category: 'إلكترونيات',
  inStock: true,
  stockQuantity: 15,
  features: [
    'شاشة OLED 6.7 بوصة',
    'كاميرا ثلاثية 108 ميجابكسل',
    'بطارية 5000 مللي أمبير',
    'ذاكرة 256 جيجابايت',
    'مقاوم للماء IP68',
    'شحن سريع 65 واط'
  ],
  specifications: {
    'الشاشة': '6.7 بوصة OLED',
    'المعالج': 'Snapdragon 8 Gen 2',
    'الذاكرة': '12 جيجابايت RAM',
    'التخزين': '256 جيجابايت',
    'الكاميرا الخلفية': '108 + 12 + 12 ميجابكسل',
    'الكاميرا الأمامية': '32 ميجابكسل',
    'البطارية': '5000 مللي أمبير',
    'نظام التشغيل': 'Android 14',
    'الوزن': '195 جرام',
    'الأبعاد': '163.3 × 75.9 × 8.9 مم'
  }
}

export default function ProductDetailPage() {
  const params = useParams()
  const { addItem } = useCart()
  const [product, setProduct] = useState<Product | null>(null)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isFavorite, setIsFavorite] = useState(false)
  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews'>('description')

  useEffect(() => {
    // In a real app, fetch product by ID
    setProduct(sampleProduct)
    
    // Check if product is in favorites
    const favorites = JSON.parse(localStorage.getItem('favorites') || '[]')
    setIsFavorite(favorites.includes(params.id))
  }, [params.id])

  const handleAddToCart = () => {
    if (!product) return
    
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.images[0],
      quantity
    })
    
    toast.success(`تم إضافة ${quantity} من ${product.name} إلى السلة`)
  }

  const toggleFavorite = () => {
    const favorites = JSON.parse(localStorage.getItem('favorites') || '[]')
    let newFavorites
    
    if (isFavorite) {
      newFavorites = favorites.filter((id: string) => id !== params.id)
      toast.success('تم إزالة المنتج من المفضلة')
    } else {
      newFavorites = [...favorites, params.id]
      toast.success('تم إضافة المنتج للمفضلة')
    }
    
    localStorage.setItem('favorites', JSON.stringify(newFavorites))
    setIsFavorite(!isFavorite)
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.name,
          text: product?.description,
          url: window.location.href
        })
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href)
        toast.success('تم نسخ رابط المنتج')
      }
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('تم نسخ رابط المنتج')
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={20}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ))
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-gray-600">جاري تحميل المنتج...</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="mb-8">
        <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
          <Link href="/" className="hover:text-primary-600">الرئيسية</Link>
          <span>/</span>
          <Link href="/products" className="hover:text-primary-600">المنتجات</Link>
          <span>/</span>
          <span className="text-gray-800">{product.name}</span>
        </div>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Images */}
        <div>
          {/* Main Image */}
          <div className="bg-gray-100 rounded-lg mb-4 aspect-square flex items-center justify-center">
            <span className="text-gray-500">صورة المنتج الرئيسية</span>
          </div>
          
          {/* Thumbnail Images */}
          <div className="grid grid-cols-4 gap-2">
            {product.images.map((_, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                className={`bg-gray-100 rounded-lg aspect-square flex items-center justify-center border-2 ${
                  selectedImage === index ? 'border-primary-600' : 'border-transparent'
                }`}
              >
                <span className="text-gray-500 text-xs">صورة {index + 1}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div>
          <div className="mb-4">
            <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">{product.category}</span>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-800 mb-4">{product.name}</h1>
          
          {/* Rating */}
          <div className="flex items-center gap-2 mb-6">
            <div className="flex">{renderStars(product.rating)}</div>
            <span className="text-gray-600">({product.reviews} تقييم)</span>
          </div>
          
          {/* Price */}
          <div className="flex items-center gap-3 mb-6">
            <span className="text-3xl font-bold text-primary-600">{product.price} ريال</span>
            {product.originalPrice && (
              <>
                <span className="text-xl text-gray-500 line-through">{product.originalPrice} ريال</span>
                <span className="bg-red-500 text-white px-2 py-1 rounded text-sm font-bold">
                  {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% خصم
                </span>
              </>
            )}
          </div>
          
          {/* Stock Status */}
          <div className="mb-6">
            {product.inStock ? (
              <div className="flex items-center gap-2 text-green-600">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>متوفر في المخزون ({product.stockQuantity} قطعة)</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-red-600">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span>غير متوفر حالياً</span>
              </div>
            )}
          </div>
          
          {/* Quantity Selector */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">الكمية</label>
            <div className="flex items-center gap-3">
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="p-2 hover:bg-gray-100 rounded-r-lg"
                >
                  <Minus size={16} />
                </button>
                <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">
                  {quantity}
                </span>
                <button
                  onClick={() => setQuantity(Math.min(product.stockQuantity, quantity + 1))}
                  className="p-2 hover:bg-gray-100 rounded-l-lg"
                >
                  <Plus size={16} />
                </button>
              </div>
              <span className="text-sm text-gray-600">
                الحد الأقصى: {product.stockQuantity}
              </span>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-3 mb-8">
            <button
              onClick={handleAddToCart}
              disabled={!product.inStock}
              className="flex-1 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <ShoppingCart size={20} />
              {product.inStock ? 'أضف للسلة' : 'غير متوفر'}
            </button>
            
            <button
              onClick={toggleFavorite}
              className="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <Heart
                size={20}
                className={isFavorite ? 'text-red-500 fill-current' : 'text-gray-400'}
              />
            </button>
            
            <button
              onClick={handleShare}
              className="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <Share2 size={20} className="text-gray-400" />
            </button>
          </div>
          
          {/* Features */}
          <div className="mb-8">
            <h3 className="font-semibold text-gray-800 mb-3">المميزات الرئيسية</h3>
            <ul className="space-y-2">
              {product.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-gray-600">
                  <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Guarantees */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Shield className="text-green-500" size={16} />
              <span>ضمان سنة</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Truck className="text-blue-500" size={16} />
              <span>شحن مجاني</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <RotateCcw className="text-purple-500" size={16} />
              <span>إرجاع 14 يوم</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Product Details Tabs */}
      <div className="mt-16">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 space-x-reverse">
            {[
              { id: 'description', label: 'الوصف' },
              { id: 'specifications', label: 'المواصفات' },
              { id: 'reviews', label: 'التقييمات' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-600 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
        
        <div className="py-8">
          {activeTab === 'description' && (
            <div className="prose max-w-none">
              <p className="text-gray-600 leading-relaxed">{product.description}</p>
            </div>
          )}
          
          {activeTab === 'specifications' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(product.specifications).map(([key, value]) => (
                <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                  <span className="font-medium text-gray-800">{key}</span>
                  <span className="text-gray-600">{value}</span>
                </div>
              ))}
            </div>
          )}
          
          {activeTab === 'reviews' && (
            <div className="text-center py-8">
              <p className="text-gray-600">التقييمات ستكون متاحة قريباً</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
