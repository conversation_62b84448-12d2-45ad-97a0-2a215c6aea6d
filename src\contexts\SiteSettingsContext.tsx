'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export interface ContactInfo {
  id: string
  type: 'phone' | 'email' | 'address' | 'whatsapp'
  label: string
  value: string
  isActive: boolean
}

export interface PaymentInfo {
  id: string
  type: 'bank' | 'wallet' | 'cash'
  name: string
  accountNumber: string
  accountName: string
  instructions: string[]
  isActive: boolean
}

export interface SocialMedia {
  id: string
  platform: 'facebook' | 'instagram' | 'twitter' | 'youtube' | 'tiktok' | 'linkedin'
  name: string
  url: string
  isActive: boolean
}

export interface SiteSettings {
  contactInfo: ContactInfo[]
  paymentInfo: PaymentInfo[]
  socialMedia: SocialMedia[]
  businessInfo: {
    name: string
    slogan: string
    description: string
    workingHours: string
    location: string
  }
}

interface SiteSettingsContextType {
  settings: SiteSettings
  updateContactInfo: (contactInfo: ContactInfo[]) => void
  updatePaymentInfo: (paymentInfo: PaymentInfo[]) => void
  updateSocialMedia: (socialMedia: SocialMedia[]) => void
  updateBusinessInfo: (businessInfo: Partial<SiteSettings['businessInfo']>) => void
  addContactInfo: (contact: Omit<ContactInfo, 'id'>) => void
  addPaymentInfo: (payment: Omit<PaymentInfo, 'id'>) => void
  addSocialMedia: (social: Omit<SocialMedia, 'id'>) => void
  removeContactInfo: (id: string) => void
  removePaymentInfo: (id: string) => void
  removeSocialMedia: (id: string) => void
}

const defaultSettings: SiteSettings = {
  contactInfo: [
    {
      id: '1',
      type: 'phone',
      label: 'الهاتف الرئيسي',
      value: '+20 ************',
      isActive: true
    },
    {
      id: '2',
      type: 'whatsapp',
      label: 'واتساب',
      value: '+20 ************',
      isActive: true
    },
    {
      id: '3',
      type: 'email',
      label: 'البريد الإلكتروني',
      value: '<EMAIL>',
      isActive: true
    },
    {
      id: '4',
      type: 'address',
      label: 'العنوان',
      value: 'القاهرة، مصر',
      isActive: true
    }
  ],
  paymentInfo: [
    {
      id: '1',
      type: 'wallet',
      name: 'فودافون كاش',
      accountNumber: '***********',
      accountName: 'مركز البدوي',
      instructions: [
        'اتصل بـ *9*رقم المحفظة*المبلغ#',
        'أو استخدم تطبيق My Vodafone',
        'أرسل صورة الإيصال'
      ],
      isActive: true
    },
    {
      id: '2',
      type: 'bank',
      name: 'البنك الأهلي المصري',
      accountNumber: '****************',
      accountName: 'مركز البدوي للتجارة',
      instructions: [
        'حول إلى رقم الحساب المذكور',
        'أرسل صورة الإيصال',
        'انتظر التأكيد خلال 24 ساعة'
      ],
      isActive: true
    }
  ],
  socialMedia: [
    {
      id: '1',
      platform: 'facebook',
      name: 'مركز البدوي',
      url: 'https://facebook.com/albadawi-center',
      isActive: true
    },
    {
      id: '2',
      platform: 'instagram',
      name: '@albadawi_center',
      url: 'https://instagram.com/albadawi_center',
      isActive: true
    },
    {
      id: '3',
      platform: 'whatsapp',
      name: 'واتساب الأعمال',
      url: 'https://wa.me/************',
      isActive: true
    }
  ],
  businessInfo: {
    name: 'مركز البدوي',
    slogan: 'اسم له تاريخ',
    description: 'مركز تجاري متكامل يقدم أفضل المنتجات والخدمات',
    workingHours: 'من 9 صباحاً إلى 9 مساءً',
    location: 'القاهرة، مصر'
  }
}

const SiteSettingsContext = createContext<SiteSettingsContextType | undefined>(undefined)

export function SiteSettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<SiteSettings>(defaultSettings)

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('siteSettings')
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings)
        setSettings({ ...defaultSettings, ...parsedSettings })
      } catch (error) {
        console.error('Error parsing site settings:', error)
      }
    }
  }, [])

  // Save settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('siteSettings', JSON.stringify(settings))
  }, [settings])

  const updateContactInfo = (contactInfo: ContactInfo[]) => {
    setSettings(prev => ({ ...prev, contactInfo }))
  }

  const updatePaymentInfo = (paymentInfo: PaymentInfo[]) => {
    setSettings(prev => ({ ...prev, paymentInfo }))
  }

  const updateSocialMedia = (socialMedia: SocialMedia[]) => {
    setSettings(prev => ({ ...prev, socialMedia }))
  }

  const updateBusinessInfo = (businessInfo: Partial<SiteSettings['businessInfo']>) => {
    setSettings(prev => ({
      ...prev,
      businessInfo: { ...prev.businessInfo, ...businessInfo }
    }))
  }

  const addContactInfo = (contact: Omit<ContactInfo, 'id'>) => {
    const newContact: ContactInfo = {
      ...contact,
      id: Date.now().toString()
    }
    setSettings(prev => ({
      ...prev,
      contactInfo: [...prev.contactInfo, newContact]
    }))
  }

  const addPaymentInfo = (payment: Omit<PaymentInfo, 'id'>) => {
    const newPayment: PaymentInfo = {
      ...payment,
      id: Date.now().toString()
    }
    setSettings(prev => ({
      ...prev,
      paymentInfo: [...prev.paymentInfo, newPayment]
    }))
  }

  const addSocialMedia = (social: Omit<SocialMedia, 'id'>) => {
    const newSocial: SocialMedia = {
      ...social,
      id: Date.now().toString()
    }
    setSettings(prev => ({
      ...prev,
      socialMedia: [...prev.socialMedia, newSocial]
    }))
  }

  const removeContactInfo = (id: string) => {
    setSettings(prev => ({
      ...prev,
      contactInfo: prev.contactInfo.filter(item => item.id !== id)
    }))
  }

  const removePaymentInfo = (id: string) => {
    setSettings(prev => ({
      ...prev,
      paymentInfo: prev.paymentInfo.filter(item => item.id !== id)
    }))
  }

  const removeSocialMedia = (id: string) => {
    setSettings(prev => ({
      ...prev,
      socialMedia: prev.socialMedia.filter(item => item.id !== id)
    }))
  }

  const value: SiteSettingsContextType = {
    settings,
    updateContactInfo,
    updatePaymentInfo,
    updateSocialMedia,
    updateBusinessInfo,
    addContactInfo,
    addPaymentInfo,
    addSocialMedia,
    removeContactInfo,
    removePaymentInfo,
    removeSocialMedia
  }

  return (
    <SiteSettingsContext.Provider value={value}>
      {children}
    </SiteSettingsContext.Provider>
  )
}

export function useSiteSettings() {
  const context = useContext(SiteSettingsContext)
  if (context === undefined) {
    throw new Error('useSiteSettings must be used within a SiteSettingsProvider')
  }
  return context
}
