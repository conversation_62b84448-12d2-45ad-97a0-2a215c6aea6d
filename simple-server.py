#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم HTTP بسيط لعرض موقع كوبرا
Simple HTTP Server for Cobra Store
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# إعدادات الخادم
PORT = 3000
HOST = "localhost"

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.path = '/preview.html'
        return super().do_GET()

def main():
    print("🐍 متجر كوبرا - خادم HTTP بسيط")
    print("🐍 Cobra Store - Simple HTTP Server")
    print("=" * 50)
    
    # التحقق من وجود ملف preview.html
    if not Path("preview.html").exists():
        print("❌ ملف preview.html غير موجود!")
        print("❌ preview.html file not found!")
        print("يرجى التأكد من وجود الملف في نفس المجلد")
        print("Please make sure the file exists in the same directory")
        sys.exit(1)
    
    try:
        # إنشاء الخادم
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ الخادم يعمل على: http://{HOST}:{PORT}")
            print(f"✅ Server running on: http://{HOST}:{PORT}")
            print()
            print("🌐 فتح المتصفح تلقائياً...")
            print("🌐 Opening browser automatically...")
            print()
            print("اضغط Ctrl+C لإيقاف الخادم")
            print("Press Ctrl+C to stop the server")
            print("=" * 50)
            
            # فتح المتصفح
            webbrowser.open(f"http://{HOST}:{PORT}")
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 10048:  # Address already in use
            print(f"❌ المنفذ {PORT} مستخدم بالفعل!")
            print(f"❌ Port {PORT} is already in use!")
            print("جرب منفذ آخر أو أغلق التطبيق الذي يستخدم هذا المنفذ")
            print("Try another port or close the application using this port")
        else:
            print(f"❌ خطأ في الخادم: {e}")
            print(f"❌ Server error: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
