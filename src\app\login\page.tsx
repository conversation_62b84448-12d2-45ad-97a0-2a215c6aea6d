'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Eye, EyeOff, Mail, Lock, LogIn } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { toast } from 'react-hot-toast'

export default function LoginPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const { login } = useAuth()
  const { t } = useLanguage()
  const { currentTheme } = useTheme()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const result = await login(formData.email, formData.password)
      
      if (result.success) {
        toast.success(result.message)
        router.push('/')
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع')
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div 
      className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8"
      style={{ backgroundColor: currentTheme.colors.background }}
    >
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div 
            className="mx-auto h-16 w-16 rounded-full flex items-center justify-center mb-4"
            style={{ backgroundColor: currentTheme.colors.primary }}
          >
            <LogIn className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold" style={{ color: currentTheme.colors.primary }}>
            {t('login')}
          </h2>
          <p className="mt-2 text-sm" style={{ color: currentTheme.colors.textSecondary }}>
            مرحباً بك في {t('companyName')}
          </p>
        </div>

        {/* Form */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Email */}
            <div>
              <label htmlFor="email" className="sr-only">
                {t('email')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-3 pl-10 border rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                  style={{
                    backgroundColor: currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  placeholder={t('email')}
                  onFocus={(e) => {
                    e.target.style.borderColor = currentTheme.colors.primary
                    e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
              </div>
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="sr-only">
                كلمة المرور
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-3 pl-10 pr-10 border rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                  style={{
                    backgroundColor: currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  placeholder="كلمة المرور"
                  onFocus={(e) => {
                    e.target.style.borderColor = currentTheme.colors.primary
                    e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                  ) : (
                    <Eye className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Remember me & Forgot password */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 focus:ring-2"
                style={{ 
                  accentColor: currentTheme.colors.primary,
                  borderColor: currentTheme.colors.border
                }}
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm" style={{ color: currentTheme.colors.text }}>
                تذكرني
              </label>
            </div>

            <div className="text-sm">
              <Link
                href="/forgot-password"
                className="font-medium hover:underline"
                style={{ color: currentTheme.colors.primary }}
              >
                نسيت كلمة المرور؟
              </Link>
            </div>
          </div>

          {/* Submit button */}
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              style={{ 
                backgroundColor: currentTheme.colors.primary,
                ':hover': { backgroundColor: currentTheme.colors.secondary }
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.secondary
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.primary
                }
              }}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  جاري تسجيل الدخول...
                </div>
              ) : (
                <div className="flex items-center">
                  <LogIn className="h-4 w-4 mr-2" />
                  {t('login')}
                </div>
              )}
            </button>
          </div>

          {/* Register link */}
          <div className="text-center">
            <p className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
              ليس لديك حساب؟{' '}
              <Link
                href="/register"
                className="font-medium hover:underline"
                style={{ color: currentTheme.colors.primary }}
              >
                {t('register')}
              </Link>
            </p>
          </div>
        </form>

        {/* Demo accounts */}
        <div 
          className="mt-6 p-4 rounded-lg border"
          style={{ 
            backgroundColor: currentTheme.colors.surface,
            borderColor: currentTheme.colors.border
          }}
        >
          <h3 className="text-sm font-medium mb-2" style={{ color: currentTheme.colors.primary }}>
            حسابات تجريبية:
          </h3>
          <div className="text-xs space-y-1" style={{ color: currentTheme.colors.textSecondary }}>
            <p>عميل عادي: <EMAIL> / password123</p>
            <p>تاجر: <EMAIL> / password123</p>
          </div>
        </div>
      </div>
    </div>
  )
}
