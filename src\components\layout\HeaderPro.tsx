'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  Search,
  ShoppingCart,
  User,
  Menu,
  X,
  Bell,
  Heart,
  Phone,
  MessageCircle,
  ChevronDown,
  Sun,
  Moon
} from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { useAuth } from '@/contexts/AuthContext'
import LanguageSettings from '@/components/settings/LanguageSettings'

export default function HeaderPro() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [cartItemsCount, setCartItemsCount] = useState(0)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)

  const { t } = useLanguage()
  const { currentTheme, toggleDarkMode } = useTheme()
  const { user, isAuthenticated, logout } = useAuth()

  // Load cart items count
  useEffect(() => {
    const updateCartCount = () => {
      const cartItems = JSON.parse(localStorage.getItem('cart') || '[]')
      setCartItemsCount(cartItems.reduce((total: number, item: any) => total + item.quantity, 0))
    }

    updateCartCount()
    window.addEventListener('storage', updateCartCount)
    return () => window.removeEventListener('storage', updateCartCount)
  }, [])

  return (
    <header
      className="sticky top-0 z-40 shadow-lg border-b"
      style={{
        backgroundColor: currentTheme.colors.background,
        borderColor: currentTheme.colors.border
      }}
    >
      {/* Top Contact Bar */}
      <div
        className="py-2 text-sm"
        style={{
          backgroundColor: currentTheme.colors.primary,
          color: currentTheme.colors.background
        }}
      >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4 text-xs">
              <span className="flex items-center gap-1">
                <Phone className="w-3 h-3" />
                +20 ************
              </span>
              <span className="flex items-center gap-1">
                <MessageCircle className="w-3 h-3" />
                {t('whatsapp')}: +20 ************
              </span>
            </div>
            <div className="text-xs font-medium" style={{ color: currentTheme.colors.accent }}>
              {t('companySlogan')}
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">

          {/* Logo */}
          <Link href="/" className="flex items-center gap-3 flex-shrink-0">
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center font-bold text-lg shadow-md"
              style={{
                backgroundColor: currentTheme.colors.primary,
                color: currentTheme.colors.background
              }}
            >
              ب
            </div>
            <div className="hidden sm:block">
              <div
                className="font-bold text-lg leading-tight"
                style={{ color: currentTheme.colors.primary }}
              >
                {t('companyName')}
              </div>
              <div
                className="text-xs leading-tight"
                style={{ color: currentTheme.colors.textSecondary }}
              >
                {t('companySlogan')}
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-6 space-x-reverse">
            <Link
              href="/"
              className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-opacity-10"
              style={{ color: currentTheme.colors.text }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = currentTheme.colors.primary + '10'
                e.currentTarget.style.color = currentTheme.colors.primary
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = currentTheme.colors.text
              }}
            >
              <span className="text-sm font-medium">{t('home')}</span>
            </Link>
            <Link
              href="/products"
              className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200"
              style={{ color: currentTheme.colors.text }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = currentTheme.colors.primary + '10'
                e.currentTarget.style.color = currentTheme.colors.primary
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = currentTheme.colors.text
              }}
            >
              <span className="text-sm font-medium">{t('products')}</span>
            </Link>
            <Link
              href="/services"
              className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200"
              style={{ color: currentTheme.colors.text }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = currentTheme.colors.primary + '10'
                e.currentTarget.style.color = currentTheme.colors.primary
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = currentTheme.colors.text
              }}
            >
              <span className="text-sm font-medium">{t('services')}</span>
            </Link>
            <Link
              href="/about"
              className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200"
              style={{ color: currentTheme.colors.text }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = currentTheme.colors.primary + '10'
                e.currentTarget.style.color = currentTheme.colors.primary
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = currentTheme.colors.text
              }}
            >
              <span className="text-sm font-medium">{t('about')}</span>
            </Link>
            <Link
              href="/contact"
              className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200"
              style={{ color: currentTheme.colors.text }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = currentTheme.colors.primary + '10'
                e.currentTarget.style.color = currentTheme.colors.primary
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = currentTheme.colors.text
              }}
            >
              <span className="text-sm font-medium">{t('contact')}</span>
            </Link>
          </nav>

          {/* Search Bar - Desktop */}
          <div className="hidden md:block flex-1 max-w-md mx-6">
            <div className="relative">
              <input
                type="text"
                placeholder={t('searchProducts')}
                className="w-full px-4 py-2 pr-10 rounded-lg border focus:outline-none focus:ring-2 transition-all"
                style={{
                  backgroundColor: currentTheme.colors.surface,
                  borderColor: currentTheme.colors.border,
                  color: currentTheme.colors.text
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = currentTheme.colors.primary
                  e.currentTarget.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = currentTheme.colors.border
                  e.currentTarget.style.boxShadow = 'none'
                }}
              />
              <Search
                className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4"
                style={{ color: currentTheme.colors.textSecondary }}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-3">

            {/* Language Selector */}
            <div className="hidden sm:block">
              <LanguageSettings />
            </div>

            {/* Theme Toggle */}
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-lg transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: currentTheme.colors.surface,
                color: currentTheme.colors.text
              }}
              title={currentTheme.isDark ? t('lightMode') : t('darkMode')}
            >
              {currentTheme.isDark ? (
                <Sun className="w-4 h-4" />
              ) : (
                <Moon className="w-4 h-4" />
              )}
            </button>

            {/* Favorites */}
            <Link
              href="/favorites"
              className="relative p-2 rounded-lg transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: currentTheme.colors.surface,
                color: currentTheme.colors.text
              }}
              title={t('addToFavorites')}
            >
              <Heart className="w-4 h-4" />
            </Link>

            {/* Cart */}
            <Link
              href="/cart"
              className="relative p-2 rounded-lg transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: currentTheme.colors.surface,
                color: currentTheme.colors.text
              }}
              title={t('cart')}
            >
              <ShoppingCart className="w-4 h-4" />
              {cartItemsCount > 0 && (
                <span
                  className="absolute -top-1 -right-1 w-5 h-5 rounded-full text-xs flex items-center justify-center font-bold"
                  style={{
                    backgroundColor: currentTheme.colors.error,
                    color: currentTheme.colors.background
                  }}
                >
                  {cartItemsCount > 99 ? '99+' : cartItemsCount}
                </span>
              )}
            </Link>

            {/* Notifications */}
            <button
              className="relative p-2 rounded-lg transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: currentTheme.colors.surface,
                color: currentTheme.colors.text
              }}
              title={t('notifications')}
            >
              <Bell className="w-4 h-4" />
              <span
                className="absolute -top-1 -right-1 w-5 h-5 rounded-full text-xs flex items-center justify-center font-bold"
                style={{
                  backgroundColor: currentTheme.colors.warning,
                  color: currentTheme.colors.background
                }}
              >
                3
              </span>
            </button>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center gap-2 p-2 rounded-lg transition-all duration-200 hover:scale-105"
                style={{
                  backgroundColor: currentTheme.colors.surface,
                  color: currentTheme.colors.text
                }}
              >
                <User className="w-4 h-4" />
                {isAuthenticated && user && (
                  <span className="hidden sm:block text-sm font-medium max-w-20 truncate">
                    {user.name}
                  </span>
                )}
                <ChevronDown className={`w-3 h-3 transition-transform ${isUserMenuOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* User Dropdown */}
              {isUserMenuOpen && (
                <>
                  <div
                    className="fixed inset-0 z-10"
                    onClick={() => setIsUserMenuOpen(false)}
                  />
                  <div
                    className="absolute top-full right-0 mt-2 w-48 rounded-lg shadow-lg border z-20"
                    style={{
                      backgroundColor: currentTheme.colors.background,
                      borderColor: currentTheme.colors.border
                    }}
                  >
                    <div className="p-2">
                      {isAuthenticated ? (
                        <>
                          {/* User Info */}
                          <div className="px-3 py-2 border-b" style={{ borderColor: currentTheme.colors.border }}>
                            <p className="text-sm font-medium" style={{ color: currentTheme.colors.text }}>
                              {user?.name}
                            </p>
                            <p className="text-xs" style={{ color: currentTheme.colors.textSecondary }}>
                              {user?.email}
                            </p>
                            {user?.userType === 'merchant' && (
                              <span
                                className="inline-block px-2 py-1 text-xs rounded-full mt-1"
                                style={{
                                  backgroundColor: currentTheme.colors.primary + '20',
                                  color: currentTheme.colors.primary
                                }}
                              >
                                تاجر
                              </span>
                            )}
                          </div>

                          {/* Profile Link */}
                          <Link
                            href="/profile"
                            onClick={() => setIsUserMenuOpen(false)}
                            className="flex items-center gap-2 w-full px-3 py-2 text-sm rounded-lg transition-colors"
                            style={{ color: currentTheme.colors.text }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = currentTheme.colors.surface
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent'
                            }}
                          >
                            <User className="w-4 h-4" />
                            {t('profile')}
                          </Link>

                          {/* Logout Button */}
                          <button
                            onClick={() => {
                              logout()
                              setIsUserMenuOpen(false)
                            }}
                            className="flex items-center gap-2 w-full px-3 py-2 text-sm rounded-lg transition-colors"
                            style={{ color: currentTheme.colors.error }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = currentTheme.colors.surface
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent'
                            }}
                          >
                            <User className="w-4 h-4" />
                            {t('logout')}
                          </button>
                        </>
                      ) : (
                        <>
                          {/* Login Link */}
                          <Link
                            href="/login"
                            onClick={() => setIsUserMenuOpen(false)}
                            className="flex items-center gap-2 w-full px-3 py-2 text-sm rounded-lg transition-colors"
                            style={{ color: currentTheme.colors.text }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = currentTheme.colors.surface
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent'
                            }}
                          >
                            <User className="w-4 h-4" />
                            {t('login')}
                          </Link>

                          {/* Register Link */}
                          <Link
                            href="/register"
                            onClick={() => setIsUserMenuOpen(false)}
                            className="flex items-center gap-2 w-full px-3 py-2 text-sm rounded-lg transition-colors"
                            style={{ color: currentTheme.colors.primary }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = currentTheme.colors.surface
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent'
                            }}
                          >
                            <User className="w-4 h-4" />
                            {t('register')}
                          </Link>
                        </>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 rounded-lg transition-all duration-200"
              style={{
                backgroundColor: currentTheme.colors.surface,
                color: currentTheme.colors.text
              }}
            >
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Search Bar */}
        <div className="md:hidden px-4 pb-4">
          <div className="relative">
            <input
              type="text"
              placeholder={t('searchProducts')}
              className="w-full px-4 py-3 pr-10 rounded-lg border focus:outline-none focus:ring-2 transition-all"
              style={{
                backgroundColor: currentTheme.colors.surface,
                borderColor: currentTheme.colors.border,
                color: currentTheme.colors.text
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = currentTheme.colors.primary
                e.currentTarget.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = currentTheme.colors.border
                e.currentTarget.style.boxShadow = 'none'
              }}
            />
            <Search
              className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5"
              style={{ color: currentTheme.colors.textSecondary }}
            />
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div
          className="lg:hidden border-t"
          style={{
            backgroundColor: currentTheme.colors.surface,
            borderColor: currentTheme.colors.border
          }}
        >
          <div className="container mx-auto px-4 py-4">
            <nav className="space-y-2">
              <Link
                href="/"
                onClick={() => setIsMenuOpen(false)}
                className="flex items-center gap-3 px-3 py-3 rounded-lg transition-colors"
                style={{ color: currentTheme.colors.text }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.background
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <span className="font-medium">{t('home')}</span>
              </Link>
              <Link
                href="/products"
                onClick={() => setIsMenuOpen(false)}
                className="flex items-center gap-3 px-3 py-3 rounded-lg transition-colors"
                style={{ color: currentTheme.colors.text }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.background
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <span className="font-medium">{t('products')}</span>
              </Link>
              <Link
                href="/services"
                onClick={() => setIsMenuOpen(false)}
                className="flex items-center gap-3 px-3 py-3 rounded-lg transition-colors"
                style={{ color: currentTheme.colors.text }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.background
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <span className="font-medium">{t('services')}</span>
              </Link>
              <Link
                href="/about"
                onClick={() => setIsMenuOpen(false)}
                className="flex items-center gap-3 px-3 py-3 rounded-lg transition-colors"
                style={{ color: currentTheme.colors.text }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.background
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <span className="font-medium">{t('about')}</span>
              </Link>
              <Link
                href="/contact"
                onClick={() => setIsMenuOpen(false)}
                className="flex items-center gap-3 px-3 py-3 rounded-lg transition-colors"
                style={{ color: currentTheme.colors.text }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.background
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <span className="font-medium">{t('contact')}</span>
              </Link>
            </nav>

            {/* Mobile Language Selector */}
            <div className="mt-4 pt-4 border-t" style={{ borderColor: currentTheme.colors.border }}>
              <LanguageSettings />
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
