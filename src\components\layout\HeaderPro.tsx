'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  Search, 
  ShoppingCart, 
  User, 
  Menu, 
  X, 
  Bell,
  Heart,
  Globe,
  Palette,
  Phone,
  MessageCircle,
  ChevronDown,
  Home,
  Package,
  Wrench,
  Info,
  Mail
} from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import LanguageSettings from '@/components/settings/LanguageSettings'
import SearchBar from '@/components/search/SearchBar'

export default function HeaderPro() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [cartItemsCount, setCartItemsCount] = useState(0)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)
  
  const { t, currentLanguage } = useLanguage()
  const { currentTheme, toggleDarkMode } = useTheme()

  // Load cart items count
  useEffect(() => {
    const updateCartCount = () => {
      const cartItems = JSON.parse(localStorage.getItem('cart') || '[]')
      setCartItemsCount(cartItems.reduce((total: number, item: any) => total + item.quantity, 0))
    }

    updateCartCount()
    window.addEventListener('storage', updateCartCount)
    return () => window.removeEventListener('storage', updateCartCount)
  }, [])

  const navigationItems = [
    { href: '/', label: t('home'), icon: Home },
    { href: '/products', label: t('products'), icon: Package },
    { href: '/services', label: t('services'), icon: Wrench },
    { href: '/about', label: t('about'), icon: Info },
    { href: '/contact', label: t('contact'), icon: Mail }
  ]

  const quickActions = [
    { 
      icon: Heart, 
      label: t('addToFavorites'), 
      count: 0, 
      href: '/favorites',
      color: 'text-red-500'
    },
    { 
      icon: ShoppingCart, 
      label: t('cart'), 
      count: cartItemsCount, 
      href: '/cart',
      color: 'text-blue-500'
    },
    { 
      icon: Bell, 
      label: t('notifications'), 
      count: 3, 
      onClick: () => setIsNotificationsOpen(!isNotificationsOpen),
      color: 'text-yellow-500'
    }
  ]

  return (
    <>
      {/* Top Contact Bar */}
      <div 
        className="py-2 text-sm border-b"
        style={{ 
          backgroundColor: currentTheme.colors.primary,
          color: currentTheme.colors.background,
          borderColor: currentTheme.colors.border
        }}
      >
        <div className="container mx-auto px-4">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
            <div className="flex items-center gap-4 text-xs sm:text-sm">
              <span className="flex items-center gap-1">
                <Phone className="w-3 h-3" />
                +20 ************
              </span>
              <span className="flex items-center gap-1">
                <MessageCircle className="w-3 h-3" />
                {t('whatsapp')}: +20 ************
              </span>
            </div>
            <div className="text-xs sm:text-sm font-medium" style={{ color: currentTheme.colors.accent }}>
              {t('companyName')} - {t('companySlogan')}
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header 
        className="sticky top-0 z-40 shadow-sm"
        style={{ backgroundColor: currentTheme.colors.background }}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            
            {/* Logo */}
            <Link href="/" className="flex items-center gap-2 flex-shrink-0">
              <div 
                className="w-10 h-10 rounded-lg flex items-center justify-center font-bold text-lg"
                style={{ 
                  backgroundColor: currentTheme.colors.primary,
                  color: currentTheme.colors.background
                }}
              >
                ب
              </div>
              <div className="hidden sm:block">
                <div 
                  className="font-bold text-lg"
                  style={{ color: currentTheme.colors.primary }}
                >
                  {t('companyName')}
                </div>
                <div 
                  className="text-xs"
                  style={{ color: currentTheme.colors.textSecondary }}
                >
                  {t('companySlogan')}
                </div>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1 space-x-reverse">
              {navigationItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-105"
                    style={{ 
                      color: currentTheme.colors.text,
                      ':hover': { backgroundColor: currentTheme.colors.surface }
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = currentTheme.colors.surface
                      e.currentTarget.style.color = currentTheme.colors.primary
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent'
                      e.currentTarget.style.color = currentTheme.colors.text
                    }}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{item.label}</span>
                  </Link>
                )
              })}
            </nav>

            {/* Search Bar - Desktop */}
            <div className="hidden md:block flex-1 max-w-md mx-4">
              <SearchBar 
                placeholder={t('searchProducts')}
                showVoiceSearch={true}
                showImageSearch={false}
              />
            </div>

            {/* Quick Actions */}
            <div className="flex items-center gap-2">
              
              {/* Language Selector */}
              <div className="hidden sm:block">
                <LanguageSettings />
              </div>

              {/* Theme Toggle */}
              <button
                onClick={toggleDarkMode}
                className="p-2 rounded-lg transition-all duration-200 hover:scale-110"
                style={{ 
                  backgroundColor: currentTheme.colors.surface,
                  color: currentTheme.colors.text
                }}
                title={currentTheme.isDark ? t('lightMode') : t('darkMode')}
              >
                <Palette className="w-4 h-4" />
              </button>

              {/* Quick Action Buttons */}
              {quickActions.map((action, index) => {
                const Icon = action.icon
                const isButton = !!action.onClick
                const Component = isButton ? 'button' : Link
                const props = isButton 
                  ? { onClick: action.onClick }
                  : { href: action.href }

                return (
                  <Component
                    key={index}
                    {...props}
                    className="relative p-2 rounded-lg transition-all duration-200 hover:scale-110"
                    style={{ 
                      backgroundColor: currentTheme.colors.surface,
                      color: currentTheme.colors.text
                    }}
                    title={action.label}
                  >
                    <Icon className={`w-4 h-4 ${action.color}`} />
                    {action.count > 0 && (
                      <span 
                        className="absolute -top-1 -right-1 w-5 h-5 rounded-full text-xs flex items-center justify-center font-bold"
                        style={{ 
                          backgroundColor: currentTheme.colors.error,
                          color: currentTheme.colors.background
                        }}
                      >
                        {action.count > 99 ? '99+' : action.count}
                      </span>
                    )}
                  </Component>
                )
              })}

              {/* User Menu */}
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center gap-2 p-2 rounded-lg transition-all duration-200 hover:scale-105"
                  style={{ 
                    backgroundColor: currentTheme.colors.surface,
                    color: currentTheme.colors.text
                  }}
                >
                  <User className="w-4 h-4" />
                  <ChevronDown className={`w-3 h-3 transition-transform ${isUserMenuOpen ? 'rotate-180' : ''}`} />
                </button>

                {/* User Dropdown */}
                {isUserMenuOpen && (
                  <>
                    <div 
                      className="fixed inset-0 z-10" 
                      onClick={() => setIsUserMenuOpen(false)}
                    />
                    <div 
                      className="absolute top-full right-0 mt-2 w-48 rounded-lg shadow-lg border z-20"
                      style={{ 
                        backgroundColor: currentTheme.colors.background,
                        borderColor: currentTheme.colors.border
                      }}
                    >
                      <div className="p-2">
                        <Link
                          href="/profile"
                          className="flex items-center gap-2 w-full px-3 py-2 text-sm rounded-lg transition-colors"
                          style={{ color: currentTheme.colors.text }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = currentTheme.colors.surface
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent'
                          }}
                        >
                          <User className="w-4 h-4" />
                          {t('profile')}
                        </Link>
                        <button
                          className="flex items-center gap-2 w-full px-3 py-2 text-sm rounded-lg transition-colors"
                          style={{ color: currentTheme.colors.text }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = currentTheme.colors.surface
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent'
                          }}
                        >
                          <User className="w-4 h-4" />
                          {t('login')}
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden p-2 rounded-lg transition-all duration-200"
                style={{ 
                  backgroundColor: currentTheme.colors.surface,
                  color: currentTheme.colors.text
                }}
              >
                {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Mobile Search Bar */}
          <div className="md:hidden pb-4">
            <SearchBar 
              placeholder={t('searchProducts')}
              showVoiceSearch={true}
              showImageSearch={false}
            />
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div 
            className="lg:hidden border-t"
            style={{ 
              backgroundColor: currentTheme.colors.surface,
              borderColor: currentTheme.colors.border
            }}
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="space-y-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={() => setIsMenuOpen(false)}
                      className="flex items-center gap-3 px-3 py-3 rounded-lg transition-colors"
                      style={{ color: currentTheme.colors.text }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = currentTheme.colors.background
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent'
                      }}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{item.label}</span>
                    </Link>
                  )
                })}
              </nav>

              {/* Mobile Language Selector */}
              <div className="mt-4 pt-4 border-t" style={{ borderColor: currentTheme.colors.border }}>
                <LanguageSettings />
              </div>
            </div>
          </div>
        )}
      </header>
    </>
  )
}
