'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { initializeDemoUsers } from '@/lib/demo-users'

export interface User {
  id: string
  name: string
  email: string
  phone?: string
  userType: 'regular' | 'merchant'
  isWholesaleApproved?: boolean
  createdAt: string
}

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<{ success: boolean; message: string }>
  register: (userData: RegisterData) => Promise<{ success: boolean; message: string }>
  logout: () => void
  updateProfile: (userData: Partial<User>) => Promise<{ success: boolean; message: string }>
}

interface RegisterData {
  name: string
  email: string
  password: string
  phone?: string
  userType: 'regular' | 'merchant'
  merchantCode?: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Load user from localStorage on mount
  useEffect(() => {
    // Initialize demo users
    initializeDemoUsers()

    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser)
        setUser(userData)
        setIsAuthenticated(true)
      } catch (error) {
        console.error('Error parsing saved user data:', error)
        localStorage.removeItem('user')
      }
    }
  }, [])

  const login = async (email: string, password: string): Promise<{ success: boolean; message: string }> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Get users from localStorage
      const users = JSON.parse(localStorage.getItem('users') || '[]')
      const foundUser = users.find((u: any) => u.email === email && u.password === password)

      if (foundUser) {
        const { password: _, ...userWithoutPassword } = foundUser
        setUser(userWithoutPassword)
        setIsAuthenticated(true)
        localStorage.setItem('user', JSON.stringify(userWithoutPassword))
        return { success: true, message: 'تم تسجيل الدخول بنجاح' }
      } else {
        return { success: false, message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' }
      }
    } catch (error) {
      return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' }
    }
  }

  const register = async (userData: RegisterData): Promise<{ success: boolean; message: string }> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Get existing users
      const users = JSON.parse(localStorage.getItem('users') || '[]')
      
      // Check if email already exists
      if (users.find((u: any) => u.email === userData.email)) {
        return { success: false, message: 'البريد الإلكتروني مستخدم بالفعل' }
      }

      // Validate merchant code if user type is merchant
      if (userData.userType === 'merchant') {
        const validMerchantCode = 'BADAWI2024' // This should come from admin settings
        if (userData.merchantCode !== validMerchantCode) {
          return { success: false, message: 'كود التاجر غير صحيح' }
        }
      }

      // Create new user
      const newUser: User = {
        id: Date.now().toString(),
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        userType: userData.userType,
        isWholesaleApproved: userData.userType === 'merchant',
        createdAt: new Date().toISOString()
      }

      // Save user with password
      const userWithPassword = { ...newUser, password: userData.password }
      users.push(userWithPassword)
      localStorage.setItem('users', JSON.stringify(users))

      // Set current user (without password)
      setUser(newUser)
      setIsAuthenticated(true)
      localStorage.setItem('user', JSON.stringify(newUser))

      return { success: true, message: 'تم إنشاء الحساب بنجاح' }
    } catch (error) {
      return { success: false, message: 'حدث خطأ أثناء إنشاء الحساب' }
    }
  }

  const logout = () => {
    setUser(null)
    setIsAuthenticated(false)
    localStorage.removeItem('user')
  }

  const updateProfile = async (userData: Partial<User>): Promise<{ success: boolean; message: string }> => {
    try {
      if (!user) {
        return { success: false, message: 'يجب تسجيل الدخول أولاً' }
      }

      // Update user data
      const updatedUser = { ...user, ...userData }
      setUser(updatedUser)
      localStorage.setItem('user', JSON.stringify(updatedUser))

      // Update in users array
      const users = JSON.parse(localStorage.getItem('users') || '[]')
      const userIndex = users.findIndex((u: any) => u.id === user.id)
      if (userIndex !== -1) {
        users[userIndex] = { ...users[userIndex], ...userData }
        localStorage.setItem('users', JSON.stringify(users))
      }

      return { success: true, message: 'تم تحديث الملف الشخصي بنجاح' }
    } catch (error) {
      return { success: false, message: 'حدث خطأ أثناء تحديث الملف الشخصي' }
    }
  }

  const value: AuthContextType = {
    user,
    isAuthenticated,
    login,
    register,
    logout,
    updateProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
