'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { User, Mail, Phone, Calendar, Shield, Edit, Save, X } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { toast } from 'react-hot-toast'

export default function ProfilePage() {
  const { user, isAuthenticated, updateProfile } = useAuth()
  const { t } = useLanguage()
  const { currentTheme } = useTheme()
  const router = useRouter()

  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: ''
  })
  const [isLoading, setIsLoading] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        phone: user.phone || ''
      })
    }
  }, [isAuthenticated, user, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const result = await updateProfile(formData)
      
      if (result.success) {
        toast.success(result.message)
        setIsEditing(false)
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع')
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleCancel = () => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        phone: user.phone || ''
      })
    }
    setIsEditing(false)
  }

  if (!isAuthenticated || !user) {
    return (
      <div 
        className="min-h-screen flex items-center justify-center"
        style={{ backgroundColor: currentTheme.colors.background }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" 
               style={{ borderColor: currentTheme.colors.primary }}></div>
          <p style={{ color: currentTheme.colors.text }}>جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <div 
      className="min-h-screen py-12 px-4 sm:px-6 lg:px-8"
      style={{ backgroundColor: currentTheme.colors.background }}
    >
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div 
            className="mx-auto h-20 w-20 rounded-full flex items-center justify-center mb-4"
            style={{ backgroundColor: currentTheme.colors.primary }}
          >
            <User className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold" style={{ color: currentTheme.colors.primary }}>
            {t('profile')}
          </h1>
          <p className="mt-2 text-sm" style={{ color: currentTheme.colors.textSecondary }}>
            إدارة معلوماتك الشخصية
          </p>
        </div>

        {/* Profile Card */}
        <div 
          className="rounded-2xl shadow-lg border p-8"
          style={{ 
            backgroundColor: currentTheme.colors.surface,
            borderColor: currentTheme.colors.border
          }}
        >
          {/* User Type Badge */}
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-xl font-bold" style={{ color: currentTheme.colors.text }}>
                معلومات الحساب
              </h2>
            </div>
            <div className="flex items-center gap-2">
              {user.userType === 'merchant' && (
                <span 
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                  style={{ 
                    backgroundColor: currentTheme.colors.primary + '20',
                    color: currentTheme.colors.primary
                  }}
                >
                  <Shield className="w-4 h-4 mr-1" />
                  تاجر معتمد
                </span>
              )}
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105"
                style={{ 
                  backgroundColor: currentTheme.colors.primary,
                  color: 'white'
                }}
              >
                <Edit className="w-4 h-4" />
                {isEditing ? 'إلغاء' : 'تعديل'}
              </button>
            </div>
          </div>

          {/* Profile Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                {t('name')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  name="name"
                  type="text"
                  required
                  disabled={!isEditing}
                  value={formData.name}
                  onChange={handleChange}
                  className="block w-full px-3 py-3 pl-10 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm disabled:opacity-60 disabled:cursor-not-allowed"
                  style={{
                    backgroundColor: isEditing ? currentTheme.colors.background : currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  onFocus={(e) => {
                    if (isEditing) {
                      e.target.style.borderColor = currentTheme.colors.primary
                      e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                    }
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
              </div>
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                {t('email')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  name="email"
                  type="email"
                  required
                  disabled={!isEditing}
                  value={formData.email}
                  onChange={handleChange}
                  className="block w-full px-3 py-3 pl-10 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm disabled:opacity-60 disabled:cursor-not-allowed"
                  style={{
                    backgroundColor: isEditing ? currentTheme.colors.background : currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  onFocus={(e) => {
                    if (isEditing) {
                      e.target.style.borderColor = currentTheme.colors.primary
                      e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                    }
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
              </div>
            </div>

            {/* Phone */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                {t('phoneNumber')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  name="phone"
                  type="tel"
                  disabled={!isEditing}
                  value={formData.phone}
                  onChange={handleChange}
                  className="block w-full px-3 py-3 pl-10 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm disabled:opacity-60 disabled:cursor-not-allowed"
                  style={{
                    backgroundColor: isEditing ? currentTheme.colors.background : currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  placeholder={t('optional')}
                  onFocus={(e) => {
                    if (isEditing) {
                      e.target.style.borderColor = currentTheme.colors.primary
                      e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                    }
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
              </div>
            </div>

            {/* Account Info */}
            <div className="pt-6 border-t" style={{ borderColor: currentTheme.colors.border }}>
              <h3 className="text-lg font-medium mb-4" style={{ color: currentTheme.colors.text }}>
                معلومات الحساب
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1" style={{ color: currentTheme.colors.textSecondary }}>
                    نوع الحساب
                  </label>
                  <p className="text-sm" style={{ color: currentTheme.colors.text }}>
                    {user.userType === 'merchant' ? 'تاجر' : 'عميل عادي'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1" style={{ color: currentTheme.colors.textSecondary }}>
                    تاريخ الانضمام
                  </label>
                  <p className="text-sm flex items-center" style={{ color: currentTheme.colors.text }}>
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(user.createdAt).toLocaleDateString('ar-EG')}
                  </p>
                </div>
              </div>
            </div>

            {/* Save Button */}
            {isEditing && (
              <div className="flex gap-3 pt-6">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 flex justify-center items-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  style={{ backgroundColor: currentTheme.colors.primary }}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      جاري الحفظ...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <Save className="h-4 w-4 mr-2" />
                      {t('save')}
                    </div>
                  )}
                </button>
                <button
                  type="button"
                  onClick={handleCancel}
                  className="flex justify-center items-center py-3 px-4 border text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200"
                  style={{ 
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text,
                    backgroundColor: currentTheme.colors.surface
                  }}
                >
                  <X className="h-4 w-4 mr-2" />
                  {t('cancel')}
                </button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  )
}
