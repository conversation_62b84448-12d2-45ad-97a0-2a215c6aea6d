'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { User, Mail, Phone, MapPin, Edit, Save, X, Plus, Trash2 } from 'lucide-react'
import toast from 'react-hot-toast'
import Link from 'next/link'

interface Address {
  id: string
  title: string
  address: string
  city: string
  district: string
  isDefault: boolean
}

export default function ProfilePage() {
  const { user, updateProfile } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || ''
  })
  
  const [addresses, setAddresses] = useState<Address[]>([
    {
      id: '1',
      title: 'المنزل',
      address: 'شارع الأمير محمد بن عبدالعزيز، مبنى 123',
      city: 'الرياض',
      district: 'النرجس',
      isDefault: true
    },
    {
      id: '2',
      title: 'العمل',
      address: 'طريق الملك فهد، برج الأعمال',
      city: 'الرياض',
      district: 'العليا',
      isDefault: false
    }
  ])

  const [showAddressForm, setShowAddressForm] = useState(false)
  const [newAddress, setNewAddress] = useState({
    title: '',
    address: '',
    city: '',
    district: ''
  })

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <User className="w-24 h-24 text-gray-300 mx-auto mb-6" />
        <h1 className="text-2xl font-bold text-gray-800 mb-4">يرجى تسجيل الدخول</h1>
        <p className="text-gray-600 mb-8">تحتاج إلى تسجيل الدخول لعرض ملفك الشخصي</p>
        <Link
          href="/"
          className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-8 rounded-lg transition-colors duration-200"
        >
          تسجيل الدخول
        </Link>
      </div>
    )
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      await updateProfile(formData)
      setIsEditing(false)
      toast.success('تم تحديث البيانات بنجاح')
    } catch (error) {
      toast.error('حدث خطأ في تحديث البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddAddress = () => {
    if (!newAddress.title || !newAddress.address || !newAddress.city) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const address: Address = {
      id: Date.now().toString(),
      ...newAddress,
      isDefault: addresses.length === 0
    }

    setAddresses([...addresses, address])
    setNewAddress({ title: '', address: '', city: '', district: '' })
    setShowAddressForm(false)
    toast.success('تم إضافة العنوان بنجاح')
  }

  const handleDeleteAddress = (id: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العنوان؟')) {
      setAddresses(addresses.filter(addr => addr.id !== id))
      toast.success('تم حذف العنوان')
    }
  }

  const handleSetDefaultAddress = (id: string) => {
    setAddresses(addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === id
    })))
    toast.success('تم تعيين العنوان الافتراضي')
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">الملف الشخصي</h1>
        <p className="text-gray-600">إدارة بياناتك الشخصية وعناوينك</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Personal Information */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">البيانات الشخصية</h2>
              {!isEditing ? (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2 text-primary-600 hover:text-primary-700"
                >
                  <Edit size={18} />
                  تعديل
                </button>
              ) : (
                <div className="flex gap-2">
                  <button
                    onClick={() => setIsEditing(false)}
                    className="flex items-center gap-2 text-gray-600 hover:text-gray-700"
                  >
                    <X size={18} />
                    إلغاء
                  </button>
                </div>
              )}
            </div>

            {isEditing ? (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الاسم الكامل *
                  </label>
                  <div className="relative">
                    <User className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني *
                  </label>
                  <div className="relative">
                    <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الهاتف
                  </label>
                  <div className="relative">
                    <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="05xxxxxxxx"
                    />
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <Save size={18} />
                      حفظ التغييرات
                    </>
                  )}
                </button>
              </form>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <User className="text-gray-400" size={18} />
                  <div>
                    <p className="text-sm text-gray-600">الاسم</p>
                    <p className="font-medium text-gray-800">{user.name}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Mail className="text-gray-400" size={18} />
                  <div>
                    <p className="text-sm text-gray-600">البريد الإلكتروني</p>
                    <p className="font-medium text-gray-800">{user.email}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Phone className="text-gray-400" size={18} />
                  <div>
                    <p className="text-sm text-gray-600">رقم الهاتف</p>
                    <p className="font-medium text-gray-800">{user.phone || 'غير محدد'}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Addresses Section */}
          <div className="bg-white rounded-lg shadow-md p-6 mt-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">العناوين المحفوظة</h2>
              <button
                onClick={() => setShowAddressForm(true)}
                className="flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-lg transition-colors duration-200"
              >
                <Plus size={18} />
                إضافة عنوان
              </button>
            </div>

            {/* Add Address Form */}
            {showAddressForm && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-gray-800 mb-4">إضافة عنوان جديد</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="عنوان العنوان (مثل: المنزل، العمل)"
                    value={newAddress.title}
                    onChange={(e) => setNewAddress({ ...newAddress, title: e.target.value })}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                  <input
                    type="text"
                    placeholder="المدينة"
                    value={newAddress.city}
                    onChange={(e) => setNewAddress({ ...newAddress, city: e.target.value })}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                  <input
                    type="text"
                    placeholder="الحي"
                    value={newAddress.district}
                    onChange={(e) => setNewAddress({ ...newAddress, district: e.target.value })}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                  <input
                    type="text"
                    placeholder="العنوان التفصيلي"
                    value={newAddress.address}
                    onChange={(e) => setNewAddress({ ...newAddress, address: e.target.value })}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div className="flex gap-2 mt-4">
                  <button
                    onClick={handleAddAddress}
                    className="bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    إضافة
                  </button>
                  <button
                    onClick={() => setShowAddressForm(false)}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            )}

            {/* Addresses List */}
            <div className="space-y-4">
              {addresses.map((address) => (
                <div key={address.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <MapPin className="text-gray-400 mt-1" size={18} />
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-gray-800">{address.title}</h3>
                          {address.isDefault && (
                            <span className="bg-primary-100 text-primary-600 text-xs px-2 py-1 rounded-full">
                              افتراضي
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm">{address.address}</p>
                        <p className="text-gray-500 text-sm">{address.city}, {address.district}</p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {!address.isDefault && (
                        <button
                          onClick={() => handleSetDefaultAddress(address.id)}
                          className="text-primary-600 hover:text-primary-700 text-sm"
                        >
                          تعيين كافتراضي
                        </button>
                      )}
                      <button
                        onClick={() => handleDeleteAddress(address.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">إجراءات سريعة</h3>
            <div className="space-y-3">
              <Link
                href="/orders"
                className="block w-full text-center bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-lg transition-colors duration-200"
              >
                عرض طلباتي
              </Link>
              <Link
                href="/cart"
                className="block w-full text-center bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors duration-200"
              >
                سلة التسوق
              </Link>
              <Link
                href="/contact"
                className="block w-full text-center bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors duration-200"
              >
                تواصل معنا
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">إحصائيات الحساب</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">إجمالي الطلبات</span>
                <span className="font-semibold">12</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">المبلغ المنفق</span>
                <span className="font-semibold">15,750 ريال</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">تاريخ الانضمام</span>
                <span className="font-semibold">يناير 2024</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
