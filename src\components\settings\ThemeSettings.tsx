'use client'

import { useState } from 'react'
import { useTheme, themes } from '@/contexts/ThemeContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { Palette, Sun, Moon, Settings, RotateCcw, Check } from 'lucide-react'

export default function ThemeSettings() {
  const { currentTheme, setTheme, toggleDarkMode, customColors, updateCustomColors, resetCustomColors, isCustomTheme } = useTheme()
  const { t } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [selectedColorKey, setSelectedColorKey] = useState<string>('')

  const colorKeys = [
    { key: 'primary', label: 'اللون الأساسي' },
    { key: 'secondary', label: 'اللون الثانوي' },
    { key: 'accent', label: 'لون التمييز' },
    { key: 'background', label: 'لون الخلفية' },
    { key: 'surface', label: 'لون السطح' },
    { key: 'text', label: 'لون النص' },
    { key: 'border', label: 'لون الحدود' }
  ]

  const handleColorChange = (colorKey: string, color: string) => {
    updateCustomColors({ [colorKey]: color })
  }

  const openColorPicker = (colorKey: string) => {
    setSelectedColorKey(colorKey)
    setShowColorPicker(true)
  }

  return (
    <>
      {/* Theme Settings Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed top-20 left-4 z-40 bg-white dark:bg-gray-800 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700"
        title={t('themeSettings')}
      >
        <Palette className="w-5 h-5 text-gray-600 dark:text-gray-300" />
      </button>

      {/* Theme Settings Panel */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                  <Settings className="w-6 h-6" />
                  {t('themeSettings')}
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <span className="text-2xl text-gray-500 dark:text-gray-400">×</span>
                </button>
              </div>
            </div>

            <div className="p-6 space-y-8">
              {/* Dark Mode Toggle */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">وضع العرض</h3>
                <div className="flex items-center gap-4">
                  <button
                    onClick={toggleDarkMode}
                    className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all ${
                      currentTheme.isDark
                        ? 'bg-gray-800 text-white border-2 border-blue-500'
                        : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:border-gray-300'
                    }`}
                  >
                    <Moon className="w-5 h-5" />
                    {t('darkMode')}
                  </button>
                  <button
                    onClick={toggleDarkMode}
                    className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all ${
                      !currentTheme.isDark
                        ? 'bg-yellow-100 text-yellow-800 border-2 border-yellow-500'
                        : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:border-gray-300'
                    }`}
                  >
                    <Sun className="w-5 h-5" />
                    {t('lightMode')}
                  </button>
                </div>
              </div>

              {/* Predefined Themes */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">الثيمات المحددة مسبقاً</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {themes.map((theme) => (
                    <button
                      key={theme.id}
                      onClick={() => setTheme(theme.id)}
                      className={`p-4 rounded-xl border-2 transition-all hover:scale-105 ${
                        currentTheme.id === theme.id && !isCustomTheme
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-3">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: theme.colors.primary }}
                        />
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: theme.colors.accent }}
                        />
                        {currentTheme.id === theme.id && !isCustomTheme && (
                          <Check className="w-4 h-4 text-blue-500 ml-auto" />
                        )}
                      </div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">{theme.name}</p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Custom Colors */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('customTheme')}</h3>
                  {isCustomTheme && (
                    <button
                      onClick={resetCustomColors}
                      className="flex items-center gap-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    >
                      <RotateCcw className="w-4 h-4" />
                      {t('resetTheme')}
                    </button>
                  )}
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {colorKeys.map(({ key, label }) => (
                    <div key={key} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {label}
                      </label>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => openColorPicker(key)}
                          className="w-10 h-10 rounded-lg border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
                          style={{
                            backgroundColor: customColors[key as keyof typeof customColors] || currentTheme.colors[key as keyof typeof currentTheme.colors]
                          }}
                        />
                        <input
                          type="color"
                          value={customColors[key as keyof typeof customColors] || currentTheme.colors[key as keyof typeof currentTheme.colors]}
                          onChange={(e) => handleColorChange(key, e.target.value)}
                          className="w-8 h-8 rounded border-0 cursor-pointer opacity-0 absolute pointer-events-none"
                        />
                        <input
                          type="text"
                          value={customColors[key as keyof typeof customColors] || currentTheme.colors[key as keyof typeof currentTheme.colors]}
                          onChange={(e) => handleColorChange(key, e.target.value)}
                          className="flex-1 px-3 py-2 text-xs border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="#000000"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Theme Preview */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">معاينة الثيم</h3>
                <div 
                  className="p-6 rounded-xl border-2"
                  style={{
                    backgroundColor: currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                >
                  <div className="space-y-4">
                    <h4 className="text-xl font-bold" style={{ color: currentTheme.colors.primary }}>
                      مركز البدوي
                    </h4>
                    <p style={{ color: currentTheme.colors.textSecondary }}>
                      اسم له تاريخ - الاسم يعني الثقة والجودة
                    </p>
                    <div className="flex gap-2">
                      <button
                        className="px-4 py-2 rounded-lg font-medium"
                        style={{
                          backgroundColor: currentTheme.colors.primary,
                          color: currentTheme.colors.background
                        }}
                      >
                        زر أساسي
                      </button>
                      <button
                        className="px-4 py-2 rounded-lg font-medium border"
                        style={{
                          backgroundColor: currentTheme.colors.accent,
                          color: currentTheme.colors.background,
                          borderColor: currentTheme.colors.border
                        }}
                      >
                        زر ثانوي
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
