'use client'

import { useState, useEffect } from 'react'
import { ShoppingCart, MessageCircle, Phone, Search, ArrowUp, Zap } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function FloatingActionButtons() {
  const [isVisible, setIsVisible] = useState(false)
  const [showScrollTop, setShowScrollTop] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true)
        setShowScrollTop(true)
      } else {
        setIsVisible(false)
        setShowScrollTop(false)
      }
    }

    window.addEventListener('scroll', toggleVisibility)
    return () => window.removeEventListener('scroll', toggleVisibility)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const openWhatsApp = () => {
    const phoneNumber = '+201001234567' // Replace with actual WhatsApp number
    const message = 'مرحباً، أريد الاستفسار عن منتجاتكم وخدماتكم'
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(url, '_blank')
  }

  const openSearch = () => {
    // Trigger global search
    window.dispatchEvent(new CustomEvent('openGlobalSearch'))
  }

  const quickShop = () => {
    router.push('/products')
  }

  const quickCall = () => {
    window.location.href = 'tel:+201001234567'
  }

  if (!isVisible) return null

  return (
    <div className="fixed bottom-6 left-6 z-50 flex flex-col gap-3">
      {/* Quick Shop Button */}
      <button
        onClick={quickShop}
        className="group w-14 h-14 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
        title="تسوق سريع"
      >
        <ShoppingCart className="w-6 h-6 group-hover:animate-bounce" />
      </button>

      {/* Quick Search Button */}
      <button
        onClick={openSearch}
        className="group w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
        title="بحث سريع"
      >
        <Search className="w-6 h-6 group-hover:animate-pulse" />
      </button>

      {/* WhatsApp Button */}
      <button
        onClick={openWhatsApp}
        className="group w-14 h-14 bg-gradient-to-r from-green-400 to-green-500 hover:from-green-500 hover:to-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
        title="تواصل واتساب"
      >
        <MessageCircle className="w-6 h-6 group-hover:animate-bounce" />
      </button>

      {/* Quick Call Button */}
      <button
        onClick={quickCall}
        className="group w-14 h-14 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
        title="اتصال سريع"
      >
        <Phone className="w-6 h-6 group-hover:animate-pulse" />
      </button>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="group w-14 h-14 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
          title="العودة للأعلى"
        >
          <ArrowUp className="w-6 h-6 group-hover:animate-bounce" />
        </button>
      )}

      {/* Floating Tooltip */}
      <div className="absolute right-16 top-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
        <div className="bg-gray-800 text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap">
          إجراءات سريعة
        </div>
      </div>
    </div>
  )
}

// Quick Actions Menu Component
export function QuickActionsMenu() {
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()

  const actions = [
    {
      label: 'تسوق الآن',
      icon: ShoppingCart,
      color: 'from-green-500 to-green-600',
      action: () => router.push('/products')
    },
    {
      label: 'بحث سريع',
      icon: Search,
      color: 'from-blue-500 to-blue-600',
      action: () => window.dispatchEvent(new CustomEvent('openGlobalSearch'))
    },
    {
      label: 'اطلب خدمة',
      icon: Zap,
      color: 'from-purple-500 to-purple-600',
      action: () => router.push('/services')
    },
    {
      label: 'تواصل معنا',
      icon: MessageCircle,
      color: 'from-green-400 to-green-500',
      action: () => {
        const phoneNumber = '+201001234567'
        const message = 'مرحباً، أريد الاستفسار عن منتجاتكم وخدماتكم'
        const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
        window.open(url, '_blank')
      }
    }
  ]

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Action Buttons */}
      <div className={`flex flex-col gap-3 mb-4 transition-all duration-300 ${
        isOpen ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'
      }`}>
        {actions.map((action, index) => {
          const Icon = action.icon
          return (
            <button
              key={index}
              onClick={() => {
                action.action()
                setIsOpen(false)
              }}
              className={`group flex items-center gap-3 bg-gradient-to-r ${action.color} hover:shadow-lg text-white px-4 py-3 rounded-full transition-all duration-300 transform hover:scale-105`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <Icon className="w-5 h-5" />
              <span className="text-sm font-medium whitespace-nowrap">{action.label}</span>
            </button>
          )
        })}
      </div>

      {/* Main Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110 ${
          isOpen ? 'rotate-45' : ''
        }`}
      >
        <Zap className="w-8 h-8" />
      </button>
    </div>
  )
}
