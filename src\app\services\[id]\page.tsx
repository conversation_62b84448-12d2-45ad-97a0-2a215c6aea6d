'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Star, Clock, CheckCircle, Calendar, MapPin, Phone, Smartphone } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import toast from 'react-hot-toast'
import Link from 'next/link'

interface Service {
  id: string
  name: string
  description: string
  longDescription: string
  icon: React.ReactNode
  price: string
  duration: string
  rating: number
  reviews: number
  features: string[]
  category: string
  availability: string
  images: string[]
  process: string[]
  requirements: string[]
}

// Sample service data
const sampleService: Service = {
  id: '1',
  name: 'صيانة الأجهزة الإلكترونية',
  description: 'خدمة صيانة شاملة للهواتف والحاسوب واللابتوب مع ضمان الجودة',
  longDescription: 'نقدم خدمة صيانة احترافية لجميع أنواع الأجهزة الإلكترونية بما في ذلك الهواتف الذكية، أجهزة الكمبيوتر المحمولة، والأجهزة اللوحية. فريقنا من الفنيين المعتمدين يستخدم أحدث الأدوات والتقنيات لضمان إصلاح سريع وموثوق. نوفر قطع غيار أصلية وضمان شامل على جميع أعمال الصيانة.',
  icon: <Smartphone className="w-8 h-8" />,
  price: 'يبدأ من 100 ريال',
  duration: '2-4 ساعات',
  rating: 4.9,
  reviews: 234,
  features: [
    'تشخيص مجاني للمشكلة',
    'ضمان 6 أشهر على الإصلاح',
    'قطع غيار أصلية معتمدة',
    'فنيين معتمدين ومدربين',
    'خدمة سريعة في نفس اليوم',
    'أسعار تنافسية وشفافة'
  ],
  category: 'إلكترونيات',
  availability: 'متاح يومياً من 9 ص إلى 10 م',
  images: ['/images/repair1.jpg', '/images/repair2.jpg', '/images/repair3.jpg'],
  process: [
    'فحص أولي مجاني للجهاز',
    'تشخيص دقيق للمشكلة',
    'تقديم عرض سعر شفاف',
    'بدء عملية الإصلاح بعد الموافقة',
    'اختبار شامل للجهاز',
    'تسليم الجهاز مع الضمان'
  ],
  requirements: [
    'إحضار الجهاز مع الشاحن الأصلي',
    'إزالة كلمة مرور الجهاز مؤقتاً',
    'نسخ احتياطي للبيانات المهمة',
    'إحضار فاتورة الشراء إن وجدت'
  ]
}

export default function ServiceDetailPage() {
  const params = useParams()
  const { user } = useAuth()
  const [service, setService] = useState<Service | null>(null)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [customerInfo, setCustomerInfo] = useState({
    name: user?.name || '',
    phone: user?.phone || '',
    address: '',
    deviceType: '',
    problemDescription: ''
  })
  const [showBookingForm, setShowBookingForm] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    // In a real app, fetch service by ID
    setService(sampleService)
  }, [params.id])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setCustomerInfo({
      ...customerInfo,
      [e.target.name]: e.target.value
    })
  }

  const handleBookService = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      toast.error('يرجى تسجيل الدخول أولاً')
      return
    }

    if (!selectedDate || !selectedTime) {
      toast.error('يرجى اختيار التاريخ والوقت')
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast.success('تم حجز الخدمة بنجاح! سنتواصل معك قريباً لتأكيد الموعد')
      setShowBookingForm(false)
      
      // Reset form
      setSelectedDate('')
      setSelectedTime('')
      setCustomerInfo({
        ...customerInfo,
        deviceType: '',
        problemDescription: ''
      })
    } catch (error) {
      toast.error('حدث خطأ في حجز الخدمة. يرجى المحاولة مرة أخرى')
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={20}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ))
  }

  // Generate available time slots
  const timeSlots = [
    '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'
  ]

  if (!service) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-gray-600">جاري تحميل الخدمة...</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="mb-8">
        <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
          <Link href="/" className="hover:text-primary-600">الرئيسية</Link>
          <span>/</span>
          <Link href="/services" className="hover:text-primary-600">الخدمات</Link>
          <span>/</span>
          <span className="text-gray-800">{service.name}</span>
        </div>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Service Details */}
        <div className="lg:col-span-2">
          {/* Service Header */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center justify-center w-16 h-16 bg-primary-100 text-primary-600 rounded-lg">
                {service.icon}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">{service.name}</h1>
                <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">{service.category}</span>
              </div>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-2 mb-4">
              <div className="flex">{renderStars(service.rating)}</div>
              <span className="text-gray-600">({service.reviews} تقييم)</span>
            </div>

            {/* Price and Duration */}
            <div className="flex items-center gap-6 mb-4">
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold text-primary-600">{service.price}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <Clock size={16} />
                <span>{service.duration}</span>
              </div>
            </div>

            {/* Availability */}
            <div className="flex items-center gap-2 text-gray-600">
              <Calendar size={16} />
              <span>{service.availability}</span>
            </div>
          </div>

          {/* Service Description */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4">وصف الخدمة</h2>
            <p className="text-gray-600 leading-relaxed mb-6">{service.longDescription}</p>
            
            <h3 className="text-lg font-semibold text-gray-800 mb-3">مميزات الخدمة</h3>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {service.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-gray-600">
                  <CheckCircle size={16} className="text-green-500 flex-shrink-0" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Service Process */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4">خطوات تنفيذ الخدمة</h2>
            <div className="space-y-4">
              {service.process.map((step, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-primary-600 text-white rounded-full text-sm font-bold flex-shrink-0">
                    {index + 1}
                  </div>
                  <p className="text-gray-600 pt-1">{step}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Requirements */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">متطلبات الخدمة</h2>
            <ul className="space-y-2">
              {service.requirements.map((requirement, index) => (
                <li key={index} className="flex items-center gap-2 text-gray-600">
                  <div className="w-2 h-2 bg-primary-600 rounded-full flex-shrink-0"></div>
                  <span>{requirement}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Booking Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
            <h3 className="text-xl font-bold text-gray-800 mb-6">احجز الخدمة</h3>
            
            {!showBookingForm ? (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 mb-2">{service.price}</div>
                  <div className="text-sm text-gray-600">مدة التنفيذ: {service.duration}</div>
                </div>
                
                <button
                  onClick={() => setShowBookingForm(true)}
                  className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
                >
                  احجز الآن
                </button>
                
                <div className="text-center">
                  <button className="flex items-center justify-center gap-2 text-primary-600 hover:text-primary-700 mx-auto">
                    <Phone size={16} />
                    <span>اتصل بنا للاستفسار</span>
                  </button>
                </div>
              </div>
            ) : (
              <form onSubmit={handleBookService} className="space-y-4">
                {/* Date Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    التاريخ المطلوب *
                  </label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                {/* Time Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الوقت المطلوب *
                  </label>
                  <select
                    value={selectedTime}
                    onChange={(e) => setSelectedTime(e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">اختر الوقت</option>
                    {timeSlots.map((time) => (
                      <option key={time} value={time}>{time}</option>
                    ))}
                  </select>
                </div>

                {/* Customer Info */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الاسم *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={customerInfo.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الهاتف *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={customerInfo.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    العنوان *
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={customerInfo.address}
                    onChange={handleInputChange}
                    required
                    placeholder="عنوان الخدمة"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الجهاز *
                  </label>
                  <select
                    name="deviceType"
                    value={customerInfo.deviceType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">اختر نوع الجهاز</option>
                    <option value="phone">هاتف ذكي</option>
                    <option value="laptop">لابتوب</option>
                    <option value="tablet">تابلت</option>
                    <option value="desktop">كمبيوتر مكتبي</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    وصف المشكلة *
                  </label>
                  <textarea
                    name="problemDescription"
                    value={customerInfo.problemDescription}
                    onChange={handleInputChange}
                    required
                    rows={3}
                    placeholder="اشرح المشكلة بالتفصيل"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none"
                  />
                </div>

                <div className="flex gap-2">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    {isSubmitting ? 'جاري الحجز...' : 'تأكيد الحجز'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowBookingForm(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
