'use client'

import { useState } from 'react'
import { CreditCard, Smartphone, Building, Wallet, CheckCircle, AlertCircle } from 'lucide-react'

interface PaymentMethod {
  id: string
  name: string
  nameEn: string
  icon: React.ReactNode
  description: string
  fees: string
  processingTime: string
  minAmount: number
  maxAmount: number
  isActive: boolean
  instructions: string[]
  accountInfo?: {
    number: string
    name: string
  }
}

interface EgyptianPaymentMethodsProps {
  onPaymentSelect: (method: PaymentMethod) => void
  selectedMethod?: string
  amount: number
}

export default function EgyptianPaymentMethods({ 
  onPaymentSelect, 
  selectedMethod, 
  amount 
}: EgyptianPaymentMethodsProps) {
  const [showInstructions, setShowInstructions] = useState<string | null>(null)

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'vodafone_cash',
      name: 'فودافون كاش',
      nameEn: 'Vodafone Cash',
      icon: <Smartphone className="w-6 h-6 text-red-600" />,
      description: 'الدفع عبر محفظة فودافون كاش الإلكترونية',
      fees: 'مجاني للمبالغ أقل من 1000 جنيه',
      processingTime: 'فوري',
      minAmount: 10,
      maxAmount: 50000,
      isActive: true,
      accountInfo: {
        number: '***********',
        name: 'مركز البدوي'
      },
      instructions: [
        'اتصل بـ *9*رقم المحفظة*المبلغ#',
        'أو استخدم تطبيق My Vodafone',
        'أرسل لنا صورة من إيصال التحويل',
        'سيتم تأكيد الطلب خلال 15 دقيقة'
      ]
    },
    {
      id: 'instapay',
      name: 'إنستاباي',
      nameEn: 'InstaPay',
      icon: <Wallet className="w-6 h-6 text-blue-600" />,
      description: 'التحويل الفوري عبر إنستاباي',
      fees: 'مجاني',
      processingTime: 'فوري',
      minAmount: 1,
      maxAmount: 100000,
      isActive: true,
      accountInfo: {
        number: '***********',
        name: 'مركز البدوي'
      },
      instructions: [
        'افتح تطبيق البنك الخاص بك',
        'اختر إنستاباي',
        'أدخل رقم الهاتف: ***********',
        'أدخل المبلغ وأكمل التحويل',
        'أرسل لنا صورة من إيصال التحويل'
      ]
    },
    {
      id: 'orange_money',
      name: 'أورانج موني',
      nameEn: 'Orange Money',
      icon: <Smartphone className="w-6 h-6 text-orange-600" />,
      description: 'الدفع عبر محفظة أورانج الإلكترونية',
      fees: '2 جنيه للمبالغ أكثر من 500 جنيه',
      processingTime: 'فوري',
      minAmount: 10,
      maxAmount: 30000,
      isActive: true,
      accountInfo: {
        number: '***********',
        name: 'مركز البدوي'
      },
      instructions: [
        'اتصل بـ #100*1*رقم المحفظة*المبلغ#',
        'أو استخدم تطبيق Orange Money',
        'أرسل لنا صورة من إيصال التحويل',
        'سيتم تأكيد الطلب خلال 15 دقيقة'
      ]
    },
    {
      id: 'etisalat_cash',
      name: 'اتصالات كاش',
      nameEn: 'Etisalat Cash',
      icon: <Smartphone className="w-6 h-6 text-green-600" />,
      description: 'الدفع عبر محفظة اتصالات الإلكترونية',
      fees: 'مجاني للمبالغ أقل من 2000 جنيه',
      processingTime: 'فوري',
      minAmount: 10,
      maxAmount: 25000,
      isActive: true,
      accountInfo: {
        number: '***********',
        name: 'مركز البدوي'
      },
      instructions: [
        'اتصل بـ *777*رقم المحفظة*المبلغ#',
        'أو استخدم تطبيق Etisalat Cash',
        'أرسل لنا صورة من إيصال التحويل',
        'سيتم تأكيد الطلب خلال 15 دقيقة'
      ]
    },
    {
      id: 'nbe_bank',
      name: 'البنك الأهلي المصري',
      nameEn: 'National Bank of Egypt',
      icon: <Building className="w-6 h-6 text-blue-800" />,
      description: 'التحويل البنكي المباشر',
      fees: 'حسب رسوم البنك',
      processingTime: '1-3 أيام عمل',
      minAmount: 50,
      maxAmount: 1000000,
      isActive: true,
      accountInfo: {
        number: '****************',
        name: 'مركز البدوي للتجارة'
      },
      instructions: [
        'قم بالتحويل إلى رقم الحساب: ****************',
        'اسم المستفيد: مركز البدوي للتجارة',
        'البنك الأهلي المصري - فرع المعادي',
        'أرسل لنا صورة من إيصال التحويل',
        'سيتم تأكيد الطلب خلال 24 ساعة'
      ]
    },
    {
      id: 'cib_bank',
      name: 'البنك التجاري الدولي',
      nameEn: 'Commercial International Bank',
      icon: <Building className="w-6 h-6 text-purple-600" />,
      description: 'التحويل البنكي عبر CIB',
      fees: 'حسب رسوم البنك',
      processingTime: '1-2 أيام عمل',
      minAmount: 50,
      maxAmount: 1000000,
      isActive: true,
      accountInfo: {
        number: '****************',
        name: 'مركز البدوي للتجارة'
      },
      instructions: [
        'قم بالتحويل إلى رقم الحساب: ****************',
        'اسم المستفيد: مركز البدوي للتجارة',
        'البنك التجاري الدولي - فرع مصر الجديدة',
        'أرسل لنا صورة من إيصال التحويل',
        'سيتم تأكيد الطلب خلال 24 ساعة'
      ]
    },
    {
      id: 'cash_on_delivery',
      name: 'الدفع عند الاستلام',
      nameEn: 'Cash on Delivery',
      icon: <CreditCard className="w-6 h-6 text-gray-600" />,
      description: 'ادفع نقداً عند استلام الطلب',
      fees: '15 جنيه رسوم خدمة',
      processingTime: 'عند التسليم',
      minAmount: 50,
      maxAmount: 5000,
      isActive: true,
      instructions: [
        'سيتم تأكيد الطلب خلال ساعة',
        'سيتصل بك مندوب التوصيل لتحديد الموعد',
        'تأكد من توفر المبلغ كاملاً عند الاستلام',
        'يمكنك فحص المنتج قبل الدفع'
      ]
    }
  ]

  const activePaymentMethods = paymentMethods.filter(method => method.isActive)

  const isAmountValid = (method: PaymentMethod) => {
    return amount >= method.minAmount && amount <= method.maxAmount
  }

  const handleMethodSelect = (method: PaymentMethod) => {
    if (!isAmountValid(method)) return
    onPaymentSelect(method)
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">اختر طريقة الدفع</h3>
        <p className="text-gray-600">المبلغ المطلوب: {amount.toLocaleString()} جنيه مصري</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {activePaymentMethods.map((method) => {
          const isValid = isAmountValid(method)
          const isSelected = selectedMethod === method.id

          return (
            <div
              key={method.id}
              className={`
                border-2 rounded-lg p-4 cursor-pointer transition-all
                ${isSelected 
                  ? 'border-primary-500 bg-primary-50' 
                  : isValid 
                    ? 'border-gray-200 hover:border-primary-300' 
                    : 'border-gray-200 opacity-50 cursor-not-allowed'
                }
              `}
              onClick={() => isValid && handleMethodSelect(method)}
            >
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-1">
                  {method.icon}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{method.name}</h4>
                    {isSelected && <CheckCircle className="w-4 h-4 text-primary-600" />}
                    {!isValid && <AlertCircle className="w-4 h-4 text-red-500" />}
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{method.description}</p>
                  
                  <div className="space-y-1 text-xs text-gray-500">
                    <div>الرسوم: {method.fees}</div>
                    <div>وقت المعالجة: {method.processingTime}</div>
                    <div>
                      الحد الأدنى: {method.minAmount.toLocaleString()} جنيه - 
                      الحد الأقصى: {method.maxAmount.toLocaleString()} جنيه
                    </div>
                  </div>

                  {!isValid && (
                    <div className="mt-2 text-xs text-red-600">
                      المبلغ خارج النطاق المسموح لهذه الطريقة
                    </div>
                  )}

                  {isSelected && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setShowInstructions(showInstructions === method.id ? null : method.id)
                      }}
                      className="mt-3 text-sm text-primary-600 hover:text-primary-700 font-medium"
                    >
                      {showInstructions === method.id ? 'إخفاء التعليمات' : 'عرض تعليمات الدفع'}
                    </button>
                  )}
                </div>
              </div>

              {/* Payment Instructions */}
              {isSelected && showInstructions === method.id && (
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <h5 className="font-medium text-blue-900 mb-2">تعليمات الدفع:</h5>
                  
                  {method.accountInfo && (
                    <div className="mb-3 p-2 bg-white border border-blue-200 rounded">
                      <div className="text-sm">
                        <div><strong>رقم الحساب/المحفظة:</strong> {method.accountInfo.number}</div>
                        <div><strong>اسم المستفيد:</strong> {method.accountInfo.name}</div>
                      </div>
                    </div>
                  )}
                  
                  <ol className="list-decimal list-inside space-y-1 text-sm text-blue-800">
                    {method.instructions.map((instruction, index) => (
                      <li key={index}>{instruction}</li>
                    ))}
                  </ol>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Payment Security Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-yellow-800">
            <h4 className="font-medium mb-1">تنبيه أمني:</h4>
            <ul className="space-y-1 text-xs">
              <li>• لا تشارك بيانات الدفع مع أي شخص آخر</li>
              <li>• تأكد من صحة أرقام الحسابات قبل التحويل</li>
              <li>• احتفظ بإيصالات التحويل حتى تأكيد الطلب</li>
              <li>• في حالة وجود مشكلة، تواصل معنا فوراً</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Customer Support */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">تحتاج مساعدة؟</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <div>📞 خدمة العملاء: +20 100 123 4567</div>
          <div>💬 واتساب: +20 100 123 4567</div>
          <div>📧 البريد الإلكتروني: <EMAIL></div>
          <div>🕒 ساعات العمل: من 9 صباحاً إلى 9 مساءً</div>
        </div>
      </div>
    </div>
  )
}
