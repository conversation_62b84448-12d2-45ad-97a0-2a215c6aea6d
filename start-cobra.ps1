# متجر كوبرا - سكريبت تشغيل PowerShell
# Cobra Store - PowerShell Start Script

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "       🐍 متجر كوبرا - تشغيل تلقائي" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من Node.js
Write-Host "🔍 التحقق من Node.js..." -ForegroundColor Blue
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js مثبت: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير مثبت!" -ForegroundColor Red
    Write-Host ""
    Write-Host "📥 يرجى تثبيت Node.js أولاً:" -ForegroundColor Yellow
    Write-Host "1. من الموقع الرسمي: https://nodejs.org/" -ForegroundColor White
    Write-Host "2. أو باستخدام winget: winget install OpenJS.NodeJS" -ForegroundColor White
    Write-Host "3. أو باستخدام Chocolatey: choco install nodejs" -ForegroundColor White
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من npm
Write-Host ""
Write-Host "🔍 التحقق من npm..." -ForegroundColor Blue
try {
    $npmVersion = npm --version
    Write-Host "✅ npm متاح: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm غير متاح!" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من المكتبات
Write-Host ""
Write-Host "🔍 التحقق من المكتبات..." -ForegroundColor Blue
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 تثبيت المكتبات المطلوبة..." -ForegroundColor Yellow
    Write-Host "هذا قد يستغرق بضع دقائق..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        npm install
        Write-Host "✅ تم تثبيت المكتبات بنجاح" -ForegroundColor Green
    } catch {
        Write-Host "❌ فشل في تثبيت المكتبات!" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
} else {
    Write-Host "✅ المكتبات مثبتة مسبقاً" -ForegroundColor Green
}

# عرض معلومات التشغيل
Write-Host ""
Write-Host "🚀 تشغيل متجر كوبرا..." -ForegroundColor Green
Write-Host ""
Write-Host "الموقع سيكون متاح على:" -ForegroundColor Yellow
Write-Host "http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "الصفحات المتاحة:" -ForegroundColor Yellow
Write-Host "• الرئيسية: http://localhost:3000/" -ForegroundColor White
Write-Host "• المنتجات: http://localhost:3000/products" -ForegroundColor White
Write-Host "• الخدمات: http://localhost:3000/services" -ForegroundColor White
Write-Host "• سلة التسوق: http://localhost:3000/cart" -ForegroundColor White
Write-Host "• من نحن: http://localhost:3000/about" -ForegroundColor White
Write-Host "• اتصل بنا: http://localhost:3000/contact" -ForegroundColor White
Write-Host ""
Write-Host "اضغط Ctrl+C لإيقاف الخادم" -ForegroundColor Red
Write-Host ""

# فتح المتصفح تلقائياً بعد 3 ثوان
Start-Job -ScriptBlock {
    Start-Sleep -Seconds 3
    Start-Process "http://localhost:3000"
} | Out-Null

# تشغيل الخادم
try {
    npm run dev
} catch {
    Write-Host ""
    Write-Host "❌ حدث خطأ في تشغيل الخادم!" -ForegroundColor Red
    Write-Host "تأكد من أن المنفذ 3000 غير مستخدم" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
}
