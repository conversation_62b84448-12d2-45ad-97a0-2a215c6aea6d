'use client'

import { useState } from 'react'
import { User, Building, Mail, Phone, MapPin, Eye, EyeOff, AlertCircle, CheckCircle, Crown, Info } from 'lucide-react'
import { db } from '@/lib/database'
import WholesaleRegistration from './WholesaleRegistration'

interface UserRegistrationProps {
  onClose: () => void
  onSuccess: (userType: 'retail' | 'wholesale') => void
}

interface RegistrationFormData {
  name: string
  email: string
  phone: string
  password: string
  confirmPassword: string
  address: string
  city: string
  governorate: string
  userType: 'retail' | 'wholesale'
  agreeToTerms: boolean
}

export default function UserRegistration({ onClose, onSuccess }: UserRegistrationProps) {
  const [step, setStep] = useState<'type-selection' | 'registration' | 'wholesale-details'>('type-selection')
  const [selectedUserType, setSelectedUserType] = useState<'retail' | 'wholesale' | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [showWholesaleModal, setShowWholesaleModal] = useState(false)

  const [formData, setFormData] = useState<RegistrationFormData>({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    address: '',
    city: '',
    governorate: '',
    userType: 'retail',
    agreeToTerms: false
  })

  const egyptianGovernorates = [
    'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
    'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
    'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
    'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
    'قنا', 'شمال سيناء', 'سوهاج'
  ]

  const handleUserTypeSelection = (type: 'retail' | 'wholesale') => {
    setSelectedUserType(type)
    setFormData({ ...formData, userType: type })
    
    if (type === 'wholesale') {
      // Show info about wholesale registration
      setStep('wholesale-details')
    } else {
      setStep('registration')
    }
  }

  const handleRegistration = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      // Validation
      if (!formData.name || !formData.email || !formData.phone || !formData.password) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة')
      }

      if (formData.password !== formData.confirmPassword) {
        throw new Error('كلمات المرور غير متطابقة')
      }

      if (formData.password.length < 6) {
        throw new Error('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      }

      if (!formData.agreeToTerms) {
        throw new Error('يجب الموافقة على الشروط والأحكام')
      }

      // Check if email already exists
      const existingCustomer = db.getCustomerByEmail(formData.email)
      if (existingCustomer) {
        throw new Error('هذا البريد الإلكتروني مسجل مسبقاً')
      }

      // Create customer account
      const customer = db.addCustomer({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        governorate: formData.governorate,
        userType: formData.userType,
        isWholesaleVerified: false,
        isActive: true
      })

      // Save user session
      const userSession = {
        isLoggedIn: true,
        userId: customer.id,
        name: customer.name,
        email: customer.email,
        userType: customer.userType,
        isWholesaleVerified: customer.isWholesaleVerified
      }

      localStorage.setItem('userSession', JSON.stringify(userSession))
      window.dispatchEvent(new CustomEvent('userSessionUpdated'))

      if (formData.userType === 'wholesale') {
        // For wholesale users, show wholesale registration modal
        setShowWholesaleModal(true)
      } else {
        // For retail users, complete registration
        onSuccess(formData.userType)
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleWholesaleSuccess = () => {
    setShowWholesaleModal(false)
    onSuccess('wholesale')
  }

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">إنشاء حساب جديد</h2>
                <p className="text-gray-600">انضم إلى مركز البدوي واستمتع بالتسوق</p>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <span className="text-red-800">{error}</span>
              </div>
            )}

            {/* Step 1: User Type Selection */}
            {step === 'type-selection' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">اختر نوع حسابك</h3>
                  <p className="text-gray-600">حدد نوع الحساب المناسب لك</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Retail User */}
                  <button
                    onClick={() => handleUserTypeSelection('retail')}
                    className="p-6 border-2 border-gray-200 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-all duration-200 text-right"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-semibold text-gray-900">مستخدم عادي</h4>
                        <p className="text-sm text-gray-600 mt-1">للتسوق الشخصي والعائلي</p>
                        <ul className="text-xs text-gray-500 mt-2 space-y-1">
                          <li>• أسعار التجزئة العادية</li>
                          <li>• تسوق سهل وسريع</li>
                          <li>• عروض وخصومات منتظمة</li>
                          <li>• دعم فني متاح</li>
                        </ul>
                      </div>
                    </div>
                  </button>

                  {/* Wholesale User */}
                  <button
                    onClick={() => handleUserTypeSelection('wholesale')}
                    className="p-6 border-2 border-gray-200 rounded-lg hover:border-yellow-500 hover:bg-yellow-50 transition-all duration-200 text-right relative"
                  >
                    <div className="absolute top-2 left-2">
                      <Crown className="w-5 h-5 text-yellow-500" />
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                        <Building className="w-6 h-6 text-yellow-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-semibold text-gray-900">تاجر / موزع</h4>
                        <p className="text-sm text-gray-600 mt-1">للتجار والموزعين</p>
                        <ul className="text-xs text-gray-500 mt-2 space-y-1">
                          <li>• أسعار جملة خاصة</li>
                          <li>• خصومات على الكميات</li>
                          <li>• دعم فني مخصص</li>
                          <li>• شروط دفع مرنة</li>
                        </ul>
                        <div className="mt-2 text-xs text-yellow-700 bg-yellow-100 px-2 py-1 rounded">
                          يتطلب تفعيل من الإدارة
                        </div>
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Wholesale Information */}
            {step === 'wholesale-details' && (
              <div className="space-y-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Crown className="w-8 h-8 text-yellow-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">حساب التاجر</h3>
                  <p className="text-gray-600">معلومات مهمة حول حساب التاجر</p>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Info className="w-5 h-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-900 mb-2">ملاحظة هامة</h4>
                      <p className="text-sm text-yellow-800 mb-3">
                        حساب التاجر يتطلب تفعيل خاص من إدارة مركز البدوي. ستحتاج إلى:
                      </p>
                      <ul className="text-sm text-yellow-800 space-y-1">
                        <li>• إنشاء حساب عادي أولاً</li>
                        <li>• تقديم طلب تفعيل حساب الجملة</li>
                        <li>• انتظار مراجعة الطلب من الإدارة</li>
                        <li>• الحصول على كود التفعيل الخاص</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">مميزات حساب التاجر</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-blue-800">
                    <div>
                      <h5 className="font-medium">الأسعار والخصومات:</h5>
                      <ul className="space-y-1 mt-1">
                        <li>• أسعار جملة خاصة</li>
                        <li>• خصم 5% للكميات 10+</li>
                        <li>• خصم 10% للكميات 50+</li>
                        <li>• خصم 15% للكميات 100+</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-medium">الخدمات الإضافية:</h5>
                      <ul className="space-y-1 mt-1">
                        <li>• دعم فني مخصص</li>
                        <li>• شروط دفع مرنة</li>
                        <li>• أولوية في التوصيل</li>
                        <li>• تقارير مبيعات مفصلة</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">للتواصل مع الإدارة</h4>
                  <div className="space-y-2 text-sm text-gray-700">
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      <span>هاتف: +20 ************</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      <span>إيميل: <EMAIL></span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={() => setStep('registration')}
                    className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-lg font-medium"
                  >
                    متابعة التسجيل
                  </button>
                  <button
                    onClick={() => setStep('type-selection')}
                    className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-3 rounded-lg font-medium"
                  >
                    العودة
                  </button>
                </div>
              </div>
            )}

            {/* Step 3: Registration Form */}
            {step === 'registration' && (
              <form onSubmit={handleRegistration} className="space-y-6">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    {selectedUserType === 'retail' ? (
                      <User className="w-5 h-5 text-blue-600" />
                    ) : (
                      <Building className="w-5 h-5 text-yellow-600" />
                    )}
                    <span className="font-medium">
                      نوع الحساب: {selectedUserType === 'retail' ? 'مستخدم عادي' : 'تاجر / موزع'}
                    </span>
                    <button
                      type="button"
                      onClick={() => setStep('type-selection')}
                      className="text-primary-600 hover:text-primary-700 text-sm underline mr-auto"
                    >
                      تغيير
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="أدخل اسمك الكامل"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      required
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="+20 ************"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      المحافظة *
                    </label>
                    <select
                      required
                      value={formData.governorate}
                      onChange={(e) => setFormData({...formData, governorate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر المحافظة</option>
                      {egyptianGovernorates.map((gov) => (
                        <option key={gov} value={gov}>{gov}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      كلمة المرور *
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        required
                        value={formData.password}
                        onChange={(e) => setFormData({...formData, password: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="أدخل كلمة المرور"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      تأكيد كلمة المرور *
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        required
                        value={formData.confirmPassword}
                        onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="أعد إدخال كلمة المرور"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="agreeToTerms"
                    checked={formData.agreeToTerms}
                    onChange={(e) => setFormData({...formData, agreeToTerms: e.target.checked})}
                    className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                  />
                  <label htmlFor="agreeToTerms" className="mr-2 text-sm text-gray-700">
                    أوافق على{' '}
                    <button type="button" className="text-primary-600 hover:text-primary-700 underline">
                      الشروط والأحكام
                    </button>
                    {' '}و{' '}
                    <button type="button" className="text-primary-600 hover:text-primary-700 underline">
                      سياسة الخصوصية
                    </button>
                  </label>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white py-3 rounded-lg font-medium flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      جاري إنشاء الحساب...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4" />
                      إنشاء الحساب
                    </>
                  )}
                </button>
              </form>
            )}
          </div>
        </div>
      </div>

      {/* Wholesale Registration Modal */}
      {showWholesaleModal && (
        <WholesaleRegistration
          onClose={() => setShowWholesaleModal(false)}
          onSuccess={handleWholesaleSuccess}
        />
      )}
    </>
  )
}
