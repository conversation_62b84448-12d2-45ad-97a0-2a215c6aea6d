# البدء السريع - متجر كوبرا 🚀

## ⚡ تشغيل المشروع في 3 خطوات

### 1️⃣ تثبيت Node.js
```bash
# تحميل من الموقع الرسمي
https://nodejs.org/

# أو باستخدام Chocolatey (Windows)
choco install nodejs

# أو باستخدام Homebrew (Mac)
brew install node
```

### 2️⃣ تثبيت المكتبات
```bash
# في مجلد المشروع
npm install
```

### 3️⃣ تشغيل المشروع
```bash
npm run dev
```

🎉 **المشروع جاهز على**: http://localhost:3000

---

## 📱 استكشاف الموقع

### الصفحات الرئيسية
- **الرئيسية**: `/` - الصفحة الترحيبية
- **المنتجات**: `/products` - تصفح وشراء المنتجات
- **الخدمات**: `/services` - طلب الخدمات
- **سلة التسوق**: `/cart` - إدارة المشتريات
- **من نحن**: `/about` - معلومات الشركة
- **اتصل بنا**: `/contact` - التواصل والاستفسارات

### الميزات التفاعلية
- 🔍 **البحث**: ابحث عن المنتجات والخدمات
- 🛒 **سلة التسوق**: أضف منتجات واحسب المجموع
- ❤️ **المفضلة**: احفظ منتجاتك المفضلة
- 📍 **الموقع**: حدد موقعك للشحن
- 👤 **الحساب**: سجل دخولك وأدر بياناتك

---

## 🛠️ للمطورين

### هيكل المشروع
```
src/
├── app/                 # صفحات Next.js
├── components/          # مكونات React
├── hooks/              # React Hooks
└── styles/             # ملفات CSS
```

### الأوامر المفيدة
```bash
# تطوير
npm run dev

# بناء للإنتاج
npm run build

# تشغيل الإنتاج
npm start

# فحص الكود
npm run lint

# فحص الأنواع
npm run type-check
```

### إضافة صفحة جديدة
```bash
# إنشاء مجلد في src/app
mkdir src/app/new-page

# إنشاء ملف page.tsx
touch src/app/new-page/page.tsx
```

### إضافة مكون جديد
```bash
# إنشاء ملف في src/components
touch src/components/MyComponent.tsx
```

---

## 🎨 التخصيص السريع

### تغيير الألوان
```javascript
// في tailwind.config.js
colors: {
  primary: {
    600: '#your-color', // اللون الأساسي
  }
}
```

### تغيير الخطوط
```css
/* في src/app/globals.css */
@import url('https://fonts.googleapis.com/css2?family=YourFont');

html {
  font-family: 'YourFont', sans-serif;
}
```

### إضافة صور
```bash
# ضع الصور في مجلد public
public/images/your-image.jpg

# استخدمها في الكود
<img src="/images/your-image.jpg" alt="وصف" />
```

---

## 🚀 النشر السريع

### Vercel (مجاني)
```bash
# تثبيت Vercel CLI
npm i -g vercel

# نشر المشروع
vercel
```

### Netlify (مجاني)
```bash
# بناء المشروع
npm run build

# رفع مجلد .next إلى Netlify
```

### GitHub Pages
```bash
# إضافة script في package.json
"export": "next export"

# بناء وتصدير
npm run build && npm run export
```

---

## 🔧 حل المشاكل الشائعة

### المشروع لا يعمل؟
```bash
# احذف node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install
```

### أخطاء TypeScript؟
```bash
# فحص الأخطاء
npm run type-check

# إصلاح تلقائي
npm run lint -- --fix
```

### مشاكل في التصميم؟
```bash
# تأكد من تشغيل Tailwind
npm run dev

# امسح cache المتصفح
Ctrl+Shift+R (Windows) / Cmd+Shift+R (Mac)
```

### الخطوط العربية لا تظهر؟
```css
/* تأكد من وجود هذا في globals.css */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

html {
  direction: rtl;
  font-family: 'Cairo', sans-serif;
}
```

---

## 📚 موارد مفيدة

### التوثيق
- [Next.js Docs](https://nextjs.org/docs)
- [React Docs](https://react.dev)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [TypeScript](https://www.typescriptlang.org/docs)

### أدوات التطوير
- [VS Code](https://code.visualstudio.com/) - محرر الكود
- [Chrome DevTools](https://developer.chrome.com/docs/devtools/) - أدوات المطور
- [React DevTools](https://react.dev/learn/react-developer-tools) - أدوات React

### إضافات VS Code مفيدة
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- TypeScript Importer
- Auto Rename Tag
- Prettier - Code formatter

---

## 🆘 الحصول على المساعدة

### الدعم الفني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 50 123 4567
- **الدردشة**: متاحة على الموقع

### المجتمع
- **GitHub Issues**: للإبلاغ عن مشاكل
- **Discussions**: للأسئلة والاقتراحات
- **Discord**: للدردشة المباشرة

### الموارد التعليمية
- **YouTube**: دروس فيديو
- **Blog**: مقالات تقنية
- **Workshops**: ورش عمل مباشرة

---

## ✅ قائمة التحقق

### قبل البدء
- [ ] تثبيت Node.js 18+
- [ ] تثبيت محرر كود (VS Code)
- [ ] تثبيت Git
- [ ] إنشاء حساب GitHub

### بعد التثبيت
- [ ] تشغيل `npm run dev` بنجاح
- [ ] فتح http://localhost:3000
- [ ] تصفح جميع الصفحات
- [ ] اختبار الميزات التفاعلية

### للنشر
- [ ] تشغيل `npm run build` بنجاح
- [ ] اختبار النسخة المبنية
- [ ] إعداد متغيرات البيئة
- [ ] اختيار منصة النشر

---

## 🎯 الخطوات التالية

1. **استكشف الكود**: تصفح الملفات وافهم الهيكل
2. **جرب التعديل**: غير الألوان أو النصوص
3. **أضف ميزة**: جرب إضافة صفحة أو مكون جديد
4. **انشر المشروع**: ضعه على الإنترنت
5. **شارك النتيجة**: أرسل لنا رابط مشروعك

---

**مبروك! 🎉 أنت الآن جاهز لاستخدام وتطوير متجر كوبرا**

للمزيد من التفاصيل، راجع الملفات الأخرى:
- `README.md` - دليل شامل
- `DEVELOPER_GUIDE.md` - للمطورين
- `USER_GUIDE.md` - للمستخدمين
- `DEPLOYMENT.md` - للنشر
