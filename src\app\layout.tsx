import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Toaster } from 'react-hot-toast'
import { AuthProvider } from '@/contexts/AuthContext'
import { CartProvider } from '@/contexts/CartContext'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { LanguageProvider } from '@/contexts/LanguageContext'
import HeaderPro from '@/components/layout/HeaderPro'
import Footer from '@/components/layout/Footer'
import { QuickActionsMenu } from '@/components/ui/FloatingActionButtons'
import ThemeSettings from '@/components/settings/ThemeSettings'


const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'مركز البدوي - اسم له تاريخ',
  description: 'مركز البدوي - موقع احترافي لبيع المنتجات وطلب الخدمات في مصر. اسم له تاريخ، الاسم يعني الثقة والجودة',
  keywords: 'مركز البدوي, متجر إلكتروني, منتجات, خدمات, توصيل, تسوق أونلاين, مصر, فودافون كاش, إنستاباي',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${inter.className} font-arabic`}>
        <ThemeProvider>
          <LanguageProvider>
            <AuthProvider>
              <CartProvider>
              <div className="min-h-screen flex flex-col bg-theme-background">
                <HeaderPro />
                <main className="flex-grow">
                  {children}
                </main>
                <Footer />
              </div>
              <QuickActionsMenu />
              {/* <ThemeSettings /> */}
              <Toaster
            position="top-center"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
                fontFamily: 'Cairo, sans-serif',
              },
            }}
          />
              </CartProvider>
            </AuthProvider>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
