@echo off
chcp 65001 >nul
echo.
echo ========================================
echo       🐍 متجر كوبرا - تشغيل تلقائي
echo ========================================
echo.

REM التحقق من وجود Node.js
echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo 🌐 سيتم فتح النسخة البسيطة من الموقع...
    echo 📄 فتح index.html في المتصفح...
    echo.
    echo للحصول على النسخة الكاملة:
    echo 1. ثبت Node.js من https://nodejs.org/
    echo 2. شغل هذا الملف مرة أخرى
    echo.
    start "" "%~dp0index.html"
    echo ✅ تم فتح الموقع في المتصفح!
    echo.
    pause
    exit /b 0
)

echo ✅ Node.js مثبت
node --version

REM التحقق من وجود npm
echo.
echo 🔍 التحقق من npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح!
    echo 🌐 سيتم فتح النسخة البسيطة...
    start "" "%~dp0index.html"
    pause
    exit /b 0
)

echo ✅ npm متاح
npm --version

REM التحقق من وجود node_modules
echo.
echo 🔍 التحقق من المكتبات...
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    echo هذا قد يستغرق بضع دقائق...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات!
        echo 🌐 سيتم فتح النسخة البسيطة...
        start "" "%~dp0index.html"
        pause
        exit /b 0
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات مثبتة مسبقاً
)

REM تشغيل المشروع
echo.
echo 🚀 تشغيل متجر كوبرا (النسخة الكاملة)...
echo.
echo الموقع سيكون متاح على:
echo http://localhost:3000
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

REM فتح المتصفح تلقائياً بعد 3 ثوان
start "" timeout /t 3 /nobreak >nul && start http://localhost:3000

REM تشغيل الخادم
npm run dev

pause
