'use client'

import { useRouter } from 'next/navigation'
import { Search, ShoppingBag, Truck } from 'lucide-react'
import { useState } from 'react'

export default function TestButtons() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleQuickShop = () => {
    router.push('/products')
  }

  const handleRequestService = () => {
    router.push('/services')
  }

  return (
    <div className="bg-blue-600 text-white p-8 text-center">
      <h2 className="text-2xl font-bold mb-6">اختبار الوظائف</h2>
      
      {/* Search Test */}
      <form onSubmit={handleSearch} className="mb-6">
        <div className="flex max-w-md mx-auto bg-white rounded-lg overflow-hidden">
          <input
            type="text"
            placeholder="اختبار البحث..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1 px-4 py-2 text-gray-800"
          />
          <button
            type="submit"
            className="bg-yellow-400 text-gray-800 px-4 py-2"
          >
            <Search className="w-5 h-5" />
          </button>
        </div>
      </form>

      {/* Button Tests */}
      <div className="flex gap-4 justify-center">
        <button
          onClick={handleQuickShop}
          className="bg-yellow-400 text-gray-800 px-6 py-3 rounded-lg flex items-center gap-2"
        >
          <ShoppingBag className="w-5 h-5" />
          تسوق الآن
        </button>
        
        <button
          onClick={handleRequestService}
          className="bg-green-500 text-white px-6 py-3 rounded-lg flex items-center gap-2"
        >
          <Truck className="w-5 h-5" />
          اطلب خدمة
        </button>
      </div>
    </div>
  )
}
