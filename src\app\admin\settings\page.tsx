'use client'

import { useState } from 'react'
import { 
  Phone, Mail, MapPin, MessageCircle, CreditCard, Building, 
  Plus, Trash2, Edit, Save, X, Settings, Globe, Users,
  Facebook, Instagram, Twitter, Youtube, Linkedin
} from 'lucide-react'
import { useSiteSettings, ContactInfo, PaymentInfo, SocialMedia } from '@/contexts/SiteSettingsContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { toast } from 'react-hot-toast'

export default function AdminSettingsPage() {
  const { settings, updateContactInfo, updatePaymentInfo, updateSocialMedia, updateBusinessInfo } = useSiteSettings()
  const { t } = useLanguage()
  const { currentTheme } = useTheme()
  
  const [activeTab, setActiveTab] = useState('contact')
  const [editingContact, setEditingContact] = useState<string | null>(null)
  const [editingPayment, setEditingPayment] = useState<string | null>(null)
  const [editingSocial, setEditingSocial] = useState<string | null>(null)

  const [newContact, setNewContact] = useState<Omit<ContactInfo, 'id'>>({
    type: 'phone',
    label: '',
    value: '',
    isActive: true
  })

  const [newPayment, setNewPayment] = useState<Omit<PaymentInfo, 'id'>>({
    type: 'wallet',
    name: '',
    accountNumber: '',
    accountName: '',
    instructions: [],
    isActive: true
  })

  const [newSocial, setNewSocial] = useState<Omit<SocialMedia, 'id'>>({
    platform: 'facebook',
    name: '',
    url: '',
    isActive: true
  })

  const tabs = [
    { id: 'contact', label: 'معلومات الاتصال', icon: Phone },
    { id: 'payment', label: 'معلومات الدفع', icon: CreditCard },
    { id: 'social', label: 'وسائل التواصل', icon: Globe },
    { id: 'business', label: 'معلومات الأعمال', icon: Building }
  ]

  const contactTypes = [
    { value: 'phone', label: 'هاتف', icon: Phone },
    { value: 'email', label: 'بريد إلكتروني', icon: Mail },
    { value: 'address', label: 'عنوان', icon: MapPin },
    { value: 'whatsapp', label: 'واتساب', icon: MessageCircle }
  ]

  const paymentTypes = [
    { value: 'bank', label: 'حساب بنكي' },
    { value: 'wallet', label: 'محفظة إلكترونية' },
    { value: 'cash', label: 'نقدي' }
  ]

  const socialPlatforms = [
    { value: 'facebook', label: 'فيسبوك', icon: Facebook },
    { value: 'instagram', label: 'إنستغرام', icon: Instagram },
    { value: 'twitter', label: 'تويتر', icon: Twitter },
    { value: 'youtube', label: 'يوتيوب', icon: Youtube },
    { value: 'linkedin', label: 'لينكد إن', icon: Linkedin }
  ]

  const handleAddContact = () => {
    if (!newContact.label || !newContact.value) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const updatedContacts = [...settings.contactInfo, { ...newContact, id: Date.now().toString() }]
    updateContactInfo(updatedContacts)
    setNewContact({ type: 'phone', label: '', value: '', isActive: true })
    toast.success('تم إضافة معلومات الاتصال بنجاح')
  }

  const handleUpdateContact = (id: string, updatedContact: Partial<ContactInfo>) => {
    const updatedContacts = settings.contactInfo.map(contact =>
      contact.id === id ? { ...contact, ...updatedContact } : contact
    )
    updateContactInfo(updatedContacts)
    setEditingContact(null)
    toast.success('تم تحديث معلومات الاتصال بنجاح')
  }

  const handleDeleteContact = (id: string) => {
    const updatedContacts = settings.contactInfo.filter(contact => contact.id !== id)
    updateContactInfo(updatedContacts)
    toast.success('تم حذف معلومات الاتصال بنجاح')
  }

  const handleAddPayment = () => {
    if (!newPayment.name || !newPayment.accountNumber || !newPayment.accountName) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const updatedPayments = [...settings.paymentInfo, { ...newPayment, id: Date.now().toString() }]
    updatePaymentInfo(updatedPayments)
    setNewPayment({
      type: 'wallet',
      name: '',
      accountNumber: '',
      accountName: '',
      instructions: [],
      isActive: true
    })
    toast.success('تم إضافة معلومات الدفع بنجاح')
  }

  const handleAddSocial = () => {
    if (!newSocial.name || !newSocial.url) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const updatedSocials = [...settings.socialMedia, { ...newSocial, id: Date.now().toString() }]
    updateSocialMedia(updatedSocials)
    setNewSocial({ platform: 'facebook', name: '', url: '', isActive: true })
    toast.success('تم إضافة وسيلة التواصل بنجاح')
  }

  return (
    <div 
      className="min-h-screen py-8 px-4"
      style={{ backgroundColor: currentTheme.colors.background }}
    >
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2" style={{ color: currentTheme.colors.primary }}>
            إعدادات الموقع
          </h1>
          <p className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
            إدارة معلومات الاتصال والدفع ووسائل التواصل الاجتماعي
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'text-white'
                      : 'hover:opacity-80'
                  }`}
                  style={{
                    backgroundColor: activeTab === tab.id 
                      ? currentTheme.colors.primary 
                      : currentTheme.colors.surface,
                    color: activeTab === tab.id 
                      ? 'white' 
                      : currentTheme.colors.text
                  }}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              )
            })}
          </div>
        </div>

        {/* Content */}
        <div 
          className="rounded-2xl shadow-lg border p-6"
          style={{ 
            backgroundColor: currentTheme.colors.surface,
            borderColor: currentTheme.colors.border
          }}
        >
          {/* Contact Info Tab */}
          {activeTab === 'contact' && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold" style={{ color: currentTheme.colors.text }}>
                معلومات الاتصال
              </h2>

              {/* Add New Contact */}
              <div 
                className="p-4 rounded-lg border"
                style={{ 
                  backgroundColor: currentTheme.colors.background,
                  borderColor: currentTheme.colors.border
                }}
              >
                <h3 className="font-medium mb-4" style={{ color: currentTheme.colors.text }}>
                  إضافة معلومات اتصال جديدة
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <select
                    value={newContact.type}
                    onChange={(e) => setNewContact({ ...newContact, type: e.target.value as any })}
                    className="px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  >
                    {contactTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  <input
                    type="text"
                    placeholder="التسمية"
                    value={newContact.label}
                    onChange={(e) => setNewContact({ ...newContact, label: e.target.value })}
                    className="px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                  <input
                    type="text"
                    placeholder="القيمة"
                    value={newContact.value}
                    onChange={(e) => setNewContact({ ...newContact, value: e.target.value })}
                    className="px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                  <button
                    onClick={handleAddContact}
                    className="flex items-center justify-center gap-2 px-4 py-2 rounded-lg text-white transition-all duration-200 hover:opacity-80"
                    style={{ backgroundColor: currentTheme.colors.primary }}
                  >
                    <Plus className="w-4 h-4" />
                    إضافة
                  </button>
                </div>
              </div>

              {/* Contact List */}
              <div className="space-y-4">
                {settings.contactInfo.map((contact) => {
                  const typeInfo = contactTypes.find(t => t.value === contact.type)
                  const Icon = typeInfo?.icon || Phone
                  
                  return (
                    <div
                      key={contact.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                      style={{ borderColor: currentTheme.colors.border }}
                    >
                      <div className="flex items-center gap-3">
                        <Icon className="w-5 h-5" style={{ color: currentTheme.colors.primary }} />
                        <div>
                          <div className="font-medium" style={{ color: currentTheme.colors.text }}>
                            {contact.label}
                          </div>
                          <div className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
                            {contact.value}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setEditingContact(contact.id)}
                          className="p-2 rounded-lg hover:opacity-80"
                          style={{ backgroundColor: currentTheme.colors.background }}
                        >
                          <Edit className="w-4 h-4" style={{ color: currentTheme.colors.primary }} />
                        </button>
                        <button
                          onClick={() => handleDeleteContact(contact.id)}
                          className="p-2 rounded-lg hover:opacity-80"
                          style={{ backgroundColor: currentTheme.colors.background }}
                        >
                          <Trash2 className="w-4 h-4" style={{ color: currentTheme.colors.error }} />
                        </button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Payment Info Tab */}
          {activeTab === 'payment' && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold" style={{ color: currentTheme.colors.text }}>
                معلومات الدفع
              </h2>

              {/* Add New Payment */}
              <div 
                className="p-4 rounded-lg border"
                style={{ 
                  backgroundColor: currentTheme.colors.background,
                  borderColor: currentTheme.colors.border
                }}
              >
                <h3 className="font-medium mb-4" style={{ color: currentTheme.colors.text }}>
                  إضافة طريقة دفع جديدة
                </h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <select
                      value={newPayment.type}
                      onChange={(e) => setNewPayment({ ...newPayment, type: e.target.value as any })}
                      className="px-3 py-2 border rounded-lg"
                      style={{
                        backgroundColor: currentTheme.colors.surface,
                        borderColor: currentTheme.colors.border,
                        color: currentTheme.colors.text
                      }}
                    >
                      {paymentTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                    <input
                      type="text"
                      placeholder="اسم طريقة الدفع"
                      value={newPayment.name}
                      onChange={(e) => setNewPayment({ ...newPayment, name: e.target.value })}
                      className="px-3 py-2 border rounded-lg"
                      style={{
                        backgroundColor: currentTheme.colors.surface,
                        borderColor: currentTheme.colors.border,
                        color: currentTheme.colors.text
                      }}
                    />
                    <input
                      type="text"
                      placeholder="رقم الحساب"
                      value={newPayment.accountNumber}
                      onChange={(e) => setNewPayment({ ...newPayment, accountNumber: e.target.value })}
                      className="px-3 py-2 border rounded-lg"
                      style={{
                        backgroundColor: currentTheme.colors.surface,
                        borderColor: currentTheme.colors.border,
                        color: currentTheme.colors.text
                      }}
                    />
                  </div>
                  <input
                    type="text"
                    placeholder="اسم صاحب الحساب"
                    value={newPayment.accountName}
                    onChange={(e) => setNewPayment({ ...newPayment, accountName: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                  <button
                    onClick={handleAddPayment}
                    className="flex items-center gap-2 px-4 py-2 rounded-lg text-white transition-all duration-200 hover:opacity-80"
                    style={{ backgroundColor: currentTheme.colors.primary }}
                  >
                    <Plus className="w-4 h-4" />
                    إضافة طريقة دفع
                  </button>
                </div>
              </div>

              {/* Payment List */}
              <div className="space-y-4">
                {settings.paymentInfo.map((payment) => (
                  <div
                    key={payment.id}
                    className="p-4 border rounded-lg"
                    style={{ borderColor: currentTheme.colors.border }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <CreditCard className="w-5 h-5" style={{ color: currentTheme.colors.primary }} />
                        <div>
                          <div className="font-medium" style={{ color: currentTheme.colors.text }}>
                            {payment.name}
                          </div>
                          <div className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
                            {payment.accountNumber} - {payment.accountName}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setEditingPayment(payment.id)}
                          className="p-2 rounded-lg hover:opacity-80"
                          style={{ backgroundColor: currentTheme.colors.background }}
                        >
                          <Edit className="w-4 h-4" style={{ color: currentTheme.colors.primary }} />
                        </button>
                        <button
                          className="p-2 rounded-lg hover:opacity-80"
                          style={{ backgroundColor: currentTheme.colors.background }}
                        >
                          <Trash2 className="w-4 h-4" style={{ color: currentTheme.colors.error }} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Social Media Tab */}
          {activeTab === 'social' && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold" style={{ color: currentTheme.colors.text }}>
                وسائل التواصل الاجتماعي
              </h2>

              {/* Add New Social */}
              <div 
                className="p-4 rounded-lg border"
                style={{ 
                  backgroundColor: currentTheme.colors.background,
                  borderColor: currentTheme.colors.border
                }}
              >
                <h3 className="font-medium mb-4" style={{ color: currentTheme.colors.text }}>
                  إضافة وسيلة تواصل جديدة
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <select
                    value={newSocial.platform}
                    onChange={(e) => setNewSocial({ ...newSocial, platform: e.target.value as any })}
                    className="px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  >
                    {socialPlatforms.map((platform) => (
                      <option key={platform.value} value={platform.value}>
                        {platform.label}
                      </option>
                    ))}
                  </select>
                  <input
                    type="text"
                    placeholder="اسم الحساب"
                    value={newSocial.name}
                    onChange={(e) => setNewSocial({ ...newSocial, name: e.target.value })}
                    className="px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                  <input
                    type="url"
                    placeholder="الرابط"
                    value={newSocial.url}
                    onChange={(e) => setNewSocial({ ...newSocial, url: e.target.value })}
                    className="px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                  <button
                    onClick={handleAddSocial}
                    className="flex items-center justify-center gap-2 px-4 py-2 rounded-lg text-white transition-all duration-200 hover:opacity-80"
                    style={{ backgroundColor: currentTheme.colors.primary }}
                  >
                    <Plus className="w-4 h-4" />
                    إضافة
                  </button>
                </div>
              </div>

              {/* Social List */}
              <div className="space-y-4">
                {settings.socialMedia.map((social) => {
                  const platformInfo = socialPlatforms.find(p => p.value === social.platform)
                  const Icon = platformInfo?.icon || Globe
                  
                  return (
                    <div
                      key={social.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                      style={{ borderColor: currentTheme.colors.border }}
                    >
                      <div className="flex items-center gap-3">
                        <Icon className="w-5 h-5" style={{ color: currentTheme.colors.primary }} />
                        <div>
                          <div className="font-medium" style={{ color: currentTheme.colors.text }}>
                            {social.name}
                          </div>
                          <div className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
                            {social.url}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setEditingSocial(social.id)}
                          className="p-2 rounded-lg hover:opacity-80"
                          style={{ backgroundColor: currentTheme.colors.background }}
                        >
                          <Edit className="w-4 h-4" style={{ color: currentTheme.colors.primary }} />
                        </button>
                        <button
                          className="p-2 rounded-lg hover:opacity-80"
                          style={{ backgroundColor: currentTheme.colors.background }}
                        >
                          <Trash2 className="w-4 h-4" style={{ color: currentTheme.colors.error }} />
                        </button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Business Info Tab */}
          {activeTab === 'business' && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold" style={{ color: currentTheme.colors.text }}>
                معلومات الأعمال
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                    اسم الشركة
                  </label>
                  <input
                    type="text"
                    value={settings.businessInfo.name}
                    onChange={(e) => updateBusinessInfo({ name: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                    الشعار
                  </label>
                  <input
                    type="text"
                    value={settings.businessInfo.slogan}
                    onChange={(e) => updateBusinessInfo({ slogan: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                    وصف الشركة
                  </label>
                  <textarea
                    value={settings.businessInfo.description}
                    onChange={(e) => updateBusinessInfo({ description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                    ساعات العمل
                  </label>
                  <input
                    type="text"
                    value={settings.businessInfo.workingHours}
                    onChange={(e) => updateBusinessInfo({ workingHours: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                    الموقع
                  </label>
                  <input
                    type="text"
                    value={settings.businessInfo.location}
                    onChange={(e) => updateBusinessInfo({ location: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
