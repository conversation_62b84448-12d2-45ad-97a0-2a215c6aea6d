'use client'

import { useState } from 'react'
import {
  Phone, Mail, MapPin, MessageCircle, CreditCard, Building,
  Plus, Trash2, Edit, Save, X, Settings, Globe, Users,
  Facebook, Instagram, Twitter, Youtube, Linkedin, Shield,
  CheckCircle, AlertCircle, Eye, EyeOff, Copy, ExternalLink
} from 'lucide-react'
import { useSiteSettings, ContactInfo, PaymentInfo, SocialMedia } from '@/contexts/SiteSettingsContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { toast } from 'react-hot-toast'

export default function AdminSettingsPage() {
  const { settings, updateContactInfo, updatePaymentInfo, updateSocialMedia, updateBusinessInfo } = useSiteSettings()
  const { t } = useLanguage()
  const { currentTheme } = useTheme()
  
  const [activeTab, setActiveTab] = useState('contact')
  const [editingContact, setEditingContact] = useState<string | null>(null)
  const [editingPayment, setEditingPayment] = useState<string | null>(null)
  const [editingSocial, setEditingSocial] = useState<string | null>(null)

  const [newContact, setNewContact] = useState<Omit<ContactInfo, 'id'>>({
    type: 'phone',
    label: '',
    value: '',
    isActive: true
  })

  const [newPayment, setNewPayment] = useState<Omit<PaymentInfo, 'id'>>({
    type: 'wallet',
    name: '',
    accountNumber: '',
    accountName: '',
    instructions: [],
    isActive: true
  })

  const [newSocial, setNewSocial] = useState<Omit<SocialMedia, 'id'>>({
    platform: 'facebook',
    name: '',
    url: '',
    isActive: true
  })

  const tabs = [
    { id: 'contact', label: 'معلومات الاتصال', icon: Phone },
    { id: 'payment', label: 'معلومات الدفع', icon: CreditCard },
    { id: 'social', label: 'وسائل التواصل', icon: Globe },
    { id: 'business', label: 'معلومات الأعمال', icon: Building }
  ]

  const contactTypes = [
    { value: 'phone', label: 'هاتف', icon: Phone },
    { value: 'email', label: 'بريد إلكتروني', icon: Mail },
    { value: 'address', label: 'عنوان', icon: MapPin },
    { value: 'whatsapp', label: 'واتساب', icon: MessageCircle }
  ]

  const paymentTypes = [
    { value: 'bank', label: 'حساب بنكي' },
    { value: 'wallet', label: 'محفظة إلكترونية' },
    { value: 'cash', label: 'نقدي' }
  ]

  const socialPlatforms = [
    { value: 'facebook', label: 'فيسبوك', icon: Facebook },
    { value: 'instagram', label: 'إنستغرام', icon: Instagram },
    { value: 'twitter', label: 'تويتر', icon: Twitter },
    { value: 'youtube', label: 'يوتيوب', icon: Youtube },
    { value: 'linkedin', label: 'لينكد إن', icon: Linkedin }
  ]

  const handleAddContact = () => {
    if (!newContact.label || !newContact.value) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const updatedContacts = [...settings.contactInfo, { ...newContact, id: Date.now().toString() }]
    updateContactInfo(updatedContacts)
    setNewContact({ type: 'phone', label: '', value: '', isActive: true })
    toast.success('تم إضافة معلومات الاتصال بنجاح')
  }

  const handleUpdateContact = (id: string, updatedContact: Partial<ContactInfo>) => {
    const updatedContacts = settings.contactInfo.map(contact =>
      contact.id === id ? { ...contact, ...updatedContact } : contact
    )
    updateContactInfo(updatedContacts)
    setEditingContact(null)
    toast.success('تم تحديث معلومات الاتصال بنجاح')
  }

  const handleDeleteContact = (id: string) => {
    const updatedContacts = settings.contactInfo.filter(contact => contact.id !== id)
    updateContactInfo(updatedContacts)
    toast.success('تم حذف معلومات الاتصال بنجاح')
  }

  const handleAddPayment = () => {
    if (!newPayment.name || !newPayment.accountNumber || !newPayment.accountName) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const updatedPayments = [...settings.paymentInfo, { ...newPayment, id: Date.now().toString() }]
    updatePaymentInfo(updatedPayments)
    setNewPayment({
      type: 'wallet',
      name: '',
      accountNumber: '',
      accountName: '',
      instructions: [],
      isActive: true
    })
    toast.success('تم إضافة معلومات الدفع بنجاح')
  }

  const handleAddSocial = () => {
    if (!newSocial.name || !newSocial.url) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const updatedSocials = [...settings.socialMedia, { ...newSocial, id: Date.now().toString() }]
    updateSocialMedia(updatedSocials)
    setNewSocial({ platform: 'facebook', name: '', url: '', isActive: true })
    toast.success('تم إضافة وسيلة التواصل بنجاح')
  }

  return (
    <div
      className="min-h-screen"
      style={{ backgroundColor: currentTheme.colors.background }}
    >
      {/* Header Section */}
      <div
        className="border-b shadow-sm"
        style={{
          backgroundColor: currentTheme.colors.surface,
          borderColor: currentTheme.colors.border
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div
                className="w-12 h-12 rounded-xl flex items-center justify-center"
                style={{ backgroundColor: currentTheme.colors.primary + '20' }}
              >
                <Settings className="w-6 h-6" style={{ color: currentTheme.colors.primary }} />
              </div>
              <div>
                <h1 className="text-3xl font-bold" style={{ color: currentTheme.colors.text }}>
                  {t('siteSettings')}
                </h1>
                <p className="text-sm mt-1" style={{ color: currentTheme.colors.textSecondary }}>
                  إدارة شاملة لجميع إعدادات الموقع ومعلومات الاتصال
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div
                className="px-3 py-1 rounded-full text-xs font-medium"
                style={{
                  backgroundColor: currentTheme.colors.success + '20',
                  color: currentTheme.colors.success
                }}
              >
                <CheckCircle className="w-3 h-3 inline mr-1" />
                متصل
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div
            className="rounded-2xl p-2 shadow-sm border"
            style={{
              backgroundColor: currentTheme.colors.surface,
              borderColor: currentTheme.colors.border
            }}
          >
            <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`relative flex flex-col items-center gap-3 p-4 rounded-xl transition-all duration-300 group ${
                      isActive ? 'shadow-lg transform scale-105' : 'hover:scale-102'
                    }`}
                    style={{
                      backgroundColor: isActive
                        ? currentTheme.colors.primary
                        : 'transparent',
                      color: isActive
                        ? 'white'
                        : currentTheme.colors.text
                    }}
                  >
                    <div
                      className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ${
                        isActive ? 'bg-white bg-opacity-20' : ''
                      }`}
                      style={{
                        backgroundColor: !isActive ? currentTheme.colors.primary + '15' : undefined
                      }}
                    >
                      <Icon
                        className="w-6 h-6"
                        style={{
                          color: isActive ? 'white' : currentTheme.colors.primary
                        }}
                      />
                    </div>
                    <div className="text-center">
                      <div className={`font-semibold text-sm ${isActive ? 'text-white' : ''}`}>
                        {tab.label}
                      </div>
                      <div
                        className={`text-xs mt-1 ${
                          isActive ? 'text-white text-opacity-80' : ''
                        }`}
                        style={{
                          color: !isActive ? currentTheme.colors.textSecondary : undefined
                        }}
                      >
                        {tab.id === 'contact' && 'إدارة بيانات التواصل'}
                        {tab.id === 'payment' && 'طرق الدفع المتاحة'}
                        {tab.id === 'social' && 'الحسابات الاجتماعية'}
                        {tab.id === 'business' && 'معلومات الشركة'}
                      </div>
                    </div>
                    {isActive && (
                      <div
                        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-8 h-1 rounded-full bg-white"
                      />
                    )}
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-8">
          {/* Contact Info Tab */}
          {activeTab === 'contact' && (
            <div className="space-y-8">
              {/* Section Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className="w-10 h-10 rounded-lg flex items-center justify-center"
                    style={{ backgroundColor: currentTheme.colors.primary + '20' }}
                  >
                    <Phone className="w-5 h-5" style={{ color: currentTheme.colors.primary }} />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold" style={{ color: currentTheme.colors.text }}>
                      {t('contactInfo')}
                    </h2>
                    <p className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
                      إدارة جميع وسائل التواصل مع العملاء
                    </p>
                  </div>
                </div>
                <div
                  className="px-3 py-1 rounded-full text-xs font-medium"
                  style={{
                    backgroundColor: currentTheme.colors.primary + '20',
                    color: currentTheme.colors.primary
                  }}
                >
                  {activeContactInfo.length} عنصر نشط
                </div>
              </div>

              {/* Add New Contact Card */}
              <div
                className="rounded-2xl border shadow-sm overflow-hidden"
                style={{
                  backgroundColor: currentTheme.colors.surface,
                  borderColor: currentTheme.colors.border
                }}
              >
                <div
                  className="px-6 py-4 border-b"
                  style={{
                    backgroundColor: currentTheme.colors.background,
                    borderColor: currentTheme.colors.border
                  }}
                >
                  <div className="flex items-center gap-3">
                    <Plus className="w-5 h-5" style={{ color: currentTheme.colors.primary }} />
                    <h3 className="font-semibold" style={{ color: currentTheme.colors.text }}>
                      إضافة معلومات اتصال جديدة
                    </h3>
                  </div>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                    {/* Contact Type */}
                    <div className="space-y-2">
                      <label className="block text-sm font-medium" style={{ color: currentTheme.colors.text }}>
                        نوع الاتصال
                      </label>
                      <div className="relative">
                        <select
                          value={newContact.type}
                          onChange={(e) => setNewContact({ ...newContact, type: e.target.value as any })}
                          className="w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200"
                          style={{
                            backgroundColor: currentTheme.colors.background,
                            borderColor: currentTheme.colors.border,
                            color: currentTheme.colors.text
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = currentTheme.colors.primary
                            e.target.style.boxShadow = `0 0 0 3px ${currentTheme.colors.primary}20`
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = currentTheme.colors.border
                            e.target.style.boxShadow = 'none'
                          }}
                        >
                          {contactTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Label */}
                    <div className="space-y-2">
                      <label className="block text-sm font-medium" style={{ color: currentTheme.colors.text }}>
                        التسمية
                      </label>
                      <input
                        type="text"
                        placeholder="مثل: الهاتف الرئيسي"
                        value={newContact.label}
                        onChange={(e) => setNewContact({ ...newContact, label: e.target.value })}
                        className="w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200"
                        style={{
                          backgroundColor: currentTheme.colors.background,
                          borderColor: currentTheme.colors.border,
                          color: currentTheme.colors.text
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = currentTheme.colors.primary
                          e.target.style.boxShadow = `0 0 0 3px ${currentTheme.colors.primary}20`
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = currentTheme.colors.border
                          e.target.style.boxShadow = 'none'
                        }}
                      />
                    </div>

                    {/* Value */}
                    <div className="space-y-2">
                      <label className="block text-sm font-medium" style={{ color: currentTheme.colors.text }}>
                        القيمة
                      </label>
                      <input
                        type="text"
                        placeholder="مثل: +20 100 123 4567"
                        value={newContact.value}
                        onChange={(e) => setNewContact({ ...newContact, value: e.target.value })}
                        className="w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200"
                        style={{
                          backgroundColor: currentTheme.colors.background,
                          borderColor: currentTheme.colors.border,
                          color: currentTheme.colors.text
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = currentTheme.colors.primary
                          e.target.style.boxShadow = `0 0 0 3px ${currentTheme.colors.primary}20`
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = currentTheme.colors.border
                          e.target.style.boxShadow = 'none'
                        }}
                      />
                    </div>

                    {/* Add Button */}
                    <div className="space-y-2">
                      <label className="block text-sm font-medium opacity-0">إضافة</label>
                      <button
                        onClick={handleAddContact}
                        disabled={!newContact.label || !newContact.value}
                        className="w-full flex items-center justify-center gap-2 px-4 py-3 rounded-xl text-white font-medium transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                        style={{ backgroundColor: currentTheme.colors.primary }}
                      >
                        <Plus className="w-4 h-4" />
                        إضافة
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              </div>

              {/* Contact List */}
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold" style={{ color: currentTheme.colors.text }}>
                    معلومات الاتصال الحالية
                  </h3>
                  <div className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
                    {settings.contactInfo.length} عنصر
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {settings.contactInfo.map((contact) => {
                    const typeInfo = contactTypes.find(t => t.value === contact.type)
                    const Icon = typeInfo?.icon || Phone

                    return (
                      <div
                        key={contact.id}
                        className="group relative rounded-2xl border shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden"
                        style={{
                          backgroundColor: currentTheme.colors.surface,
                          borderColor: contact.isActive ? currentTheme.colors.primary + '40' : currentTheme.colors.border
                        }}
                      >
                        {/* Status Indicator */}
                        <div
                          className={`absolute top-0 left-0 w-full h-1 ${
                            contact.isActive ? 'bg-green-500' : 'bg-gray-300'
                          }`}
                        />

                        <div className="p-6">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-4 flex-1">
                              <div
                                className="w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0"
                                style={{ backgroundColor: currentTheme.colors.primary + '20' }}
                              >
                                <Icon className="w-6 h-6" style={{ color: currentTheme.colors.primary }} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-semibold truncate" style={{ color: currentTheme.colors.text }}>
                                    {contact.label}
                                  </h4>
                                  <span
                                    className={`px-2 py-1 text-xs rounded-full font-medium ${
                                      contact.isActive
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-gray-100 text-gray-600'
                                    }`}
                                  >
                                    {contact.isActive ? 'نشط' : 'غير نشط'}
                                  </span>
                                </div>
                                <p className="text-sm break-all" style={{ color: currentTheme.colors.textSecondary }}>
                                  {contact.value}
                                </p>
                                <div className="flex items-center gap-2 mt-2">
                                  <span
                                    className="text-xs px-2 py-1 rounded-full"
                                    style={{
                                      backgroundColor: currentTheme.colors.background,
                                      color: currentTheme.colors.textSecondary
                                    }}
                                  >
                                    {typeInfo?.label}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                              <button
                                onClick={() => navigator.clipboard.writeText(contact.value)}
                                className="p-2 rounded-lg hover:scale-110 transition-transform duration-200"
                                style={{ backgroundColor: currentTheme.colors.background }}
                                title="نسخ"
                              >
                                <Copy className="w-4 h-4" style={{ color: currentTheme.colors.textSecondary }} />
                              </button>
                              <button
                                onClick={() => setEditingContact(contact.id)}
                                className="p-2 rounded-lg hover:scale-110 transition-transform duration-200"
                                style={{ backgroundColor: currentTheme.colors.background }}
                                title="تعديل"
                              >
                                <Edit className="w-4 h-4" style={{ color: currentTheme.colors.primary }} />
                              </button>
                              <button
                                onClick={() => handleDeleteContact(contact.id)}
                                className="p-2 rounded-lg hover:scale-110 transition-transform duration-200"
                                style={{ backgroundColor: currentTheme.colors.background }}
                                title="حذف"
                              >
                                <Trash2 className="w-4 h-4" style={{ color: currentTheme.colors.error }} />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {settings.contactInfo.length === 0 && (
                  <div
                    className="text-center py-12 rounded-2xl border-2 border-dashed"
                    style={{ borderColor: currentTheme.colors.border }}
                  >
                    <Phone className="w-12 h-12 mx-auto mb-4 opacity-50" style={{ color: currentTheme.colors.textSecondary }} />
                    <h3 className="text-lg font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                      لا توجد معلومات اتصال
                    </h3>
                    <p className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
                      ابدأ بإضافة معلومات الاتصال الخاصة بك
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Placeholder for other tabs */}
          {activeTab !== 'contact' && (
            <div
              className="text-center py-20 rounded-2xl border-2 border-dashed"
              style={{ borderColor: currentTheme.colors.border }}
            >
              <Settings className="w-16 h-16 mx-auto mb-4 opacity-50" style={{ color: currentTheme.colors.textSecondary }} />
              <h3 className="text-xl font-medium mb-2" style={{ color: currentTheme.colors.text }}>
                قريباً
              </h3>
              <p className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
                هذا القسم قيد التطوير
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}


        </div>
      </div>
    </div>
  )
}
