'use client'

import { useState } from 'react'
import { Users, UserPlus, Edit, Trash2, Shield, Key, Eye, EyeOff, Mail, Phone } from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  phone: string
  role: 'admin' | 'manager' | 'employee'
  permissions: string[]
  isActive: boolean
  lastLogin: string
  createdAt: string
}

interface Permission {
  id: string
  name: string
  description: string
  category: string
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([
    {
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      phone: '+20 ************',
      role: 'admin',
      permissions: ['all'],
      isActive: true,
      lastLogin: '2024-01-15 10:30',
      createdAt: '2024-01-01'
    },
    {
      id: '2',
      name: 'فاطمة علي',
      email: '<EMAIL>',
      phone: '+20 ************',
      role: 'manager',
      permissions: ['products', 'orders', 'customers'],
      isActive: true,
      lastLogin: '2024-01-14 16:45',
      createdAt: '2024-01-05'
    },
    {
      id: '3',
      name: 'محمد حسن',
      email: '<EMAIL>',
      phone: '+20 ************',
      role: 'employee',
      permissions: ['orders', 'customers'],
      isActive: false,
      lastLogin: '2024-01-10 14:20',
      createdAt: '2024-01-10'
    }
  ])

  const [showAddModal, setShowAddModal] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [showPasswords, setShowPasswords] = useState(false)

  const permissions: Permission[] = [
    { id: 'products', name: 'إدارة المنتجات', description: 'إضافة وتعديل وحذف المنتجات', category: 'المنتجات' },
    { id: 'categories', name: 'إدارة التصنيفات', description: 'إدارة تصنيفات المنتجات', category: 'المنتجات' },
    { id: 'orders', name: 'إدارة الطلبات', description: 'عرض ومعالجة الطلبات', category: 'المبيعات' },
    { id: 'customers', name: 'إدارة العملاء', description: 'عرض وإدارة بيانات العملاء', category: 'العملاء' },
    { id: 'payments', name: 'إدارة المدفوعات', description: 'عرض وإدارة المعاملات المالية', category: 'المالية' },
    { id: 'reports', name: 'التقارير', description: 'عرض التقارير والإحصائيات', category: 'التقارير' },
    { id: 'settings', name: 'الإعدادات', description: 'تعديل إعدادات النظام', category: 'النظام' },
    { id: 'users', name: 'إدارة المستخدمين', description: 'إضافة وإدارة المستخدمين', category: 'النظام' }
  ]

  const roleLabels = {
    admin: 'مدير عام',
    manager: 'مدير',
    employee: 'موظف'
  }

  const roleColors = {
    admin: 'bg-red-100 text-red-800',
    manager: 'bg-blue-100 text-blue-800',
    employee: 'bg-green-100 text-green-800'
  }

  const toggleUserStatus = (userId: string) => {
    setUsers(users.map(user => 
      user.id === userId ? { ...user, isActive: !user.isActive } : user
    ))
  }

  const deleteUser = (userId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      setUsers(users.filter(user => user.id !== userId))
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة المستخدمين والأذونات</h2>
          <p className="text-gray-600">إدارة المستخدمين وصلاحياتهم في النظام</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
        >
          <UserPlus className="w-4 h-4" />
          إضافة مستخدم جديد
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي المستخدمين</p>
              <p className="text-2xl font-bold text-gray-900">{users.length}</p>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">نشط</p>
              <p className="text-2xl font-bold text-gray-900">{users.filter(u => u.isActive).length}</p>
            </div>
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 font-bold">✓</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-red-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المديرين</p>
              <p className="text-2xl font-bold text-gray-900">{users.filter(u => u.role === 'admin').length}</p>
            </div>
            <Shield className="w-8 h-8 text-red-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border-r-4 border-yellow-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الموظفين</p>
              <p className="text-2xl font-bold text-gray-900">{users.filter(u => u.role === 'employee').length}</p>
            </div>
            <Users className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">قائمة المستخدمين</h3>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowPasswords(!showPasswords)}
                className="text-sm text-gray-600 hover:text-gray-800 flex items-center gap-1"
              >
                {showPasswords ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {showPasswords ? 'إخفاء' : 'عرض'} التفاصيل
              </button>
            </div>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستخدم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الدور</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصلاحيات</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر دخول</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 font-bold text-sm">
                          {user.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900">{user.name}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <Mail className="w-3 h-3" />
                            {user.email}
                          </span>
                          <span className="flex items-center gap-1">
                            <Phone className="w-3 h-3" />
                            {user.phone}
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleColors[user.role]}`}>
                      {roleLabels[user.role]}
                    </span>
                  </td>
                  
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {user.permissions.includes('all') ? (
                        <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                          جميع الصلاحيات
                        </span>
                      ) : (
                        user.permissions.slice(0, 3).map((perm, index) => (
                          <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {permissions.find(p => p.id === perm)?.name}
                          </span>
                        ))
                      )}
                      {user.permissions.length > 3 && !user.permissions.includes('all') && (
                        <span className="text-xs text-gray-500">
                          +{user.permissions.length - 3} المزيد
                        </span>
                      )}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{user.lastLogin}</div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => toggleUserStatus(user.id)}
                      className={`text-xs px-2 py-1 rounded-full font-medium ${
                        user.isActive
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      }`}
                    >
                      {user.isActive ? 'نشط' : 'غير نشط'}
                    </button>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setEditingUser(user)}
                        className="text-blue-600 hover:text-blue-800 p-1"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-800 p-1">
                        <Key className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => deleteUser(user.id)}
                        className="text-red-600 hover:text-red-800 p-1"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Permissions Overview */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">نظرة عامة على الصلاحيات</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(
            permissions.reduce((acc, perm) => {
              if (!acc[perm.category]) acc[perm.category] = []
              acc[perm.category].push(perm)
              return acc
            }, {} as Record<string, Permission[]>)
          ).map(([category, perms]) => (
            <div key={category} className="border rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">{category}</h4>
              <div className="space-y-2">
                {perms.map((perm) => (
                  <div key={perm.id} className="text-sm">
                    <div className="font-medium text-gray-800">{perm.name}</div>
                    <div className="text-gray-600 text-xs">{perm.description}</div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
