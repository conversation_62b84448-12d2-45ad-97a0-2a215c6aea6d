'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Search, ShoppingBag, Truck, Mic } from 'lucide-react'

export default function HeroSimple() {
  const [searchQuery, setSearchQuery] = useState('')
  const router = useRouter()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleQuickShop = () => {
    router.push('/products')
  }

  const handleRequestService = () => {
    router.push('/services')
  }

  return (
    <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
      <div className="container mx-auto px-4 text-center">
        {/* Title */}
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          مرحباً بكم في
          <span className="block text-yellow-300">مركز البدوي</span>
        </h1>
        
        <p className="text-xl md:text-2xl font-bold text-yellow-200 mb-2">
          اسم له تاريخ
        </p>
        
        <p className="text-lg text-blue-100 mb-8">
          أفضل المنتجات والخدمات مع توصيل سريع وآمن
        </p>

        {/* Search Box */}
        <div className="max-w-2xl mx-auto mb-8">
          <form onSubmit={handleSearch} className="flex bg-white rounded-lg overflow-hidden shadow-lg">
            <input
              type="text"
              placeholder="ابحث في أكثر من 1000 منتج وخدمة..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1 px-6 py-4 text-gray-800 text-lg focus:outline-none"
            />
            <button
              type="button"
              className="px-4 py-4 text-gray-400 hover:text-blue-600"
              title="البحث الصوتي"
            >
              <Mic className="w-5 h-5" />
            </button>
            <button
              type="submit"
              className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 px-8 py-4 font-bold"
            >
              <Search className="w-5 h-5" />
            </button>
          </form>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <button
            onClick={handleQuickShop}
            className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-bold py-4 px-8 rounded-lg flex items-center justify-center gap-3 transition-all transform hover:scale-105"
          >
            <ShoppingBag className="w-6 h-6" />
            <span className="text-lg">تسوق الآن</span>
          </button>
          
          <button
            onClick={handleRequestService}
            className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-600 font-bold py-4 px-8 rounded-lg flex items-center justify-center gap-3 transition-all transform hover:scale-105"
          >
            <Truck className="w-6 h-6" />
            <span className="text-lg">اطلب خدمة</span>
          </button>
        </div>

        {/* Quick Stats */}
        <div className="flex flex-wrap gap-6 justify-center text-blue-100">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>+1000 منتج متاح</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
            <span>توصيل لجميع المحافظات</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
            <span>دعم فني 24/7</span>
          </div>
        </div>
      </div>
    </section>
  )
}
