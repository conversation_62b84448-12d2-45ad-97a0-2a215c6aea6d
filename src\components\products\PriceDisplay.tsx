'use client'

import { useState, useEffect } from 'react'
import { Crown, Tag, TrendingDown, Info } from 'lucide-react'

interface PriceDisplayProps {
  retailPrice: number
  wholesalePrice: number
  originalPrice?: number
  currency?: string
  showWholesalePrice?: boolean
  isWholesaleUser?: boolean
  discount?: number
  className?: string
}

interface UserSession {
  isLoggedIn: boolean
  userType: 'retail' | 'wholesale'
  isWholesaleVerified: boolean
  name?: string
}

export default function PriceDisplay({
  retailPrice,
  wholesalePrice,
  originalPrice,
  currency = 'جنيه',
  showWholesalePrice = false,
  isWholesaleUser = false,
  discount,
  className = ''
}: PriceDisplayProps) {
  const [userSession, setUserSession] = useState<UserSession>({
    isLoggedIn: false,
    userType: 'retail',
    isWholesaleVerified: false
  })

  useEffect(() => {
    // Check user session from localStorage
    const checkUserSession = () => {
      try {
        const session = localStorage.getItem('userSession')
        if (session) {
          const parsedSession = JSON.parse(session)
          setUserSession(parsedSession)
        }
      } catch (error) {
        console.error('Error reading user session:', error)
      }
    }

    checkUserSession()

    // Listen for session updates
    const handleSessionUpdate = () => {
      checkUserSession()
    }

    window.addEventListener('userSessionUpdated', handleSessionUpdate)
    return () => window.removeEventListener('userSessionUpdated', handleSessionUpdate)
  }, [])

  const shouldShowWholesalePrice = userSession.isLoggedIn && 
                                   userSession.userType === 'wholesale' && 
                                   userSession.isWholesaleVerified

  const currentPrice = shouldShowWholesalePrice ? wholesalePrice : retailPrice
  const savings = retailPrice - wholesalePrice
  const savingsPercentage = Math.round((savings / retailPrice) * 100)

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('ar-EG', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(price)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Main Price Display */}
      <div className="flex items-center gap-3 flex-wrap">
        {/* Current Price */}
        <div className="flex items-center gap-2">
          <span className="text-2xl font-bold text-primary-600">
            {formatPrice(currentPrice)} {currency}
          </span>
          
          {shouldShowWholesalePrice && (
            <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
              <Crown className="w-3 h-3" />
              سعر الجملة
            </div>
          )}
        </div>

        {/* Original Price (if discounted) */}
        {originalPrice && originalPrice > currentPrice && (
          <div className="flex items-center gap-2">
            <span className="text-lg text-gray-500 line-through">
              {formatPrice(originalPrice)} {currency}
            </span>
            <div className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
              خصم {Math.round(((originalPrice - currentPrice) / originalPrice) * 100)}%
            </div>
          </div>
        )}
      </div>

      {/* Wholesale Benefits for Verified Users */}
      {shouldShowWholesalePrice && savings > 0 && (
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <Crown className="w-4 h-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">
              مميزات حساب الجملة
            </span>
          </div>
          <div className="space-y-1 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">السعر العادي:</span>
              <span className="text-gray-600">{formatPrice(retailPrice)} {currency}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-yellow-700 font-medium">سعر الجملة:</span>
              <span className="text-yellow-800 font-bold">{formatPrice(wholesalePrice)} {currency}</span>
            </div>
            <div className="flex items-center justify-between border-t border-yellow-200 pt-1">
              <span className="text-green-700 font-medium">توفر:</span>
              <span className="text-green-800 font-bold">
                {formatPrice(savings)} {currency} ({savingsPercentage}%)
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Call to Action for Non-Wholesale Users */}
      {!shouldShowWholesalePrice && userSession.isLoggedIn && userSession.userType === 'retail' && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <Info className="w-4 h-4 text-blue-600 mt-0.5" />
            <div className="flex-1">
              <p className="text-sm text-blue-800">
                <span className="font-medium">هل أنت تاجر؟</span> احصل على أسعار خاصة وخصومات إضافية
              </p>
              <button
                onClick={() => window.dispatchEvent(new CustomEvent('openWholesaleRegistration'))}
                className="text-xs text-blue-600 hover:text-blue-700 font-medium mt-1 underline"
              >
                سجل حساب جملة الآن
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Login Prompt for Guest Users */}
      {!userSession.isLoggedIn && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <Tag className="w-4 h-4 text-gray-600" />
            <div className="flex-1">
              <p className="text-sm text-gray-700">
                <span className="font-medium">سجل دخولك</span> لرؤية الأسعار الخاصة والعروض الحصرية
              </p>
              <div className="flex gap-2 mt-2">
                <button
                  onClick={() => window.dispatchEvent(new CustomEvent('openLogin'))}
                  className="text-xs bg-primary-600 text-white px-3 py-1 rounded hover:bg-primary-700"
                >
                  تسجيل الدخول
                </button>
                <button
                  onClick={() => window.dispatchEvent(new CustomEvent('openRegister'))}
                  className="text-xs border border-primary-600 text-primary-600 px-3 py-1 rounded hover:bg-primary-50"
                >
                  إنشاء حساب
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Discount Info for Wholesale Users */}
      {shouldShowWholesalePrice && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <TrendingDown className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-green-800">
              خصومات الكميات
            </span>
          </div>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center p-2 bg-white rounded border">
              <div className="font-medium text-gray-900">10+ قطع</div>
              <div className="text-green-600">خصم 5%</div>
            </div>
            <div className="text-center p-2 bg-white rounded border">
              <div className="font-medium text-gray-900">50+ قطعة</div>
              <div className="text-green-600">خصم 10%</div>
            </div>
            <div className="text-center p-2 bg-white rounded border">
              <div className="font-medium text-gray-900">100+ قطعة</div>
              <div className="text-green-600">خصم 15%</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Helper function to update user session
export const updateUserSession = (session: UserSession) => {
  try {
    localStorage.setItem('userSession', JSON.stringify(session))
    window.dispatchEvent(new CustomEvent('userSessionUpdated'))
  } catch (error) {
    console.error('Error updating user session:', error)
  }
}

// Helper function to get current user session
export const getCurrentUserSession = (): UserSession => {
  try {
    const session = localStorage.getItem('userSession')
    if (session) {
      return JSON.parse(session)
    }
  } catch (error) {
    console.error('Error reading user session:', error)
  }
  
  return {
    isLoggedIn: false,
    userType: 'retail',
    isWholesaleVerified: false
  }
}
