'use client'

import { useState } from 'react'
import CategoryManager from '@/components/admin/CategoryManager'
import ProductManager from '@/components/admin/ProductManager'
import ContactInfoManager from '@/components/admin/ContactInfoManager'
import {
  Users,
  Package,
  ShoppingCart,
  DollarSign,
  TrendingUp,
  Settings,
  BarChart3,
  FileText,
  Bell,
  Calendar,
  Eye,
  Edit,
  Trash2,
  Plus,
  Phone
} from 'lucide-react'

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('dashboard')

  const stats = [
    {
      title: 'إجمالي المبيعات',
      value: '125,430 جنيه',
      change: '+12.5%',
      icon: DollarSign,
      color: 'text-green-600'
    },
    {
      title: 'الطلبات الجديدة',
      value: '48',
      change: '****%',
      icon: ShoppingCart,
      color: 'text-blue-600'
    },
    {
      title: 'العملاء النشطين',
      value: '1,234',
      change: '+15.3%',
      icon: Users,
      color: 'text-purple-600'
    },
    {
      title: 'المنتجات',
      value: '567',
      change: '****%',
      icon: Package,
      color: 'text-orange-600'
    }
  ]

  const recentOrders = [
    { id: '#12345', customer: 'أحمد محمد', amount: '1,250 جنيه', status: 'مكتمل', date: '2024-01-15' },
    { id: '#12346', customer: 'فاطمة علي', amount: '890 جنيه', status: 'قيد التنفيذ', date: '2024-01-15' },
    { id: '#12347', customer: 'محمد حسن', amount: '2,100 جنيه', status: 'جديد', date: '2024-01-14' },
    { id: '#12348', customer: 'سارة أحمد', amount: '750 جنيه', status: 'مكتمل', date: '2024-01-14' },
  ]

  const menuItems = [
    { id: 'dashboard', label: 'لوحة التحكم', icon: BarChart3 },
    { id: 'products', label: 'إدارة المنتجات', icon: Package },
    { id: 'categories', label: 'التصنيفات', icon: FileText },
    { id: 'orders', label: 'الطلبات', icon: ShoppingCart },
    { id: 'customers', label: 'العملاء', icon: Users },
    { id: 'payments', label: 'المدفوعات', icon: DollarSign },
    { id: 'reports', label: 'التقارير', icon: TrendingUp },
    { id: 'contact', label: 'معلومات الاتصال', icon: Phone },
    { id: 'settings', label: 'الإعدادات', icon: Settings },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مكتمل': return 'bg-green-100 text-green-800'
      case 'قيد التنفيذ': return 'bg-yellow-100 text-yellow-800'
      case 'جديد': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">لوحة تحكم مركز البدوي</h1>
              <p className="text-gray-600">إدارة شاملة للموقع والمبيعات</p>
            </div>
            <div className="flex items-center gap-4">
              <button className="relative p-2 text-gray-600 hover:text-gray-900">
                <Bell className="w-6 h-6" />
                <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold">
                  م
                </div>
                <span className="font-medium">المدير</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm min-h-screen">
          <nav className="p-4">
            <ul className="space-y-2">
              {menuItems.map((item) => (
                <li key={item.id}>
                  <button
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-right transition-colors ${
                      activeTab === item.id
                        ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <item.icon className="w-5 h-5" />
                    {item.label}
                  </button>
                </li>
              ))}
            </ul>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {stats.map((stat, index) => (
                  <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                        <p className={`text-sm ${stat.color}`}>{stat.change}</p>
                      </div>
                      <div className={`p-3 rounded-full bg-gray-100`}>
                        <stat.icon className={`w-6 h-6 ${stat.color}`} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Charts Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Sales Chart */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold mb-4">المبيعات الشهرية</h3>
                  <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <p className="text-gray-500">رسم بياني للمبيعات</p>
                  </div>
                </div>

                {/* Orders Chart */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold mb-4">الطلبات اليومية</h3>
                  <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <p className="text-gray-500">رسم بياني للطلبات</p>
                  </div>
                </div>
              </div>

              {/* Recent Orders */}
              <div className="bg-white rounded-lg shadow-sm">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">الطلبات الأخيرة</h3>
                    <button className="text-primary-600 hover:text-primary-700 font-medium">
                      عرض الكل
                    </button>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الطلب</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">العميل</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {recentOrders.map((order) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {order.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {order.customer}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {order.amount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                              {order.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {order.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div className="flex items-center gap-2">
                              <button className="text-blue-600 hover:text-blue-800">
                                <Eye className="w-4 h-4" />
                              </button>
                              <button className="text-green-600 hover:text-green-800">
                                <Edit className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'products' && (
            <ProductManager />
          )}

          {activeTab === 'categories' && (
            <CategoryManager />
          )}

          {activeTab === 'payments' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900">إدارة المدفوعات</h2>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold mb-4">طرق الدفع المتاحة</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">فودافون كاش</h4>
                    <p className="text-sm text-gray-600">رقم المحفظة: 01012345678</p>
                    <span className="inline-block mt-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded">نشط</span>
                  </div>
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">إنستاباي</h4>
                    <p className="text-sm text-gray-600">رقم المحفظة: 01012345678</p>
                    <span className="inline-block mt-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded">نشط</span>
                  </div>
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">البنك الأهلي</h4>
                    <p className="text-sm text-gray-600">رقم الحساب: 1234567890</p>
                    <span className="inline-block mt-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded">نشط</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'contact' && (
            <ContactInfoManager />
          )}

          {/* Other tabs content will be added here */}
          {activeTab !== 'dashboard' && activeTab !== 'products' && activeTab !== 'categories' && activeTab !== 'payments' && activeTab !== 'contact' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {menuItems.find(item => item.id === activeTab)?.label}
              </h2>
              <p className="text-gray-600">سيتم إضافة محتوى هذا القسم قريباً</p>
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
