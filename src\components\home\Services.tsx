'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  Wrench, 
  Truck, 
  Home, 
  Smartphone, 
  Car, 
  Paintbrush,
  ArrowLeft,
  Clock,
  Star,
  CheckCircle
} from 'lucide-react'

interface Service {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  price: string
  duration: string
  rating: number
  features: string[]
  popular: boolean
}

const services: Service[] = [
  {
    id: '1',
    name: 'صيانة الأجهزة الإلكترونية',
    description: 'خدمة صيانة شاملة للهواتف والحاسوب واللابتوب',
    icon: <Smartphone className="w-8 h-8" />,
    price: 'يبدأ من 100 ريال',
    duration: '2-4 ساعات',
    rating: 4.9,
    features: ['تشخيص مجاني', 'ضمان 6 أشهر', 'قطع غيار أصلية'],
    popular: true
  },
  {
    id: '2',
    name: 'توصيل وتركيب',
    description: 'خدمة توصيل وتركيب الأجهزة والأثاث',
    icon: <Truck className="w-8 h-8" />,
    price: 'يبدأ من 50 ريال',
    duration: '1-3 ساعات',
    rating: 4.8,
    features: ['توصيل سريع', 'تركيب احترافي', 'ضمان التركيب'],
    popular: false
  },
  {
    id: '3',
    name: 'خدمات منزلية',
    description: 'تنظيف، صيانة، وإصلاحات منزلية متنوعة',
    icon: <Home className="w-8 h-8" />,
    price: 'يبدأ من 80 ريال',
    duration: '2-6 ساعات',
    rating: 4.7,
    features: ['فريق محترف', 'أدوات متطورة', 'خدمة شاملة'],
    popular: true
  },
  {
    id: '4',
    name: 'صيانة السيارات',
    description: 'خدمة صيانة وإصلاح السيارات في الموقع',
    icon: <Car className="w-8 h-8" />,
    price: 'يبدأ من 150 ريال',
    duration: '1-4 ساعات',
    rating: 4.6,
    features: ['خدمة في الموقع', 'فنيين معتمدين', 'قطع غيار أصلية'],
    popular: false
  },
  {
    id: '5',
    name: 'أعمال الدهان والديكور',
    description: 'دهان الجدران وأعمال الديكور الداخلي',
    icon: <Paintbrush className="w-8 h-8" />,
    price: 'يبدأ من 200 ريال',
    duration: '4-8 ساعات',
    rating: 4.8,
    features: ['ألوان عالية الجودة', 'تصاميم عصرية', 'ضمان سنتين'],
    popular: false
  },
  {
    id: '6',
    name: 'صيانة عامة',
    description: 'خدمات صيانة متنوعة للمنزل والمكتب',
    icon: <Wrench className="w-8 h-8" />,
    price: 'يبدأ من 75 ريال',
    duration: '1-3 ساعات',
    rating: 4.5,
    features: ['استجابة سريعة', 'أسعار منافسة', 'خدمة موثوقة'],
    popular: false
  }
]

export default function Services() {
  const [selectedService, setSelectedService] = useState<string | null>(null)

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={14}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ))
  }

  return (
    <div className="space-y-8">
      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service) => (
          <div
            key={service.id}
            className={`relative bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-all duration-300 cursor-pointer border-2 ${
              selectedService === service.id ? 'border-primary-500' : 'border-transparent'
            }`}
            onClick={() => setSelectedService(selectedService === service.id ? null : service.id)}
          >
            {/* Popular Badge */}
            {service.popular && (
              <div className="absolute -top-2 -right-2 bg-yellow-400 text-gray-800 px-3 py-1 rounded-full text-sm font-bold">
                الأكثر طلباً
              </div>
            )}

            {/* Service Icon */}
            <div className="flex items-center justify-center w-16 h-16 bg-primary-100 text-primary-600 rounded-lg mb-4">
              {service.icon}
            </div>

            {/* Service Info */}
            <h3 className="text-xl font-semibold text-gray-800 mb-2">{service.name}</h3>
            <p className="text-gray-600 mb-4">{service.description}</p>

            {/* Rating */}
            <div className="flex items-center gap-2 mb-3">
              <div className="flex">{renderStars(service.rating)}</div>
              <span className="text-sm text-gray-600">({service.rating})</span>
            </div>

            {/* Price and Duration */}
            <div className="flex justify-between items-center mb-4">
              <span className="text-lg font-bold text-primary-600">{service.price}</span>
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <Clock size={14} />
                <span>{service.duration}</span>
              </div>
            </div>

            {/* Features (shown when selected) */}
            {selectedService === service.id && (
              <div className="border-t pt-4 mt-4 animate-slide-up">
                <h4 className="font-semibold text-gray-800 mb-2">مميزات الخدمة:</h4>
                <ul className="space-y-1">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                      <CheckCircle size={14} className="text-green-500" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 mt-4">
              <Link
                href={`/services/${service.id}`}
                className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-center"
              >
                اطلب الخدمة
              </Link>
              <Link
                href={`/services/${service.id}`}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-3 rounded-lg transition-colors duration-200 flex items-center justify-center"
              >
                <ArrowLeft size={16} />
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white rounded-lg p-8 text-center">
        <h3 className="text-2xl font-bold mb-4">هل تحتاج خدمة مخصصة؟</h3>
        <p className="text-blue-100 mb-6">
          تواصل معنا للحصول على استشارة مجانية وعرض سعر مخصص لاحتياجاتك
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/contact"
            className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-bold py-3 px-8 rounded-lg transition-colors duration-200"
          >
            تواصل معنا
          </Link>
          <Link
            href="/services/custom"
            className="bg-transparent border-2 border-white hover:bg-white hover:text-primary-600 font-bold py-3 px-8 rounded-lg transition-colors duration-200"
          >
            طلب خدمة مخصصة
          </Link>
        </div>
      </div>
    </div>
  )
}
