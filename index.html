<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز البدوي - الموقع الرسمي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .top-bar {
            background: #2563eb;
            color: white;
            padding: 10px 0;
            font-size: 14px;
        }
        
        .top-bar .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 0;
        }
        
        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            background: #2563eb;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 24px;
        }
        
        .nav {
            display: flex;
            gap: 30px;
        }
        
        .nav a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav a:hover {
            color: #2563eb;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .cart-icon {
            position: relative;
            font-size: 24px;
            color: #333;
            cursor: pointer;
        }
        
        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-secondary {
            background: #f3f4f6;
            color: #333;
            border: 1px solid #d1d5db;
        }
        
        .btn-secondary:hover {
            background: #e5e7eb;
        }
        
        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .hero .highlight {
            color: #fbbf24;
            display: block;
        }
        
        .hero p {
            font-size: 20px;
            margin-bottom: 30px;
            color: #dbeafe;
        }
        
        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-yellow {
            background: #fbbf24;
            color: #1f2937;
        }
        
        .btn-yellow:hover {
            background: #f59e0b;
        }
        
        .btn-outline {
            background: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .btn-outline:hover {
            background: white;
            color: #2563eb;
        }
        
        /* Features */
        .features {
            padding: 60px 0;
            background: white;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
        }
        
        .feature {
            text-align: center;
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: #eff6ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: #2563eb;
        }
        
        .feature h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #6b7280;
        }
        
        /* Products */
        .products {
            padding: 60px 0;
            background: #f9fafb;
        }
        
        .section-title {
            text-align: center;
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 50px;
            color: #1f2937;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
        }
        
        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .product-image {
            height: 200px;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #9ca3af;
        }
        
        .product-info {
            padding: 20px;
        }
        
        .product-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .product-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .stars {
            color: #fbbf24;
        }
        
        .rating-count {
            color: #6b7280;
            font-size: 14px;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .current-price {
            font-size: 20px;
            font-weight: bold;
            color: #2563eb;
        }
        
        .old-price {
            color: #9ca3af;
            text-decoration: line-through;
        }
        
        .discount {
            background: #ef4444;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        /* Services */
        .services {
            padding: 60px 0;
            background: white;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .service-card {
            text-align: center;
            padding: 40px 20px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.3s;
        }
        
        .service-card:hover {
            border-color: #2563eb;
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.1);
        }
        
        .service-icon {
            width: 80px;
            height: 80px;
            background: #eff6ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: #2563eb;
        }
        
        .service-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .service-description {
            color: #6b7280;
            margin-bottom: 20px;
        }
        
        .service-price {
            font-size: 18px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 20px;
        }
        
        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 50px 0 20px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }
        
        .footer-section h4 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .footer-section ul {
            list-style: none;
        }
        
        .footer-section ul li {
            margin-bottom: 10px;
        }
        
        .footer-section ul li a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-section ul li a:hover {
            color: white;
        }
        
        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 20px;
            text-align: center;
            color: #9ca3af;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav {
                display: none;
            }
            
            .hero h1 {
                font-size: 32px;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .top-bar .container {
                flex-direction: column;
                gap: 10px;
            }
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-fade-in {
            animation: fadeInUp 0.6s ease-out;
        }
        
        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(100%);
            transition: transform 0.3s;
            z-index: 1000;
            max-width: 300px;
        }
        
        .notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div>
                <i class="fas fa-phone"></i> +966 50 123 4567
                <span style="margin: 0 20px;">|</span>
                <i class="fas fa-truck"></i> توصيل مجاني للطلبات أكثر من 200 ريال
            </div>
            <div>مرحباً بكم في مركز البدوي</div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">مركز البدوي</div>
            
            <nav class="nav">
                <a href="#home">الرئيسية</a>
                <a href="#products">المنتجات</a>
                <a href="#services">الخدمات</a>
                <a href="#about">من نحن</a>
                <a href="#contact">اتصل بنا</a>
            </nav>
            
            <div class="header-actions">
                <div class="cart-icon" onclick="showCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">3</span>
                </div>
                <button class="btn btn-primary" onclick="showLogin()">تسجيل الدخول</button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <h1 class="animate-fade-in">
                مرحباً بكم في
                <span class="highlight">مركز البدوي</span>
            </h1>

            <!-- الجملة التسويقية الجديدة -->
            <div style="margin: 20px 0; animation: fadeInUp 0.8s ease-out 0.3s both;">
                <p style="font-size: 28px; font-weight: bold; color: #fbbf24; margin-bottom: 8px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                    اسم له تاريخ
                </p>
                <p style="font-size: 18px; color: #dbeafe; font-style: italic;">
                    الاسم يعني الثقة والجودة
                </p>
            </div>

            <p class="animate-fade-in" style="animation-delay: 0.5s;">أفضل المنتجات والخدمات مع توصيل سريع وآمن إلى باب منزلك في جميع أنحاء مصر</p>
            <div class="hero-actions animate-fade-in">
                <a href="#products" class="btn btn-yellow">
                    <i class="fas fa-shopping-bag"></i> تسوق الآن
                </a>
                <a href="#services" class="btn btn-outline">
                    <i class="fas fa-tools"></i> اطلب خدمة
                </a>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="features">
        <div class="container">
            <div class="features-grid">
                <div class="feature animate-fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h3>توصيل سريع</h3>
                    <p>توصيل في نفس اليوم لجميع أنحاء المملكة</p>
                </div>
                
                <div class="feature animate-fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>دفع آمن</h3>
                    <p>حماية كاملة لبياناتك المالية</p>
                </div>
                
                <div class="feature animate-fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>خدمة 24/7</h3>
                    <p>دعم فني متواصل طوال أيام الأسبوع</p>
                </div>
                
                <div class="feature animate-fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>منتجات أصلية</h3>
                    <p>ضمان الجودة والأصالة لجميع منتجاتنا</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Products -->
    <section id="products" class="products">
        <div class="container">
            <h2 class="section-title">المنتجات المميزة</h2>
            <div class="products-grid">
                <div class="product-card animate-fade-in">
                    <div class="product-image">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">هاتف ذكي متطور</h3>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-count">(124 تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="current-price">2500 ريال</span>
                            <span class="old-price">3000 ريال</span>
                            <span class="discount">17% خصم</span>
                        </div>
                        <button class="btn btn-primary" onclick="addToCart('هاتف ذكي متطور')">
                            <i class="fas fa-cart-plus"></i> أضف للسلة
                        </button>
                    </div>
                </div>

                <div class="product-card animate-fade-in">
                    <div class="product-image">
                        <i class="fas fa-laptop"></i>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">لابتوب عالي الأداء</h3>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-count">(89 تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="current-price">4500 ريال</span>
                            <span class="old-price">5200 ريال</span>
                            <span class="discount">13% خصم</span>
                        </div>
                        <button class="btn btn-primary" onclick="addToCart('لابتوب عالي الأداء')">
                            <i class="fas fa-cart-plus"></i> أضف للسلة
                        </button>
                    </div>
                </div>

                <div class="product-card animate-fade-in">
                    <div class="product-image">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">ساعة ذكية رياضية</h3>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="rating-count">(156 تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="current-price">800 ريال</span>
                            <span class="old-price">1000 ريال</span>
                            <span class="discount">20% خصم</span>
                        </div>
                        <button class="btn btn-primary" onclick="addToCart('ساعة ذكية رياضية')">
                            <i class="fas fa-cart-plus"></i> أضف للسلة
                        </button>
                    </div>
                </div>

                <div class="product-card animate-fade-in">
                    <div class="product-image">
                        <i class="fas fa-headphones"></i>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">سماعات لاسلكية</h3>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-count">(203 تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="current-price">350 ريال</span>
                            <span class="old-price">450 ريال</span>
                            <span class="discount">22% خصم</span>
                        </div>
                        <button class="btn btn-primary" onclick="addToCart('سماعات لاسلكية')">
                            <i class="fas fa-cart-plus"></i> أضف للسلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services -->
    <section id="services" class="services">
        <div class="container">
            <h2 class="section-title">خدماتنا</h2>
            <div class="services-grid">
                <div class="service-card animate-fade-in">
                    <div class="service-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3 class="service-title">صيانة الأجهزة</h3>
                    <p class="service-description">صيانة شاملة للهواتف والحاسوب مع ضمان الجودة</p>
                    <div class="service-price">يبدأ من 100 ريال</div>
                    <button class="btn btn-primary" onclick="requestService('صيانة الأجهزة')">
                        <i class="fas fa-calendar-plus"></i> اطلب الخدمة
                    </button>
                </div>

                <div class="service-card animate-fade-in">
                    <div class="service-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="service-title">خدمات منزلية</h3>
                    <p class="service-description">تنظيف وصيانة وإصلاحات منزلية متنوعة</p>
                    <div class="service-price">يبدأ من 80 ريال</div>
                    <button class="btn btn-primary" onclick="requestService('خدمات منزلية')">
                        <i class="fas fa-calendar-plus"></i> اطلب الخدمة
                    </button>
                </div>

                <div class="service-card animate-fade-in">
                    <div class="service-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <h3 class="service-title">توصيل وتركيب</h3>
                    <p class="service-description">توصيل وتركيب الأجهزة والأثاث بأيدي خبيرة</p>
                    <div class="service-price">يبدأ من 50 ريال</div>
                    <button class="btn btn-primary" onclick="requestService('توصيل وتركيب')">
                        <i class="fas fa-calendar-plus"></i> اطلب الخدمة
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo" style="margin-bottom: 20px;">مركز البدوي</div>
                    <p style="color: #d1d5db;">مركز تجاري إلكتروني احترافي يوفر أفضل المنتجات والخدمات مع توصيل سريع وآمن في جميع أنحاء مصر.</p>
                    <div style="color: #fbbf24; font-size: 14px; margin-top: 10px; font-weight: 500;">
                        اسم له تاريخ - الاسم يعني الثقة والجودة
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="#home">الرئيسية</a></li>
                        <li><a href="#products">المنتجات</a></li>
                        <li><a href="#services">الخدمات</a></li>
                        <li><a href="#about">من نحن</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>خدمة العملاء</h4>
                    <ul>
                        <li><a href="#">مركز المساعدة</a></li>
                        <li><a href="#">الشحن والتوصيل</a></li>
                        <li><a href="#">الإرجاع والاستبدال</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div style="color: #d1d5db;">
                        <p><i class="fas fa-phone"></i> +966 50 123 4567</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> الرياض، السعودية</p>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>© 2024 مركز البدوي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Notification -->
    <div id="notification" class="notification">
        🎉 مرحباً بك في مركز البدوي! اسم له تاريخ - الاسم يعني الثقة والجودة
    </div>

    <script>
        // Show notification
        setTimeout(() => {
            document.getElementById('notification').classList.add('show');
        }, 1000);

        // Hide notification after 5 seconds
        setTimeout(() => {
            document.getElementById('notification').classList.remove('show');
        }, 6000);

        // Cart functionality
        let cartCount = 3;

        function addToCart(productName) {
            cartCount++;
            document.querySelector('.cart-count').textContent = cartCount;
            showNotification(`تم إضافة "${productName}" إلى سلة التسوق! 🛒`);
        }

        function showCart() {
            showNotification('سيتم فتح سلة التسوق قريباً! 🛒');
        }

        function showLogin() {
            showNotification('سيتم فتح نافذة تسجيل الدخول قريباً! 👤');
        }

        function requestService(serviceName) {
            showNotification(`تم طلب خدمة "${serviceName}"! سنتواصل معك قريباً 🔧`);
        }

        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        // Observe all animatable elements
        document.querySelectorAll('.product-card, .service-card, .feature').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
