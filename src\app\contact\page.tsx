'use client'

import { useState, useEffect } from 'react'
import { Phone, Mail, MapPin, Send, MessageCircle, Clock, Facebook, Twitter, Instagram } from 'lucide-react'
import toast from 'react-hot-toast'

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })

  const [contactInfo, setContactInfo] = useState({
    phone: '+20 ************',
    whatsapp: '+20 ************',
    email: '<EMAIL>',
    supportEmail: '<EMAIL>',
    address: 'شارع التحرير، المعادي',
    city: 'القاهرة',
    workingHours: {
      weekdays: 'الأحد - الخميس: 9:00 ص - 9:00 م',
      weekend: 'الجمعة - السبت: 10:00 ص - 8:00 م'
    },
    paymentNumbers: {
      vodafoneCash: '01012345678',
      instaPay: '01012345678',
      orangeMoney: '01112345678',
      etisalatCash: '01212345678'
    }
  })

  // Load contact info from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('contactInfo')
      if (saved) {
        try {
          const parsedInfo = JSON.parse(saved)
          setContactInfo(prev => ({ ...prev, ...parsedInfo }))
        } catch (e) {
          console.error('Error parsing contact info:', e)
        }
      }
    }

    // Listen for contact info updates
    const handleContactUpdate = (event: any) => {
      const newInfo = event.detail
      setContactInfo(prev => ({ ...prev, ...newInfo }))
    }

    window.addEventListener('contactInfoUpdated', handleContactUpdate)
    return () => window.removeEventListener('contactInfoUpdated', handleContactUpdate)
  }, [])
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً')
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      })
    } catch (error) {
      toast.error('حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">تواصل معنا</h1>
          <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
            نحن هنا لمساعدتك! تواصل معنا في أي وقت وسنكون سعداء للرد على استفساراتك
          </p>
        </div>
      </section>

      {/* Contact Info & Form */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Info */}
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-8">معلومات الاتصال</h2>
              
              {/* Contact Methods */}
              <div className="space-y-6 mb-8">
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg">
                    <Phone size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">الهاتف</h3>
                    <p className="text-gray-600">{contactInfo.phone}</p>
                    <p className="text-gray-500 text-sm">متاح من 9 صباحاً إلى 10 مساءً</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg">
                    <Mail size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">البريد الإلكتروني</h3>
                    <p className="text-gray-600">{contactInfo.email}</p>
                    <p className="text-gray-500 text-sm">نرد خلال 24 ساعة</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg">
                    <MapPin size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">العنوان</h3>
                    <p className="text-gray-600">{contactInfo.address}, {contactInfo.city}</p>
                    <p className="text-gray-500 text-sm">مركز الأعمال الرئيسي</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg">
                    <MessageCircle size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">الدردشة المباشرة</h3>
                    <p className="text-gray-600">متاح 24/7 لخدمتكم</p>
                    <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      ابدأ المحادثة الآن
                    </button>
                  </div>
                </div>
              </div>

              {/* Working Hours */}
              <div className="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  <Clock size={20} />
                  ساعات العمل
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">السبت - الخميس</span>
                    <span className="text-gray-800 font-medium">9:00 ص - 10:00 م</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">الجمعة</span>
                    <span className="text-gray-800 font-medium">2:00 م - 10:00 م</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">خدمة العملاء</span>
                    <span className="text-gray-800 font-medium">24/7</span>
                  </div>
                </div>
              </div>

              {/* Payment Methods */}
              <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-6 mb-8">
                <h3 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  💳 طرق الدفع المتاحة
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-red-500 text-white rounded-lg p-3 text-center">
                    <div className="font-medium text-sm">فودافون كاش</div>
                    <div className="text-red-100 text-xs">{contactInfo.paymentNumbers.vodafoneCash}</div>
                  </div>
                  <div className="bg-blue-600 text-white rounded-lg p-3 text-center">
                    <div className="font-medium text-sm">إنستاباي</div>
                    <div className="text-blue-100 text-xs">{contactInfo.paymentNumbers.instaPay}</div>
                  </div>
                  <div className="bg-orange-500 text-white rounded-lg p-3 text-center">
                    <div className="font-medium text-sm">أورانج موني</div>
                    <div className="text-orange-100 text-xs">{contactInfo.paymentNumbers.orangeMoney}</div>
                  </div>
                  <div className="bg-green-600 text-white rounded-lg p-3 text-center">
                    <div className="font-medium text-sm">اتصالات كاش</div>
                    <div className="text-green-100 text-xs">{contactInfo.paymentNumbers.etisalatCash}</div>
                  </div>
                </div>
                <div className="text-center mt-3 text-gray-600 text-xs">
                  💰 نقبل جميع طرق الدفع المصرية + التحويل البنكي
                </div>
              </div>

              {/* Social Media */}
              <div>
                <h3 className="font-semibold text-gray-800 mb-4">تابعنا على</h3>
                <div className="flex space-x-4 space-x-reverse">
                  <a href="#" className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <Facebook size={20} />
                  </a>
                  <a href="#" className="flex items-center justify-center w-10 h-10 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors">
                    <Twitter size={20} />
                  </a>
                  <a href="#" className="flex items-center justify-center w-10 h-10 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors">
                    <Instagram size={20} />
                  </a>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-gray-50 rounded-lg p-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">أرسل لنا رسالة</h3>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="أدخل اسمك الكامل"
                    />
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="05xxxxxxxx"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    الموضوع *
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">اختر الموضوع</option>
                    <option value="product">استفسار عن منتج</option>
                    <option value="service">طلب خدمة</option>
                    <option value="order">استفسار عن طلب</option>
                    <option value="complaint">شكوى</option>
                    <option value="suggestion">اقتراح</option>
                    <option value="partnership">شراكة تجارية</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    الرسالة *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                    placeholder="اكتب رسالتك هنا..."
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      جاري الإرسال...
                    </>
                  ) : (
                    <>
                      <Send size={18} />
                      إرسال الرسالة
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-12">الأسئلة الشائعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h3 className="font-semibold text-gray-800 mb-3">كيف يمكنني تتبع طلبي؟</h3>
              <p className="text-gray-600 text-sm">
                يمكنك تتبع طلبك من خلال صفحة "طلباتي" في حسابك، أو عبر رقم الطلب المرسل إليك.
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h3 className="font-semibold text-gray-800 mb-3">ما هي طرق الدفع المتاحة؟</h3>
              <p className="text-gray-600 text-sm">
                نقبل فودافون كاش، إنستاباي، أورانج موني، اتصالات كاش، والتحويل البنكي، والدفع عند الاستلام.
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h3 className="font-semibold text-gray-800 mb-3">كم تستغرق عملية التوصيل؟</h3>
              <p className="text-gray-600 text-sm">
                التوصيل خلال 24-48 ساعة داخل القاهرة والجيزة، و3-5 أيام لباقي المحافظات في مصر.
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h3 className="font-semibold text-gray-800 mb-3">هل يمكنني إرجاع المنتج؟</h3>
              <p className="text-gray-600 text-sm">
                نعم، يمكنك إرجاع المنتج خلال 14 يوم من تاريخ الاستلام حسب شروط الإرجاع.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
