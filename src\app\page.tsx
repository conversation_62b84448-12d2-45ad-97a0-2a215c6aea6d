'use client'

import { useRouter } from 'next/navigation'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { Search, ShoppingBag, Truck, Mic } from 'lucide-react'

export default function Home() {
  const router = useRouter()
  const { t } = useLanguage()
  const { currentTheme } = useTheme()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement)
    const query = formData.get('search') as string
    if (query?.trim()) {
      router.push(`/search?q=${encodeURIComponent(query)}`)
    }
  }

  const handleShopNow = () => {
    router.push('/products')
  }

  const handleRequestService = () => {
    router.push('/services')
  }

  return (
    <div
      className="min-h-screen text-white"
      style={{
        background: `linear-gradient(135deg, ${currentTheme.colors.primary}, ${currentTheme.colors.secondary})`
      }}
    >
      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            {t('welcomeMessage')}
            <span className="block" style={{ color: currentTheme.colors.accent }}>مركز البدوي</span>
          </h1>

          <p className="text-2xl md:text-3xl font-bold mb-4" style={{ color: currentTheme.colors.accent }}>
            {t('brandSlogan')}
          </p>

          <p className="text-lg md:text-xl mb-8 opacity-90">
            {t('brandDescription')}
          </p>

          <p className="text-xl mb-8 opacity-80">
            {t('mainDescription')}
          </p>

          {/* Search Box */}
          <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-8">
            <div className="flex bg-white rounded-lg overflow-hidden shadow-lg">
              <input
                name="search"
                type="text"
                placeholder={t('searchPlaceholder')}
                className="flex-1 px-6 py-4 text-gray-800 text-lg focus:outline-none"
              />
              <button
                type="button"
                className="px-4 py-4 text-gray-400 hover:text-gray-600"
                title="البحث الصوتي"
              >
                <Mic className="w-5 h-5" />
              </button>
              <button
                type="submit"
                className="px-8 py-4 font-bold text-gray-800 transition-colors"
                style={{ backgroundColor: currentTheme.colors.accent }}
              >
                <Search className="w-5 h-5" />
              </button>
            </div>
          </form>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button
              onClick={handleShopNow}
              className="font-bold py-4 px-8 rounded-lg transition-all transform hover:scale-105 flex items-center justify-center gap-2"
              style={{ backgroundColor: currentTheme.colors.accent, color: currentTheme.colors.background }}
            >
              <ShoppingBag className="w-5 h-5" />
              {t('shopNow')}
            </button>

            <button
              onClick={handleRequestService}
              className="bg-transparent border-2 border-white hover:bg-white font-bold py-4 px-8 rounded-lg transition-all transform hover:scale-105 flex items-center justify-center gap-2"
              style={{
                borderColor: 'white',
                color: 'white'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'white'
                e.currentTarget.style.color = currentTheme.colors.primary
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = 'white'
              }}
            >
              <Truck className="w-5 h-5" />
              {t('requestService')}
            </button>
          </div>

          {/* Quick Stats */}
          <div className="flex flex-wrap gap-6 justify-center opacity-80">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>{t('productsAvailable')}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
              <span>{t('deliveryAllGovernorates')}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span>{t('support247')}</span>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section
        className="py-16"
        style={{ backgroundColor: currentTheme.colors.surface, color: currentTheme.colors.text }}
      >
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8" style={{ color: currentTheme.colors.primary }}>
            لماذا مركز البدوي؟
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="p-6 rounded-lg" style={{ backgroundColor: currentTheme.colors.background }}>
              <div className="text-4xl mb-4">🚚</div>
              <h3 className="text-xl font-bold mb-2" style={{ color: currentTheme.colors.primary }}>
                {t('fastDelivery')}
              </h3>
              <p style={{ color: currentTheme.colors.textSecondary }}>
                {t('fastDeliveryDesc')}
              </p>
            </div>
            <div className="p-6 rounded-lg" style={{ backgroundColor: currentTheme.colors.background }}>
              <div className="text-4xl mb-4">🛡️</div>
              <h3 className="text-xl font-bold mb-2" style={{ color: currentTheme.colors.primary }}>
                {t('securePayment')}
              </h3>
              <p style={{ color: currentTheme.colors.textSecondary }}>
                {t('securePaymentDesc')}
              </p>
            </div>
            <div className="p-6 rounded-lg" style={{ backgroundColor: currentTheme.colors.background }}>
              <div className="text-4xl mb-4">⭐</div>
              <h3 className="text-xl font-bold mb-2" style={{ color: currentTheme.colors.primary }}>
                {t('originalProducts')}
              </h3>
              <p style={{ color: currentTheme.colors.textSecondary }}>
                {t('originalProductsDesc')}
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
