export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-r from-blue-600 to-blue-800 text-white">
      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            مرحباً بكم في
            <span className="block text-yellow-300">مركز البدوي</span>
          </h1>

          <p className="text-2xl md:text-3xl font-bold text-yellow-200 mb-4">
            اسم له تاريخ
          </p>

          <p className="text-lg md:text-xl text-blue-100 mb-8">
            الاسم يعني الثقة والجودة
          </p>

          <p className="text-xl mb-8 text-blue-100">
            أفضل المنتجات والخدمات مع توصيل سريع وآمن إلى باب منزلك في جميع أنحاء مصر
          </p>

          {/* Search Box */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="flex bg-white rounded-lg overflow-hidden shadow-lg">
              <input
                type="text"
                placeholder="ابحث في أكثر من 1000 منتج وخدمة..."
                className="flex-1 px-6 py-4 text-gray-800 text-lg focus:outline-none"
              />
              <button className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 px-8 py-4 font-bold">
                بحث
              </button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-bold py-4 px-8 rounded-lg transition-all transform hover:scale-105">
              🛒 تسوق الآن
            </button>

            <button className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-600 font-bold py-4 px-8 rounded-lg transition-all transform hover:scale-105">
              🚚 اطلب خدمة
            </button>
          </div>

          {/* Quick Stats */}
          <div className="flex flex-wrap gap-6 justify-center text-blue-100">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>+1000 منتج متاح</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
              <span>توصيل لجميع المحافظات</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span>دعم فني 24/7</span>
            </div>
          </div>
        </div>
      </section>

      {/* Simple Content */}
      <section className="py-16 bg-white text-gray-800">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">
            لماذا مركز البدوي؟
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="p-6">
              <div className="text-4xl mb-4">🚚</div>
              <h3 className="text-xl font-bold mb-2">توصيل سريع</h3>
              <p>توصيل في نفس اليوم داخل المدينة</p>
            </div>
            <div className="p-6">
              <div className="text-4xl mb-4">🛡️</div>
              <h3 className="text-xl font-bold mb-2">دفع آمن</h3>
              <p>حماية كاملة لبياناتك المالية</p>
            </div>
            <div className="p-6">
              <div className="text-4xl mb-4">⭐</div>
              <h3 className="text-xl font-bold mb-2">منتجات أصلية</h3>
              <p>ضمان الجودة والأصالة</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
