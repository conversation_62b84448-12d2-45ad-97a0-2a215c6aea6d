'use client'

import { useState, useEffect } from 'react'
import Hero from '@/components/home/<USER>'
import FeaturedProducts from '@/components/home/<USER>'
import Services from '@/components/home/<USER>'
import About from '@/components/home/<USER>'
import Contact from '@/components/home/<USER>'
import LocationModal from '@/components/modals/LocationModal'

export default function Home() {
  const [showLocationModal, setShowLocationModal] = useState(false)

  useEffect(() => {
    // Check if user location is already set
    const userLocation = localStorage.getItem('userLocation')
    if (!userLocation) {
      setShowLocationModal(true)
    }
  }, [])

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Hero />
      
      {/* Featured Products */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            المنتجات المميزة
          </h2>
          <FeaturedProducts />
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            خدماتنا
          </h2>
          <Services />
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <About />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <Contact />
        </div>
      </section>

      {/* Location Modal */}
      {showLocationModal && (
        <LocationModal 
          isOpen={showLocationModal}
          onClose={() => setShowLocationModal(false)}
        />
      )}
    </div>
  )
}
