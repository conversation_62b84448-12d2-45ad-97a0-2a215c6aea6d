const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.url === '/' || req.url === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز البدوي - اسم له تاريخ</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 2rem;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .slogan {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature h3 {
            margin-bottom: 0.5rem;
            color: #4ecdc4;
        }
        
        .status {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            display: inline-block;
            margin: 1rem 0;
            border: 1px solid #4caf50;
        }
        
        .links {
            margin-top: 2rem;
        }
        
        .link {
            display: inline-block;
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: transform 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .note {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            border-radius: 10px;
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">ب</div>
        <h1>مركز البدوي</h1>
        <p class="slogan">اسم له تاريخ</p>
        
        <div class="status">
            ✅ الخادم يعمل بنجاح
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🔐 نظام المستخدمين</h3>
                <p>تسجيل دخول وإنشاء حسابات منفصلة</p>
            </div>
            <div class="feature">
                <h3>🛒 نظام التسوق</h3>
                <p>سلة تسوق وإتمام شراء متكامل</p>
            </div>
            <div class="feature">
                <h3>🎨 الثيمات</h3>
                <p>6 ثيمات مع محرر ألوان مخصص</p>
            </div>
            <div class="feature">
                <h3>🌍 الترجمة</h3>
                <p>3 لغات مع ترجمة شاملة</p>
            </div>
            <div class="feature">
                <h3>⚙️ الإعدادات</h3>
                <p>إدارة شاملة لمعلومات الموقع</p>
            </div>
            <div class="feature">
                <h3>📱 متجاوب</h3>
                <p>يعمل على جميع الأجهزة</p>
            </div>
        </div>
        
        <div class="links">
            <a href="/admin/settings" class="link">⚙️ إعدادات الموقع</a>
            <a href="/login" class="link">🔐 تسجيل الدخول</a>
            <a href="/register" class="link">📝 إنشاء حساب</a>
            <a href="/products" class="link">🛍️ المنتجات</a>
            <a href="/services" class="link">🔧 الخدمات</a>
        </div>
        
        <div class="note">
            <strong>ملاحظة:</strong> هذا خادم مؤقت. للحصول على الموقع الكامل مع Next.js، يرجى إصلاح مشكلة Node.js أو npm.
        </div>
    </div>
</body>
</html>
    `);
  } else if (req.url === '/admin/settings') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الموقع - مركز البدوي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1a202c;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .header-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .tabs {
            background: white;
            border-radius: 20px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }
        
        .tabs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .tab {
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            background: #f8fafc;
        }
        
        .tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .tab-icon {
            width: 50px;
            height: 50px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
        }
        
        .tab.active .tab-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .section-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
        }
        
        .form-group input,
        .form-group select {
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .contact-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .contact-item {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .contact-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
        }
        
        .contact-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .contact-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .contact-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 8px;
        }
        
        .btn-secondary {
            background: #6b7280;
        }
        
        .btn-danger {
            background: #ef4444;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="header-icon">⚙️</div>
            <div>
                <h1 style="font-size: 2rem; margin-bottom: 0.5rem;">إعدادات الموقع</h1>
                <p style="opacity: 0.9;">إدارة شاملة لجميع إعدادات الموقع ومعلومات الاتصال</p>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="tabs">
            <div class="tabs-grid">
                <div class="tab active" onclick="switchTab('contact')">
                    <div class="tab-icon">📞</div>
                    <h3>معلومات الاتصال</h3>
                    <p style="font-size: 0.875rem; opacity: 0.8; margin-top: 0.5rem;">إدارة بيانات التواصل</p>
                </div>
                <div class="tab" onclick="switchTab('payment')">
                    <div class="tab-icon">💳</div>
                    <h3>معلومات الدفع</h3>
                    <p style="font-size: 0.875rem; opacity: 0.8; margin-top: 0.5rem;">طرق الدفع المتاحة</p>
                </div>
                <div class="tab" onclick="switchTab('social')">
                    <div class="tab-icon">🌐</div>
                    <h3>وسائل التواصل</h3>
                    <p style="font-size: 0.875rem; opacity: 0.8; margin-top: 0.5rem;">الحسابات الاجتماعية</p>
                </div>
                <div class="tab" onclick="switchTab('business')">
                    <div class="tab-icon">🏢</div>
                    <h3>معلومات الأعمال</h3>
                    <p style="font-size: 0.875rem; opacity: 0.8; margin-top: 0.5rem;">معلومات الشركة</p>
                </div>
            </div>
        </div>
        
        <div class="content">
            <div id="contact-tab" class="tab-content">
                <div class="section-header">
                    <div class="section-icon">📞</div>
                    <div>
                        <h2>معلومات الاتصال</h2>
                        <p style="color: #6b7280; margin-top: 0.25rem;">إدارة جميع وسائل التواصل مع العملاء</p>
                    </div>
                </div>
                
                <div style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem;">
                    <h3 style="margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        ➕ إضافة معلومات اتصال جديدة
                    </h3>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>نوع الاتصال</label>
                            <select id="contactType">
                                <option value="phone">هاتف</option>
                                <option value="email">بريد إلكتروني</option>
                                <option value="address">عنوان</option>
                                <option value="whatsapp">واتساب</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>التسمية</label>
                            <input type="text" id="contactLabel" placeholder="مثل: الهاتف الرئيسي">
                        </div>
                        <div class="form-group">
                            <label>القيمة</label>
                            <input type="text" id="contactValue" placeholder="مثل: +20 ************">
                        </div>
                        <div class="form-group">
                            <label style="opacity: 0;">إضافة</label>
                            <button class="btn" onclick="addContact()">➕ إضافة</button>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 style="margin-bottom: 1rem;">معلومات الاتصال الحالية</h3>
                    <div class="contact-list" id="contactList">
                        <div class="contact-item">
                            <div class="contact-header">
                                <div class="contact-icon">📞</div>
                                <div>
                                    <h4>الهاتف الرئيسي</h4>
                                    <p style="color: #6b7280;">+20 ************</p>
                                </div>
                            </div>
                            <div class="contact-actions">
                                <button class="btn btn-small btn-secondary">✏️ تعديل</button>
                                <button class="btn btn-small btn-danger">🗑️ حذف</button>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-header">
                                <div class="contact-icon">📧</div>
                                <div>
                                    <h4>البريد الإلكتروني</h4>
                                    <p style="color: #6b7280;"><EMAIL></p>
                                </div>
                            </div>
                            <div class="contact-actions">
                                <button class="btn btn-small btn-secondary">✏️ تعديل</button>
                                <button class="btn btn-small btn-danger">🗑️ حذف</button>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-header">
                                <div class="contact-icon">📍</div>
                                <div>
                                    <h4>العنوان</h4>
                                    <p style="color: #6b7280;">القاهرة، مصر</p>
                                </div>
                            </div>
                            <div class="contact-actions">
                                <button class="btn btn-small btn-secondary">✏️ تعديل</button>
                                <button class="btn btn-small btn-danger">🗑️ حذف</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="other-tabs" class="tab-content" style="display: none;">
                <div class="empty-state">
                    <div class="empty-icon">🚧</div>
                    <h3>قريباً</h3>
                    <p>هذا القسم قيد التطوير</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchTab(tabName) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Add active class to clicked tab
            event.target.closest('.tab').classList.add('active');
            
            // Show/hide content
            if (tabName === 'contact') {
                document.getElementById('contact-tab').style.display = 'block';
                document.getElementById('other-tabs').style.display = 'none';
            } else {
                document.getElementById('contact-tab').style.display = 'none';
                document.getElementById('other-tabs').style.display = 'block';
            }
        }
        
        function addContact() {
            const type = document.getElementById('contactType').value;
            const label = document.getElementById('contactLabel').value;
            const value = document.getElementById('contactValue').value;
            
            if (!label || !value) {
                alert('يرجى ملء جميع الحقول');
                return;
            }
            
            const icons = {
                phone: '📞',
                email: '📧',
                address: '📍',
                whatsapp: '💬'
            };
            
            const contactList = document.getElementById('contactList');
            const newContact = document.createElement('div');
            newContact.className = 'contact-item';
            newContact.innerHTML = \`
                <div class="contact-header">
                    <div class="contact-icon">\${icons[type]}</div>
                    <div>
                        <h4>\${label}</h4>
                        <p style="color: #6b7280;">\${value}</p>
                    </div>
                </div>
                <div class="contact-actions">
                    <button class="btn btn-small btn-secondary">✏️ تعديل</button>
                    <button class="btn btn-small btn-danger" onclick="this.closest('.contact-item').remove()">🗑️ حذف</button>
                </div>
            \`;
            
            contactList.appendChild(newContact);
            
            // Clear form
            document.getElementById('contactLabel').value = '';
            document.getElementById('contactValue').value = '';
            
            alert('تم إضافة معلومات الاتصال بنجاح!');
        }
    </script>
</body>
</html>
    `);
  } else {
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - مركز البدوي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            margin: 0;
        }
        .container {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 { font-size: 4rem; margin-bottom: 1rem; }
        p { font-size: 1.2rem; margin-bottom: 2rem; }
        a {
            color: #4ecdc4;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        a:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container">
        <h1>404</h1>
        <p>الصفحة غير موجودة</p>
        <a href="/">العودة للصفحة الرئيسية</a>
    </div>
</body>
</html>
    `);
  }
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(\`🚀 الخادم يعمل على http://localhost:\${PORT}\`);
  console.log(\`📱 صفحة الإعدادات: http://localhost:\${PORT}/admin/settings\`);
});
