'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Minus, Plus, Trash2, ShoppingBag, ArrowRight } from 'lucide-react'
import { useCart } from '@/contexts/CartContext'
import { useAuth } from '@/contexts/AuthContext'
import toast from 'react-hot-toast'

export default function CartPage() {
  const { items, updateQuantity, removeItem, clearCart, totalPrice } = useCart()
  const { user } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return
    updateQuantity(id, newQuantity)
  }

  const handleRemoveItem = (id: string) => {
    removeItem(id)
    toast.success('تم حذف المنتج من السلة')
  }

  const handleClearCart = () => {
    if (window.confirm('هل أنت متأكد من حذف جميع المنتجات؟')) {
      clearCart()
      toast.success('تم تفريغ السلة')
    }
  }

  const handleCheckout = async () => {
    if (!user) {
      toast.error('يرجى تسجيل الدخول أولاً')
      return
    }

    if (items.length === 0) {
      toast.error('السلة فارغة')
      return
    }

    setIsProcessing(true)
    
    try {
      // Simulate checkout process
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast.success('تم إرسال طلبك بنجاح!')
      clearCart()
      
      // Redirect to orders page
      window.location.href = '/orders'
    } catch (error) {
      toast.error('حدث خطأ في معالجة الطلب')
    } finally {
      setIsProcessing(false)
    }
  }


  const shippingCost = totalPrice > 200 ? 0 : 25
  const finalTotal = totalPrice + shippingCost

  if (items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <ShoppingBag className="w-24 h-24 text-gray-300 mx-auto mb-6" />
          <h1 className="text-2xl font-bold text-gray-800 mb-4">سلة التسوق فارغة</h1>
          <p className="text-gray-600 mb-8">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
          <Link
            href="/products"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2"
          >
            <ArrowRight size={20} />
            تصفح المنتجات
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-800">سلة التسوق</h1>
        <button
          onClick={handleClearCart}
          className="text-red-600 hover:text-red-700 font-medium"
        >
          تفريغ السلة
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md">
            {items.map((item, index) => (
              <div key={item.id} className={`p-6 ${index !== items.length - 1 ? 'border-b' : ''}`}>
                <div className="flex gap-4">
                  {/* Product Image */}
                  <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-gray-500 text-xs">صورة</span>
                  </div>

                  {/* Product Info */}
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-800 mb-2">{item.name}</h3>
                    <p className="text-primary-600 font-bold mb-3">{item.price} ريال</p>

                    {/* Quantity Controls */}
                    <div className="flex items-center gap-3">
                      <div className="flex items-center border border-gray-300 rounded-lg">
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          className="p-2 hover:bg-gray-100 rounded-r-lg"
                        >
                          <Minus size={16} />
                        </button>
                        <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          className="p-2 hover:bg-gray-100 rounded-l-lg"
                        >
                          <Plus size={16} />
                        </button>
                      </div>

                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        className="text-red-600 hover:text-red-700 p-2"
                      >
                        <Trash2 size={18} />
                      </button>
                    </div>
                  </div>

                  {/* Item Total */}
                  <div className="text-left">
                    <p className="font-bold text-gray-800">
                      {(item.price * item.quantity).toFixed(2)} ريال
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
            <h2 className="text-xl font-bold text-gray-800 mb-6">ملخص الطلب</h2>

            <div className="space-y-4 mb-6">
              <div className="flex justify-between">
                <span className="text-gray-600">المجموع الفرعي</span>
                <span className="font-medium">{totalPrice.toFixed(2)} ريال</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">الشحن</span>
                <span className="font-medium">
                  {shippingCost === 0 ? 'مجاني' : `${shippingCost} ريال`}
                </span>
              </div>
              
              {totalPrice < 200 && (
                <div className="text-sm text-gray-500 bg-yellow-50 p-3 rounded-lg">
                  أضف {(200 - totalPrice).toFixed(2)} ريال أخرى للحصول على الشحن المجاني
                </div>
              )}
              
              <div className="border-t pt-4">
                <div className="flex justify-between text-lg font-bold">
                  <span>المجموع الكلي</span>
                  <span className="text-primary-600">{finalTotal.toFixed(2)} ريال</span>
                </div>
              </div>
            </div>

            {/* Checkout Button */}
            <button
              onClick={handleCheckout}
              disabled={isProcessing}
              className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 mb-4"
            >
              {isProcessing ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري المعالجة...
                </div>
              ) : (
                'إتمام الطلب'
              )}
            </button>

            {/* Continue Shopping */}
            <Link
              href="/products"
              className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-center block"
            >
              متابعة التسوق
            </Link>

            {/* Security Info */}
            <div className="mt-6 text-center text-sm text-gray-500">
              <p>🔒 معاملة آمنة ومشفرة</p>
              <p>✅ ضمان استرداد الأموال</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
