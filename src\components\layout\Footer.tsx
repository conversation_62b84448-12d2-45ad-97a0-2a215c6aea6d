'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Facebook, Twitter, Instagram, Phone, Mail, MapPin, Youtube, Linkedin, MessageCircle, Clock } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { useSiteSettings } from '@/contexts/SiteSettingsContext'

export default function Footer() {
  const { t } = useLanguage()
  const { currentTheme } = useTheme()
  const { settings } = useSiteSettings()
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear())

  useEffect(() => {
    setCurrentYear(new Date().getFullYear())
  }, [])

  const getSocialIcon = (platform: string) => {
    switch (platform) {
      case 'facebook': return Facebook
      case 'instagram': return Instagram
      case 'twitter': return Twitter
      case 'youtube': return Youtube
      case 'linkedin': return Linkedin
      default: return Facebook
    }
  }

  const getContactIcon = (type: string) => {
    switch (type) {
      case 'phone': return Phone
      case 'email': return Mail
      case 'address': return MapPin
      case 'whatsapp': return MessageCircle
      default: return Phone
    }
  }

  const activeContactInfo = settings.contactInfo.filter(contact => contact.isActive)
  const activeSocialMedia = settings.socialMedia.filter(social => social.isActive)

  return (
    <footer 
      className="border-t"
      style={{ 
        backgroundColor: currentTheme.colors.surface,
        borderColor: currentTheme.colors.border
      }}
    >
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-bold mb-2" style={{ color: currentTheme.colors.primary }}>
                {settings.businessInfo.name}
              </h3>
              <p className="text-sm font-medium" style={{ color: currentTheme.colors.textSecondary }}>
                {settings.businessInfo.slogan}
              </p>
            </div>
            <p className="text-sm leading-relaxed" style={{ color: currentTheme.colors.textSecondary }}>
              {t('footerDescription')}
            </p>
            <div className="flex items-center gap-2 text-sm" style={{ color: currentTheme.colors.textSecondary }}>
              <Clock className="w-4 h-4" />
              <span>{t('workingHours')}: {settings.businessInfo.workingHours}</span>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold" style={{ color: currentTheme.colors.text }}>
              {t('quickLinks')}
            </h4>
            <nav className="space-y-2">
              <Link 
                href="/" 
                className="block text-sm hover:underline transition-colors"
                style={{ color: currentTheme.colors.textSecondary }}
              >
                {t('home')}
              </Link>
              <Link 
                href="/products" 
                className="block text-sm hover:underline transition-colors"
                style={{ color: currentTheme.colors.textSecondary }}
              >
                {t('products')}
              </Link>
              <Link 
                href="/services" 
                className="block text-sm hover:underline transition-colors"
                style={{ color: currentTheme.colors.textSecondary }}
              >
                {t('services')}
              </Link>
              <Link 
                href="/about" 
                className="block text-sm hover:underline transition-colors"
                style={{ color: currentTheme.colors.textSecondary }}
              >
                {t('about')}
              </Link>
              <Link 
                href="/contact" 
                className="block text-sm hover:underline transition-colors"
                style={{ color: currentTheme.colors.textSecondary }}
              >
                {t('contactUs')}
              </Link>
            </nav>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold" style={{ color: currentTheme.colors.text }}>
              {t('contactUs')}
            </h4>
            <div className="space-y-3">
              {activeContactInfo.map((contact) => {
                const Icon = getContactIcon(contact.type)
                return (
                  <div key={contact.id} className="flex items-center gap-3">
                    <Icon className="w-4 h-4 flex-shrink-0" style={{ color: currentTheme.colors.primary }} />
                    <div>
                      <div className="text-xs font-medium" style={{ color: currentTheme.colors.textSecondary }}>
                        {contact.label}
                      </div>
                      <div className="text-sm" style={{ color: currentTheme.colors.text }}>
                        {contact.type === 'email' ? (
                          <a href={`mailto:${contact.value}`} className="hover:underline">
                            {contact.value}
                          </a>
                        ) : contact.type === 'phone' || contact.type === 'whatsapp' ? (
                          <a href={`tel:${contact.value}`} className="hover:underline">
                            {contact.value}
                          </a>
                        ) : (
                          contact.value
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Social Media */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold" style={{ color: currentTheme.colors.text }}>
              {t('followUs')}
            </h4>
            <div className="space-y-3">
              {activeSocialMedia.map((social) => {
                const Icon = getSocialIcon(social.platform)
                return (
                  <a
                    key={social.id}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 hover:opacity-80 transition-opacity"
                  >
                    <Icon className="w-4 h-4" style={{ color: currentTheme.colors.primary }} />
                    <span className="text-sm" style={{ color: currentTheme.colors.text }}>
                      {social.name}
                    </span>
                  </a>
                )
              })}
            </div>
            
            {/* Social Icons Row */}
            <div className="flex gap-3 pt-2">
              {activeSocialMedia.map((social) => {
                const Icon = getSocialIcon(social.platform)
                return (
                  <a
                    key={`icon-${social.id}`}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110"
                    style={{ backgroundColor: currentTheme.colors.primary + '20' }}
                  >
                    <Icon className="w-4 h-4" style={{ color: currentTheme.colors.primary }} />
                  </a>
                )
              })}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div 
          className="mt-8 pt-8 border-t text-center"
          style={{ borderColor: currentTheme.colors.border }}
        >
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
              © {currentYear} {settings.businessInfo.name}. {t('allRightsReserved')}
            </p>
            <div className="flex items-center gap-4 text-sm">
              <Link 
                href="/privacy" 
                className="hover:underline"
                style={{ color: currentTheme.colors.textSecondary }}
              >
                سياسة الخصوصية
              </Link>
              <Link 
                href="/terms" 
                className="hover:underline"
                style={{ color: currentTheme.colors.textSecondary }}
              >
                الشروط والأحكام
              </Link>
              <Link 
                href="/admin/settings" 
                className="hover:underline"
                style={{ color: currentTheme.colors.primary }}
              >
                {t('settings')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
