# خادم HTTP بسيط لعرض موقع كوبرا
# Simple HTTP Server for Cobra Store

Write-Host "🐍 بدء تشغيل خادم موقع كوبرا..." -ForegroundColor Green
Write-Host "Starting Cobra Store Server..." -ForegroundColor Green

# إنشاء خادم HTTP بسيط
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:3000/")

try {
    $listener.Start()
    Write-Host ""
    Write-Host "✅ الخادم يعمل الآن على:" -ForegroundColor Green
    Write-Host "✅ Server is running on:" -ForegroundColor Green
    Write-Host "🌐 http://localhost:3000" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "اضغط Ctrl+C لإيقاف الخادم" -ForegroundColor Yellow
    Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
    Write-Host ""

    # فتح المتصفح تلقائياً
    Start-Process "http://localhost:3000"

    while ($listener.IsListening) {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response

        Write-Host "📥 طلب جديد: $($request.Url)" -ForegroundColor Blue

        # قراءة ملف HTML
        $htmlPath = Join-Path $PSScriptRoot "preview.html"
        
        if (Test-Path $htmlPath) {
            $html = Get-Content $htmlPath -Raw -Encoding UTF8
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($html)
            
            $response.ContentType = "text/html; charset=utf-8"
            $response.ContentLength64 = $buffer.Length
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
        } else {
            # صفحة خطأ بسيطة
            $errorHtml = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>خطأ - متجر كوبرا</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; direction: rtl; }
        .error { color: #e74c3c; font-size: 24px; margin-bottom: 20px; }
        .message { color: #666; font-size: 16px; }
    </style>
</head>
<body>
    <div class="error">❌ ملف الموقع غير موجود</div>
    <div class="message">يرجى التأكد من وجود ملف preview.html في نفس المجلد</div>
    <div class="message">File preview.html not found in the same directory</div>
</body>
</html>
"@
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($errorHtml)
            $response.ContentType = "text/html; charset=utf-8"
            $response.ContentLength64 = $buffer.Length
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
        }

        $response.Close()
    }
} catch {
    Write-Host "❌ خطأ في تشغيل الخادم: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Server error: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($listener.IsListening) {
        $listener.Stop()
        Write-Host "🛑 تم إيقاف الخادم" -ForegroundColor Yellow
        Write-Host "🛑 Server stopped" -ForegroundColor Yellow
    }
}
