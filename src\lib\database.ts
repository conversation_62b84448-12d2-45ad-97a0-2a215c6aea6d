// Local Database System for Al-Badawi Center
// This simulates a database using localStorage for persistence

export interface Product {
  id: string
  name: string
  description: string
  price: number
  wholesalePrice: number
  originalPrice?: number
  categoryId: string
  subcategoryId?: string
  image: string
  images: string[]
  inStock: boolean
  stockQuantity: number
  rating: number
  reviewCount: number
  features: string[]
  brand: string
  sku: string
  weight: number
  dimensions: string
  isActive: boolean
  isFeatured: boolean
  createdAt: string
  updatedAt: string
}

export interface Category {
  id: string
  name: string
  description: string
  parentId?: string
  level: number
  image: string
  isActive: boolean
  productCount: number
  createdAt: string
}

export interface Customer {
  id: string
  name: string
  email: string
  phone: string
  address: string
  city: string
  governorate: string
  userType: 'retail' | 'wholesale'
  isWholesaleVerified: boolean
  wholesaleCode?: string
  registrationDate: string
  lastOrderDate?: string
  totalOrders: number
  totalSpent: number
  isActive: boolean
}

export interface Service {
  id: string
  name: string
  description: string
  price: number
  wholesalePrice: number
  categoryId: string
  duration: string
  isActive: boolean
  features: string[]
  createdAt: string
}

export interface WholesaleRequest {
  id: string
  customerId: string
  customerName: string
  customerEmail: string
  businessName: string
  businessType: string
  taxNumber?: string
  requestDate: string
  status: 'pending' | 'approved' | 'rejected'
  verificationCode?: string
  approvedDate?: string
  notes?: string
}

class LocalDatabase {
  private getStorageKey(table: string): string {
    return `albadawi_${table}`
  }

  // Generic CRUD operations
  private getAll<T>(table: string): T[] {
    try {
      const data = localStorage.getItem(this.getStorageKey(table))
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error(`Error reading ${table}:`, error)
      return []
    }
  }

  private save<T>(table: string, data: T[]): void {
    try {
      localStorage.setItem(this.getStorageKey(table), JSON.stringify(data))
    } catch (error) {
      console.error(`Error saving ${table}:`, error)
    }
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
  }

  // Products
  getAllProducts(): Product[] {
    return this.getAll<Product>('products')
  }

  getProduct(id: string): Product | null {
    const products = this.getAllProducts()
    return products.find(p => p.id === id) || null
  }

  addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Product {
    const products = this.getAllProducts()
    const newProduct: Product = {
      ...product,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    products.push(newProduct)
    this.save('products', products)
    return newProduct
  }

  updateProduct(id: string, updates: Partial<Product>): Product | null {
    const products = this.getAllProducts()
    const index = products.findIndex(p => p.id === id)
    if (index === -1) return null

    products[index] = {
      ...products[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }
    this.save('products', products)
    return products[index]
  }

  deleteProduct(id: string): boolean {
    const products = this.getAllProducts()
    const filteredProducts = products.filter(p => p.id !== id)
    if (filteredProducts.length === products.length) return false
    
    this.save('products', filteredProducts)
    return true
  }

  getProductsByCategory(categoryId: string): Product[] {
    const products = this.getAllProducts()
    return products.filter(p => p.categoryId === categoryId || p.subcategoryId === categoryId)
  }

  // Categories
  getAllCategories(): Category[] {
    return this.getAll<Category>('categories')
  }

  getCategory(id: string): Category | null {
    const categories = this.getAllCategories()
    return categories.find(c => c.id === id) || null
  }

  addCategory(category: Omit<Category, 'id' | 'createdAt' | 'productCount'>): Category {
    const categories = this.getAllCategories()
    const newCategory: Category = {
      ...category,
      id: this.generateId(),
      productCount: 0,
      createdAt: new Date().toISOString()
    }
    categories.push(newCategory)
    this.save('categories', categories)
    return newCategory
  }

  updateCategory(id: string, updates: Partial<Category>): Category | null {
    const categories = this.getAllCategories()
    const index = categories.findIndex(c => c.id === id)
    if (index === -1) return null

    categories[index] = { ...categories[index], ...updates }
    this.save('categories', categories)
    return categories[index]
  }

  deleteCategory(id: string): boolean {
    const categories = this.getAllCategories()
    const filteredCategories = categories.filter(c => c.id !== id)
    if (filteredCategories.length === categories.length) return false
    
    this.save('categories', filteredCategories)
    return true
  }

  // Customers
  getAllCustomers(): Customer[] {
    return this.getAll<Customer>('customers')
  }

  getCustomer(id: string): Customer | null {
    const customers = this.getAllCustomers()
    return customers.find(c => c.id === id) || null
  }

  getCustomerByEmail(email: string): Customer | null {
    const customers = this.getAllCustomers()
    return customers.find(c => c.email === email) || null
  }

  addCustomer(customer: Omit<Customer, 'id' | 'registrationDate' | 'totalOrders' | 'totalSpent'>): Customer {
    const customers = this.getAllCustomers()
    const newCustomer: Customer = {
      ...customer,
      id: this.generateId(),
      registrationDate: new Date().toISOString(),
      totalOrders: 0,
      totalSpent: 0
    }
    customers.push(newCustomer)
    this.save('customers', customers)
    return newCustomer
  }

  updateCustomer(id: string, updates: Partial<Customer>): Customer | null {
    const customers = this.getAllCustomers()
    const index = customers.findIndex(c => c.id === id)
    if (index === -1) return null

    customers[index] = { ...customers[index], ...updates }
    this.save('customers', customers)
    return customers[index]
  }

  // Services
  getAllServices(): Service[] {
    return this.getAll<Service>('services')
  }

  addService(service: Omit<Service, 'id' | 'createdAt'>): Service {
    const services = this.getAllServices()
    const newService: Service = {
      ...service,
      id: this.generateId(),
      createdAt: new Date().toISOString()
    }
    services.push(newService)
    this.save('services', services)
    return newService
  }

  // Wholesale Requests
  getAllWholesaleRequests(): WholesaleRequest[] {
    return this.getAll<WholesaleRequest>('wholesale_requests')
  }

  addWholesaleRequest(request: Omit<WholesaleRequest, 'id' | 'requestDate' | 'status'>): WholesaleRequest {
    const requests = this.getAllWholesaleRequests()
    const newRequest: WholesaleRequest = {
      ...request,
      id: this.generateId(),
      requestDate: new Date().toISOString(),
      status: 'pending'
    }
    requests.push(newRequest)
    this.save('wholesale_requests', requests)
    
    // Send email notification (simulated)
    this.sendWholesaleRequestNotification(newRequest)
    
    return newRequest
  }

  approveWholesaleRequest(id: string, verificationCode: string): boolean {
    const requests = this.getAllWholesaleRequests()
    const index = requests.findIndex(r => r.id === id)
    if (index === -1) return false

    requests[index] = {
      ...requests[index],
      status: 'approved',
      verificationCode,
      approvedDate: new Date().toISOString()
    }
    this.save('wholesale_requests', requests)

    // Update customer to wholesale
    const customer = this.getCustomerByEmail(requests[index].customerEmail)
    if (customer) {
      this.updateCustomer(customer.id, {
        userType: 'wholesale',
        isWholesaleVerified: true,
        wholesaleCode: verificationCode
      })
    }

    return true
  }

  private sendWholesaleRequestNotification(request: WholesaleRequest): void {
    // Simulate sending email to admin
    console.log('📧 طلب تفعيل حساب جملة جديد:', {
      customerName: request.customerName,
      customerEmail: request.customerEmail,
      businessName: request.businessName,
      requestDate: request.requestDate
    })
    
    // In a real application, this would send an actual email
    // using a service like EmailJS, SendGrid, or similar
  }

  // Initialize with sample data
  initializeSampleData(): void {
    // Only initialize if no data exists
    if (this.getAllProducts().length === 0) {
      this.initializeProducts()
    }
    if (this.getAllCategories().length === 0) {
      this.initializeCategories()
    }
    if (this.getAllCustomers().length === 0) {
      this.initializeCustomers()
    }
    if (this.getAllServices().length === 0) {
      this.initializeServices()
    }
  }

  private initializeProducts(): void {
    const sampleProducts: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'هاتف ذكي متطور',
        description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
        price: 2500,
        wholesalePrice: 2200,
        originalPrice: 3000,
        categoryId: '2',
        image: '/images/phone1.jpg',
        images: ['/images/phone1.jpg'],
        inStock: true,
        stockQuantity: 25,
        rating: 4.8,
        reviewCount: 124,
        features: ['شاشة OLED', 'كاميرا 108MP', 'بطارية 5000mAh'],
        brand: 'سامسونج',
        sku: 'PHONE-001',
        weight: 0.2,
        dimensions: '15x7x0.8 سم',
        isActive: true,
        isFeatured: true
      }
    ]

    sampleProducts.forEach(product => this.addProduct(product))
  }

  private initializeCategories(): void {
    const sampleCategories: Omit<Category, 'id' | 'createdAt' | 'productCount'>[] = [
      {
        name: 'الإلكترونيات',
        description: 'جميع الأجهزة الإلكترونية والتقنية',
        level: 0,
        image: '/images/electronics.jpg',
        isActive: true
      },
      {
        name: 'الهواتف الذكية',
        description: 'هواتف ذكية من جميع الماركات',
        parentId: '1',
        level: 1,
        image: '/images/phones.jpg',
        isActive: true
      }
    ]

    sampleCategories.forEach(category => this.addCategory(category))
  }

  private initializeCustomers(): void {
    const sampleCustomers: Omit<Customer, 'id' | 'registrationDate' | 'totalOrders' | 'totalSpent'>[] = [
      {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '+20 ************',
        address: 'شارع التحرير، المعادي',
        city: 'القاهرة',
        governorate: 'القاهرة',
        userType: 'retail',
        isWholesaleVerified: false,
        isActive: true
      },
      {
        name: 'فاطمة علي التاجرة',
        email: '<EMAIL>',
        phone: '+20 ************',
        address: 'شارع الجمهورية، وسط البلد',
        city: 'القاهرة',
        governorate: 'القاهرة',
        userType: 'wholesale',
        isWholesaleVerified: true,
        wholesaleCode: '123456',
        isActive: true
      },
      {
        name: 'محمد حسن',
        email: '<EMAIL>',
        phone: '+20 ************',
        address: 'شارع النيل، المنيل',
        city: 'القاهرة',
        governorate: 'القاهرة',
        userType: 'wholesale',
        isWholesaleVerified: false,
        isActive: true
      }
    ]

    sampleCustomers.forEach(customer => this.addCustomer(customer))
  }

  private initializeServices(): void {
    // Initialize with sample services
  }
}

// Export singleton instance
export const db = new LocalDatabase()

// Initialize sample data on first load
if (typeof window !== 'undefined') {
  db.initializeSampleData()
}
