'use client'

import { useState, useEffect } from 'react'
import { Save, <PERSON>, Eye, <PERSON>Off, <PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Shield, Database, Globe } from 'lucide-react'

interface SiteSettings {
  siteName: string
  siteDescription: string
  siteKeywords: string
  currency: string
  language: string
  timezone: string
  maintenanceMode: boolean
  allowRegistration: boolean
  emailNotifications: boolean
  smsNotifications: boolean
  autoBackup: boolean
  backupFrequency: string
}

interface ContactSettings {
  phone: string
  whatsapp: string
  email: string
  supportEmail: string
  salesEmail: string
  address: string
  city: string
  governorate: string
  postalCode: string
  workingHours: {
    weekdays: string
    weekend: string
    holidays: string
  }
}

interface PaymentSettings {
  vodafoneCash: {
    number: string
    name: string
    enabled: boolean
  }
  instaPay: {
    number: string
    name: string
    enabled: boolean
  }
  orangeMoney: {
    number: string
    name: string
    enabled: boolean
  }
  etisalatCash: {
    number: string
    name: string
    enabled: boolean
  }
  bankAccounts: {
    nbe: {
      accountNumber: string
      accountName: string
      branch: string
      iban: string
      enabled: boolean
    }
    cib: {
      accountNumber: string
      accountName: string
      branch: string
      iban: string
      enabled: boolean
    }
  }
  cashOnDelivery: {
    enabled: boolean
    extraFee: number
  }
}

export default function AdvancedSettings() {
  const [activeTab, setActiveTab] = useState('site')
  const [isEditing, setIsEditing] = useState(false)
  const [showPasswords, setShowPasswords] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Site Settings
  const [siteSettings, setSiteSettings] = useState<SiteSettings>({
    siteName: 'مركز البدوي',
    siteDescription: 'مركز البدوي - اسم له تاريخ وأصالة. الاسم يعني الثقة والجودة',
    siteKeywords: 'مركز البدوي, متجر إلكتروني, مصر, فودافون كاش, إنستاباي',
    currency: 'EGP',
    language: 'ar',
    timezone: 'Africa/Cairo',
    maintenanceMode: false,
    allowRegistration: true,
    emailNotifications: true,
    smsNotifications: true,
    autoBackup: true,
    backupFrequency: 'daily'
  })

  // Contact Settings
  const [contactSettings, setContactSettings] = useState<ContactSettings>({
    phone: '+20 ************',
    whatsapp: '+20 ************',
    email: '<EMAIL>',
    supportEmail: '<EMAIL>',
    salesEmail: '<EMAIL>',
    address: 'شارع التحرير، المعادي',
    city: 'القاهرة',
    governorate: 'القاهرة',
    postalCode: '11728',
    workingHours: {
      weekdays: 'الأحد - الخميس: 9:00 ص - 9:00 م',
      weekend: 'الجمعة - السبت: 10:00 ص - 8:00 م',
      holidays: 'العطل الرسمية: مغلق'
    }
  })

  // Payment Settings
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    vodafoneCash: {
      number: '***********',
      name: 'مركز البدوي للتجارة',
      enabled: true
    },
    instaPay: {
      number: '***********',
      name: 'مركز البدوي للتجارة',
      enabled: true
    },
    orangeMoney: {
      number: '***********',
      name: 'مركز البدوي للتجارة',
      enabled: true
    },
    etisalatCash: {
      number: '***********',
      name: 'مركز البدوي للتجارة',
      enabled: true
    },
    bankAccounts: {
      nbe: {
        accountNumber: '****************',
        accountName: 'مركز البدوي للتجارة',
        branch: 'فرع المعادي',
        iban: '*****************************',
        enabled: true
      },
      cib: {
        accountNumber: '****************',
        accountName: 'مركز البدوي للتجارة',
        branch: 'فرع مصر الجديدة',
        iban: '*****************************',
        enabled: true
      }
    },
    cashOnDelivery: {
      enabled: true,
      extraFee: 25
    }
  })

  // Load settings from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedSite = localStorage.getItem('siteSettings')
      const savedContact = localStorage.getItem('contactSettings')
      const savedPayment = localStorage.getItem('paymentSettings')
      
      if (savedSite) {
        try {
          setSiteSettings(JSON.parse(savedSite))
        } catch (e) {
          console.error('Error loading site settings:', e)
        }
      }
      
      if (savedContact) {
        try {
          setContactSettings(JSON.parse(savedContact))
        } catch (e) {
          console.error('Error loading contact settings:', e)
        }
      }
      
      if (savedPayment) {
        try {
          setPaymentSettings(JSON.parse(savedPayment))
        } catch (e) {
          console.error('Error loading payment settings:', e)
        }
      }
    }
  }, [])

  const handleSave = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('siteSettings', JSON.stringify(siteSettings))
      localStorage.setItem('contactSettings', JSON.stringify(contactSettings))
      localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings))
      
      // Update contact info for other components
      const contactInfo = {
        phone: contactSettings.phone,
        whatsapp: contactSettings.whatsapp,
        email: contactSettings.email,
        address: contactSettings.address,
        city: contactSettings.city,
        paymentNumbers: {
          vodafoneCash: paymentSettings.vodafoneCash.number,
          instaPay: paymentSettings.instaPay.number,
          orangeMoney: paymentSettings.orangeMoney.number,
          etisalatCash: paymentSettings.etisalatCash.number
        }
      }
      
      localStorage.setItem('contactInfo', JSON.stringify(contactInfo))
      
      // Trigger update event
      window.dispatchEvent(new CustomEvent('contactInfoUpdated', { 
        detail: contactInfo 
      }))
    }
    
    setLastSaved(new Date())
    setIsEditing(false)
    alert('تم حفظ جميع الإعدادات بنجاح! ستظهر التغييرات في الموقع فوراً.')
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert('تم نسخ النص بنجاح!')
  }

  const tabs = [
    { id: 'site', label: 'إعدادات الموقع', icon: Globe },
    { id: 'contact', label: 'معلومات الاتصال', icon: Settings },
    { id: 'payment', label: 'طرق الدفع', icon: Database },
    { id: 'security', label: 'الأمان والنسخ الاحتياطي', icon: Shield }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">الإعدادات المتقدمة</h2>
          <p className="text-gray-600">إدارة شاملة لجميع إعدادات الموقع والحسابات</p>
          {lastSaved && (
            <p className="text-sm text-green-600 mt-1">
              آخر حفظ: {lastSaved.toLocaleString('ar-EG')}
            </p>
          )}
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <button
                onClick={handleSave}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                حفظ جميع التغييرات
              </button>
              <button
                onClick={() => setIsEditing(false)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg"
              >
                إلغاء
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              تعديل الإعدادات
            </button>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm">
        {/* Site Settings Tab */}
        {activeTab === 'site' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">إعدادات الموقع العامة</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اسم الموقع</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={siteSettings.siteName}
                      onChange={(e) => setSiteSettings({...siteSettings, siteName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.siteName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">وصف الموقع</label>
                  {isEditing ? (
                    <textarea
                      value={siteSettings.siteDescription}
                      onChange={(e) => setSiteSettings({...siteSettings, siteDescription: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      rows={3}
                    />
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.siteDescription}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الكلمات المفتاحية</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={siteSettings.siteKeywords}
                      onChange={(e) => setSiteSettings({...siteSettings, siteKeywords: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.siteKeywords}</p>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">العملة</label>
                  {isEditing ? (
                    <select
                      value={siteSettings.currency}
                      onChange={(e) => setSiteSettings({...siteSettings, currency: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="EGP">جنيه مصري (EGP)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                      <option value="EUR">يورو (EUR)</option>
                    </select>
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.currency}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">المنطقة الزمنية</label>
                  {isEditing ? (
                    <select
                      value={siteSettings.timezone}
                      onChange={(e) => setSiteSettings({...siteSettings, timezone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                      <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                      <option value="Asia/Dubai">دبي (GMT+4)</option>
                    </select>
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.timezone}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="maintenanceMode"
                      checked={siteSettings.maintenanceMode}
                      onChange={(e) => setSiteSettings({...siteSettings, maintenanceMode: e.target.checked})}
                      disabled={!isEditing}
                      className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="maintenanceMode" className="mr-2 text-sm text-gray-700">
                      وضع الصيانة
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="allowRegistration"
                      checked={siteSettings.allowRegistration}
                      onChange={(e) => setSiteSettings({...siteSettings, allowRegistration: e.target.checked})}
                      disabled={!isEditing}
                      className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="allowRegistration" className="mr-2 text-sm text-gray-700">
                      السماح بالتسجيل الجديد
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Contact Settings Tab */}
        {activeTab === 'contact' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">معلومات الاتصال التفصيلية</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">أرقام الاتصال</h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف الرئيسي</label>
                  <div className="flex gap-2">
                    {isEditing ? (
                      <input
                        type="tel"
                        value={contactSettings.phone}
                        onChange={(e) => setContactSettings({...contactSettings, phone: e.target.value})}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="flex-1 text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.phone}</p>
                    )}
                    <button
                      onClick={() => copyToClipboard(contactSettings.phone)}
                      className="px-3 py-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رقم الواتساب</label>
                  <div className="flex gap-2">
                    {isEditing ? (
                      <input
                        type="tel"
                        value={contactSettings.whatsapp}
                        onChange={(e) => setContactSettings({...contactSettings, whatsapp: e.target.value})}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="flex-1 text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.whatsapp}</p>
                    )}
                    <button
                      onClick={() => copyToClipboard(contactSettings.whatsapp)}
                      className="px-3 py-2 bg-green-100 text-green-600 rounded-lg hover:bg-green-200"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">عناوين البريد الإلكتروني</h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">البريد الرئيسي</label>
                  <div className="flex gap-2">
                    {isEditing ? (
                      <input
                        type="email"
                        value={contactSettings.email}
                        onChange={(e) => setContactSettings({...contactSettings, email: e.target.value})}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="flex-1 text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.email}</p>
                    )}
                    <button
                      onClick={() => copyToClipboard(contactSettings.email)}
                      className="px-3 py-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">بريد الدعم الفني</label>
                  {isEditing ? (
                    <input
                      type="email"
                      value={contactSettings.supportEmail}
                      onChange={(e) => setContactSettings({...contactSettings, supportEmail: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.supportEmail}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">بريد المبيعات</label>
                  {isEditing ? (
                    <input
                      type="email"
                      value={contactSettings.salesEmail}
                      onChange={(e) => setContactSettings({...contactSettings, salesEmail: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.salesEmail}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm">
        {/* Site Settings Tab */}
        {activeTab === 'site' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">إعدادات الموقع العامة</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اسم الموقع</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={siteSettings.siteName}
                      onChange={(e) => setSiteSettings({...siteSettings, siteName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.siteName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">وصف الموقع</label>
                  {isEditing ? (
                    <textarea
                      value={siteSettings.siteDescription}
                      onChange={(e) => setSiteSettings({...siteSettings, siteDescription: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      rows={3}
                    />
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.siteDescription}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">العملة</label>
                  {isEditing ? (
                    <select
                      value={siteSettings.currency}
                      onChange={(e) => setSiteSettings({...siteSettings, currency: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="EGP">جنيه مصري (EGP)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                      <option value="EUR">يورو (EUR)</option>
                    </select>
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.currency}</p>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="maintenanceMode"
                      checked={siteSettings.maintenanceMode}
                      onChange={(e) => setSiteSettings({...siteSettings, maintenanceMode: e.target.checked})}
                      disabled={!isEditing}
                      className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="maintenanceMode" className="mr-2 text-sm text-gray-700">
                      وضع الصيانة
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="allowRegistration"
                      checked={siteSettings.allowRegistration}
                      onChange={(e) => setSiteSettings({...siteSettings, allowRegistration: e.target.checked})}
                      disabled={!isEditing}
                      className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="allowRegistration" className="mr-2 text-sm text-gray-700">
                      السماح بالتسجيل الجديد
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="emailNotifications"
                      checked={siteSettings.emailNotifications}
                      onChange={(e) => setSiteSettings({...siteSettings, emailNotifications: e.target.checked})}
                      disabled={!isEditing}
                      className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="emailNotifications" className="mr-2 text-sm text-gray-700">
                      إشعارات البريد الإلكتروني
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Contact Settings Tab */}
        {activeTab === 'contact' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">معلومات الاتصال التفصيلية</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">أرقام الاتصال</h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف الرئيسي</label>
                  <div className="flex gap-2">
                    {isEditing ? (
                      <input
                        type="tel"
                        value={contactSettings.phone}
                        onChange={(e) => setContactSettings({...contactSettings, phone: e.target.value})}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="flex-1 text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.phone}</p>
                    )}
                    <button
                      onClick={() => copyToClipboard(contactSettings.phone)}
                      className="px-3 py-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رقم الواتساب</label>
                  <div className="flex gap-2">
                    {isEditing ? (
                      <input
                        type="tel"
                        value={contactSettings.whatsapp}
                        onChange={(e) => setContactSettings({...contactSettings, whatsapp: e.target.value})}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="flex-1 text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.whatsapp}</p>
                    )}
                    <button
                      onClick={() => copyToClipboard(contactSettings.whatsapp)}
                      className="px-3 py-2 bg-green-100 text-green-600 rounded-lg hover:bg-green-200"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">البريد الرئيسي</label>
                  <div className="flex gap-2">
                    {isEditing ? (
                      <input
                        type="email"
                        value={contactSettings.email}
                        onChange={(e) => setContactSettings({...contactSettings, email: e.target.value})}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="flex-1 text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.email}</p>
                    )}
                    <button
                      onClick={() => copyToClipboard(contactSettings.email)}
                      className="px-3 py-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">العنوان</h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">العنوان التفصيلي</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={contactSettings.address}
                      onChange={(e) => setContactSettings({...contactSettings, address: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  ) : (
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.address}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المدينة</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={contactSettings.city}
                        onChange={(e) => setContactSettings({...contactSettings, city: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.city}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المحافظة</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={contactSettings.governorate}
                        onChange={(e) => setContactSettings({...contactSettings, governorate: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{contactSettings.governorate}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Payment Settings Tab */}
        {activeTab === 'payment' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">إدارة طرق الدفع والحسابات البنكية</h3>

            {/* Mobile Wallets */}
            <div className="mb-8">
              <h4 className="font-medium text-gray-900 border-b pb-2 mb-4">المحافظ الإلكترونية</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Vodafone Cash */}
                <div className="border border-red-200 rounded-lg p-4 bg-red-50">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-red-800">فودافون كاش</h5>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={paymentSettings.vodafoneCash.enabled}
                        onChange={(e) => setPaymentSettings({
                          ...paymentSettings,
                          vodafoneCash: {...paymentSettings.vodafoneCash, enabled: e.target.checked}
                        })}
                        disabled={!isEditing}
                        className="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
                      />
                      <span className="mr-2 text-sm text-red-700">مفعل</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-red-700 mb-1">رقم المحفظة</label>
                      <div className="flex gap-2">
                        {isEditing ? (
                          <input
                            type="tel"
                            value={paymentSettings.vodafoneCash.number}
                            onChange={(e) => setPaymentSettings({
                              ...paymentSettings,
                              vodafoneCash: {...paymentSettings.vodafoneCash, number: e.target.value}
                            })}
                            className="flex-1 px-3 py-2 border border-red-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                          />
                        ) : (
                          <p className="flex-1 text-red-900 bg-white px-3 py-2 rounded-lg border border-red-200">{paymentSettings.vodafoneCash.number}</p>
                        )}
                        <button
                          onClick={() => copyToClipboard(paymentSettings.vodafoneCash.number)}
                          className="px-3 py-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-red-700 mb-1">اسم المستفيد</label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={paymentSettings.vodafoneCash.name}
                          onChange={(e) => setPaymentSettings({
                            ...paymentSettings,
                            vodafoneCash: {...paymentSettings.vodafoneCash, name: e.target.value}
                          })}
                          className="w-full px-3 py-2 border border-red-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                        />
                      ) : (
                        <p className="text-red-900 bg-white px-3 py-2 rounded-lg border border-red-200">{paymentSettings.vodafoneCash.name}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* InstaPay */}
                <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-blue-800">إنستاباي</h5>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={paymentSettings.instaPay.enabled}
                        onChange={(e) => setPaymentSettings({
                          ...paymentSettings,
                          instaPay: {...paymentSettings.instaPay, enabled: e.target.checked}
                        })}
                        disabled={!isEditing}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className="mr-2 text-sm text-blue-700">مفعل</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">رقم المحفظة</label>
                      <div className="flex gap-2">
                        {isEditing ? (
                          <input
                            type="tel"
                            value={paymentSettings.instaPay.number}
                            onChange={(e) => setPaymentSettings({
                              ...paymentSettings,
                              instaPay: {...paymentSettings.instaPay, number: e.target.value}
                            })}
                            className="flex-1 px-3 py-2 border border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        ) : (
                          <p className="flex-1 text-blue-900 bg-white px-3 py-2 rounded-lg border border-blue-200">{paymentSettings.instaPay.number}</p>
                        )}
                        <button
                          onClick={() => copyToClipboard(paymentSettings.instaPay.number)}
                          className="px-3 py-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">اسم المستفيد</label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={paymentSettings.instaPay.name}
                          onChange={(e) => setPaymentSettings({
                            ...paymentSettings,
                            instaPay: {...paymentSettings.instaPay, name: e.target.value}
                          })}
                          className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <p className="text-blue-900 bg-white px-3 py-2 rounded-lg border border-blue-200">{paymentSettings.instaPay.name}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Bank Accounts */}
            <div className="mb-8">
              <h4 className="font-medium text-gray-900 border-b pb-2 mb-4">الحسابات البنكية</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* NBE Bank */}
                <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-green-800">البنك الأهلي المصري</h5>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={paymentSettings.bankAccounts.nbe.enabled}
                        onChange={(e) => setPaymentSettings({
                          ...paymentSettings,
                          bankAccounts: {
                            ...paymentSettings.bankAccounts,
                            nbe: {...paymentSettings.bankAccounts.nbe, enabled: e.target.checked}
                          }
                        })}
                        disabled={!isEditing}
                        className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                      />
                      <span className="mr-2 text-sm text-green-700">مفعل</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-green-700 mb-1">رقم الحساب</label>
                      <div className="flex gap-2">
                        {isEditing ? (
                          <input
                            type="text"
                            value={paymentSettings.bankAccounts.nbe.accountNumber}
                            onChange={(e) => setPaymentSettings({
                              ...paymentSettings,
                              bankAccounts: {
                                ...paymentSettings.bankAccounts,
                                nbe: {...paymentSettings.bankAccounts.nbe, accountNumber: e.target.value}
                              }
                            })}
                            className="flex-1 px-3 py-2 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 font-mono"
                          />
                        ) : (
                          <p className="flex-1 text-green-900 bg-white px-3 py-2 rounded-lg border border-green-200 font-mono">{paymentSettings.bankAccounts.nbe.accountNumber}</p>
                        )}
                        <button
                          onClick={() => copyToClipboard(paymentSettings.bankAccounts.nbe.accountNumber)}
                          className="px-3 py-2 bg-green-100 text-green-600 rounded-lg hover:bg-green-200"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-green-700 mb-1">اسم المستفيد</label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={paymentSettings.bankAccounts.nbe.accountName}
                          onChange={(e) => setPaymentSettings({
                            ...paymentSettings,
                            bankAccounts: {
                              ...paymentSettings.bankAccounts,
                              nbe: {...paymentSettings.bankAccounts.nbe, accountName: e.target.value}
                            }
                          })}
                          className="w-full px-3 py-2 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      ) : (
                        <p className="text-green-900 bg-white px-3 py-2 rounded-lg border border-green-200">{paymentSettings.bankAccounts.nbe.accountName}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-green-700 mb-1">الفرع</label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={paymentSettings.bankAccounts.nbe.branch}
                          onChange={(e) => setPaymentSettings({
                            ...paymentSettings,
                            bankAccounts: {
                              ...paymentSettings.bankAccounts,
                              nbe: {...paymentSettings.bankAccounts.nbe, branch: e.target.value}
                            }
                          })}
                          className="w-full px-3 py-2 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      ) : (
                        <p className="text-green-900 bg-white px-3 py-2 rounded-lg border border-green-200">{paymentSettings.bankAccounts.nbe.branch}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* CIB Bank */}
                <div className="border border-purple-200 rounded-lg p-4 bg-purple-50">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-purple-800">البنك التجاري الدولي</h5>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={paymentSettings.bankAccounts.cib.enabled}
                        onChange={(e) => setPaymentSettings({
                          ...paymentSettings,
                          bankAccounts: {
                            ...paymentSettings.bankAccounts,
                            cib: {...paymentSettings.bankAccounts.cib, enabled: e.target.checked}
                          }
                        })}
                        disabled={!isEditing}
                        className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                      />
                      <span className="mr-2 text-sm text-purple-700">مفعل</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-purple-700 mb-1">رقم الحساب</label>
                      <div className="flex gap-2">
                        {isEditing ? (
                          <input
                            type="text"
                            value={paymentSettings.bankAccounts.cib.accountNumber}
                            onChange={(e) => setPaymentSettings({
                              ...paymentSettings,
                              bankAccounts: {
                                ...paymentSettings.bankAccounts,
                                cib: {...paymentSettings.bankAccounts.cib, accountNumber: e.target.value}
                              }
                            })}
                            className="flex-1 px-3 py-2 border border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono"
                          />
                        ) : (
                          <p className="flex-1 text-purple-900 bg-white px-3 py-2 rounded-lg border border-purple-200 font-mono">{paymentSettings.bankAccounts.cib.accountNumber}</p>
                        )}
                        <button
                          onClick={() => copyToClipboard(paymentSettings.bankAccounts.cib.accountNumber)}
                          className="px-3 py-2 bg-purple-100 text-purple-600 rounded-lg hover:bg-purple-200"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-purple-700 mb-1">اسم المستفيد</label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={paymentSettings.bankAccounts.cib.accountName}
                          onChange={(e) => setPaymentSettings({
                            ...paymentSettings,
                            bankAccounts: {
                              ...paymentSettings.bankAccounts,
                              cib: {...paymentSettings.bankAccounts.cib, accountName: e.target.value}
                            }
                          })}
                          className="w-full px-3 py-2 border border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      ) : (
                        <p className="text-purple-900 bg-white px-3 py-2 rounded-lg border border-purple-200">{paymentSettings.bankAccounts.cib.accountName}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Cash on Delivery */}
            <div>
              <h4 className="font-medium text-gray-900 border-b pb-2 mb-4">الدفع عند الاستلام</h4>

              <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="font-medium text-gray-800">الدفع عند الاستلام</h5>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={paymentSettings.cashOnDelivery.enabled}
                      onChange={(e) => setPaymentSettings({
                        ...paymentSettings,
                        cashOnDelivery: {...paymentSettings.cashOnDelivery, enabled: e.target.checked}
                      })}
                      disabled={!isEditing}
                      className="w-4 h-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500"
                    />
                    <span className="mr-2 text-sm text-gray-700">مفعل</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رسوم إضافية (جنيه)</label>
                  {isEditing ? (
                    <input
                      type="number"
                      value={paymentSettings.cashOnDelivery.extraFee}
                      onChange={(e) => setPaymentSettings({
                        ...paymentSettings,
                        cashOnDelivery: {...paymentSettings.cashOnDelivery, extraFee: Number(e.target.value)}
                      })}
                      className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                    />
                  ) : (
                    <p className="text-gray-900 bg-white px-3 py-2 rounded-lg border border-gray-200 w-32">{paymentSettings.cashOnDelivery.extraFee} جنيه</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">الأمان والنسخ الاحتياطي</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">إعدادات الأمان</h4>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="autoBackup"
                      checked={siteSettings.autoBackup}
                      onChange={(e) => setSiteSettings({...siteSettings, autoBackup: e.target.checked})}
                      disabled={!isEditing}
                      className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="autoBackup" className="mr-2 text-sm text-gray-700">
                      النسخ الاحتياطي التلقائي
                    </label>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">تكرار النسخ الاحتياطي</label>
                    {isEditing ? (
                      <select
                        value={siteSettings.backupFrequency}
                        onChange={(e) => setSiteSettings({...siteSettings, backupFrequency: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="hourly">كل ساعة</option>
                        <option value="daily">يومياً</option>
                        <option value="weekly">أسبوعياً</option>
                        <option value="monthly">شهرياً</option>
                      </select>
                    ) : (
                      <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{siteSettings.backupFrequency}</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">إجراءات الأمان</h4>

                <div className="space-y-3">
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2">
                    <Database className="w-4 h-4" />
                    إنشاء نسخة احتياطية الآن
                  </button>

                  <button className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2">
                    <RefreshCw className="w-4 h-4" />
                    تحديث كلمات المرور
                  </button>

                  <button className="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2">
                    <Shield className="w-4 h-4" />
                    فحص الأمان
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
