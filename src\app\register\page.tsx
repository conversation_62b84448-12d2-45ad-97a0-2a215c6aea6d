'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Eye, EyeOff, Mail, Lock, User, Phone, UserPlus, Building } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { toast } from 'react-hot-toast'

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    userType: 'regular' as 'regular' | 'merchant',
    merchantCode: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const { register } = useAuth()
  const { t } = useLanguage()
  const { currentTheme } = useTheme()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Validation
    if (formData.password !== formData.confirmPassword) {
      toast.error('كلمة المرور وتأكيد كلمة المرور غير متطابقتين')
      setIsLoading(false)
      return
    }

    if (formData.password.length < 6) {
      toast.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      setIsLoading(false)
      return
    }

    if (formData.userType === 'merchant' && !formData.merchantCode) {
      toast.error('يجب إدخال كود التاجر')
      setIsLoading(false)
      return
    }

    try {
      const result = await register(formData)
      
      if (result.success) {
        toast.success(result.message)
        router.push('/')
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع')
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div 
      className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8"
      style={{ backgroundColor: currentTheme.colors.background }}
    >
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div 
            className="mx-auto h-16 w-16 rounded-full flex items-center justify-center mb-4"
            style={{ backgroundColor: currentTheme.colors.primary }}
          >
            <UserPlus className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold" style={{ color: currentTheme.colors.primary }}>
            {t('register')}
          </h2>
          <p className="mt-2 text-sm" style={{ color: currentTheme.colors.textSecondary }}>
            انضم إلى عائلة {t('companyName')}
          </p>
        </div>

        {/* Form */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Name */}
            <div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  name="name"
                  type="text"
                  required
                  value={formData.name}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-3 pl-10 border rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                  style={{
                    backgroundColor: currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  placeholder={t('name')}
                  onFocus={(e) => {
                    e.target.style.borderColor = currentTheme.colors.primary
                    e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
              </div>
            </div>

            {/* Email */}
            <div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-3 pl-10 border rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                  style={{
                    backgroundColor: currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  placeholder={t('email')}
                  onFocus={(e) => {
                    e.target.style.borderColor = currentTheme.colors.primary
                    e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
              </div>
            </div>

            {/* Phone */}
            <div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-3 pl-10 border rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                  style={{
                    backgroundColor: currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  placeholder={t('phoneNumber') + ' (' + t('optional') + ')'}
                  onFocus={(e) => {
                    e.target.style.borderColor = currentTheme.colors.primary
                    e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
              </div>
            </div>

            {/* User Type */}
            <div>
              <select
                name="userType"
                value={formData.userType}
                onChange={handleChange}
                className="appearance-none relative block w-full px-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                style={{
                  backgroundColor: currentTheme.colors.surface,
                  borderColor: currentTheme.colors.border,
                  color: currentTheme.colors.text
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = currentTheme.colors.primary
                  e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = currentTheme.colors.border
                  e.target.style.boxShadow = 'none'
                }}
              >
                <option value="regular">عميل عادي</option>
                <option value="merchant">تاجر</option>
              </select>
            </div>

            {/* Merchant Code */}
            {formData.userType === 'merchant' && (
              <div>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Building className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                  </div>
                  <input
                    name="merchantCode"
                    type="text"
                    required
                    value={formData.merchantCode}
                    onChange={handleChange}
                    className="appearance-none relative block w-full px-3 py-3 pl-10 border rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                    style={{
                      backgroundColor: currentTheme.colors.surface,
                      borderColor: currentTheme.colors.border,
                      color: currentTheme.colors.text
                    }}
                    placeholder="كود التاجر (مطلوب من صاحب المركز)"
                    onFocus={(e) => {
                      e.target.style.borderColor = currentTheme.colors.primary
                      e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = currentTheme.colors.border
                      e.target.style.boxShadow = 'none'
                    }}
                  />
                </div>
                <p className="mt-1 text-xs" style={{ color: currentTheme.colors.textSecondary }}>
                  للحصول على كود التاجر، يرجى التواصل مع إدارة المركز
                </p>
              </div>
            )}

            {/* Password */}
            <div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-3 pl-10 pr-10 border rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                  style={{
                    backgroundColor: currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  placeholder="كلمة المرور (6 أحرف على الأقل)"
                  onFocus={(e) => {
                    e.target.style.borderColor = currentTheme.colors.primary
                    e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                  ) : (
                    <Eye className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                  )}
                </button>
              </div>
            </div>

            {/* Confirm Password */}
            <div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                </div>
                <input
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-3 pl-10 pr-10 border rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:z-10 sm:text-sm"
                  style={{
                    backgroundColor: currentTheme.colors.surface,
                    borderColor: currentTheme.colors.border,
                    color: currentTheme.colors.text
                  }}
                  placeholder="تأكيد كلمة المرور"
                  onFocus={(e) => {
                    e.target.style.borderColor = currentTheme.colors.primary
                    e.target.style.boxShadow = `0 0 0 2px ${currentTheme.colors.primary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = currentTheme.colors.border
                    e.target.style.boxShadow = 'none'
                  }}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                  ) : (
                    <Eye className="h-5 w-5" style={{ color: currentTheme.colors.textSecondary }} />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Submit button */}
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              style={{ 
                backgroundColor: currentTheme.colors.primary,
                ':hover': { backgroundColor: currentTheme.colors.secondary }
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.secondary
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = currentTheme.colors.primary
                }
              }}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  جاري إنشاء الحساب...
                </div>
              ) : (
                <div className="flex items-center">
                  <UserPlus className="h-4 w-4 mr-2" />
                  {t('register')}
                </div>
              )}
            </button>
          </div>

          {/* Login link */}
          <div className="text-center">
            <p className="text-sm" style={{ color: currentTheme.colors.textSecondary }}>
              لديك حساب بالفعل؟{' '}
              <Link
                href="/login"
                className="font-medium hover:underline"
                style={{ color: currentTheme.colors.primary }}
              >
                {t('login')}
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  )
}
