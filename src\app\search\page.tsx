'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { Search, Filter, Package, Tag, FileText, Phone, Clock, TrendingUp } from 'lucide-react'
import { db } from '@/lib/database'
import PriceDisplay from '@/components/products/PriceDisplay'
import Link from 'next/link'

interface SearchResult {
  id: string
  title: string
  description: string
  type: 'product' | 'category' | 'service' | 'page'
  url: string
  price?: number
  wholesalePrice?: number
  image?: string
  category?: string
  relevance: number
}

export default function SearchPage() {
  const searchParams = useSearchParams()
  const query = searchParams.get('q') || ''
  
  const [results, setResults] = useState<SearchResult[]>([])
  const [filteredResults, setFilteredResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedType, setSelectedType] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('relevance')

  const resultTypes = [
    { id: 'all', label: 'جميع النتائج', icon: Search },
    { id: 'product', label: 'المنتجات', icon: Package },
    { id: 'category', label: 'التصنيفات', icon: Tag },
    { id: 'service', label: 'الخدمات', icon: Phone },
    { id: 'page', label: 'الصفحات', icon: FileText }
  ]

  useEffect(() => {
    if (query.trim()) {
      performSearch(query)
    }
  }, [query])

  useEffect(() => {
    let filtered = results

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(result => result.type === selectedType)
    }

    // Sort results
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return (a.price || 0) - (b.price || 0)
        case 'price-high':
          return (b.price || 0) - (a.price || 0)
        case 'name':
          return a.title.localeCompare(b.title, 'ar')
        case 'relevance':
        default:
          return b.relevance - a.relevance
      }
    })

    setFilteredResults(filtered)
  }, [results, selectedType, sortBy])

  const performSearch = async (searchQuery: string) => {
    setIsLoading(true)
    
    try {
      const searchResults: SearchResult[] = []
      const lowerQuery = searchQuery.toLowerCase()

      // Search Products
      const products = db.getAllProducts()
      products.forEach(product => {
        let relevance = 0
        
        if (product.name.toLowerCase().includes(lowerQuery)) {
          relevance += 10
        }
        
        if (product.description.toLowerCase().includes(lowerQuery)) {
          relevance += 5
        }
        
        if (product.brand && product.brand.toLowerCase().includes(lowerQuery)) {
          relevance += 7
        }
        
        if (product.features.some(feature => feature.toLowerCase().includes(lowerQuery))) {
          relevance += 3
        }

        if (relevance > 0) {
          searchResults.push({
            id: product.id,
            title: product.name,
            description: product.description,
            type: 'product',
            url: `/products/${product.id}`,
            price: product.price,
            wholesalePrice: product.wholesalePrice,
            image: product.image,
            category: product.categoryId,
            relevance
          })
        }
      })

      // Search Categories
      const categories = db.getAllCategories()
      categories.forEach(category => {
        let relevance = 0
        
        if (category.name.toLowerCase().includes(lowerQuery)) {
          relevance += 8
        }
        
        if (category.description.toLowerCase().includes(lowerQuery)) {
          relevance += 4
        }

        if (relevance > 0) {
          searchResults.push({
            id: category.id,
            title: category.name,
            description: category.description,
            type: 'category',
            url: `/products?category=${category.id}`,
            relevance
          })
        }
      })

      // Search Services
      const services = db.getAllServices()
      services.forEach(service => {
        let relevance = 0
        
        if (service.name.toLowerCase().includes(lowerQuery)) {
          relevance += 8
        }
        
        if (service.description.toLowerCase().includes(lowerQuery)) {
          relevance += 4
        }

        if (relevance > 0) {
          searchResults.push({
            id: service.id,
            title: service.name,
            description: service.description,
            type: 'service',
            url: `/services/${service.id}`,
            price: service.price,
            wholesalePrice: service.wholesalePrice,
            relevance
          })
        }
      })

      // Search Static Pages
      const staticPages = [
        {
          id: 'about',
          title: 'من نحن',
          description: 'تعرف على مركز البدوي وتاريخنا العريق',
          keywords: ['من نحن', 'تاريخ', 'مركز البدوي', 'أصالة', 'ثقة'],
          url: '/about'
        },
        {
          id: 'contact',
          title: 'اتصل بنا',
          description: 'طرق التواصل مع مركز البدوي',
          keywords: ['اتصل بنا', 'تواصل', 'هاتف', 'عنوان', 'خدمة عملاء'],
          url: '/contact'
        },
        {
          id: 'services',
          title: 'خدماتنا',
          description: 'جميع الخدمات المتاحة في مركز البدوي',
          keywords: ['خدمات', 'توصيل', 'صيانة', 'دعم فني'],
          url: '/services'
        }
      ]

      staticPages.forEach(page => {
        let relevance = 0
        
        if (page.title.toLowerCase().includes(lowerQuery)) {
          relevance += 8
        }
        
        if (page.description.toLowerCase().includes(lowerQuery)) {
          relevance += 4
        }
        
        if (page.keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery))) {
          relevance += 6
        }

        if (relevance > 0) {
          searchResults.push({
            id: page.id,
            title: page.title,
            description: page.description,
            type: 'page',
            url: page.url,
            relevance
          })
        }
      })

      // Sort by relevance
      searchResults.sort((a, b) => b.relevance - a.relevance)
      
      setResults(searchResults)
      
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }

  const getTypeIcon = (type: string) => {
    const typeConfig = resultTypes.find(t => t.id === type)
    if (typeConfig) {
      const Icon = typeConfig.icon
      return <Icon className="w-4 h-4" />
    }
    return <Search className="w-4 h-4" />
  }

  const getTypeLabel = (type: string) => {
    const typeConfig = resultTypes.find(t => t.id === type)
    return typeConfig?.label || type
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'product': return 'text-blue-600 bg-blue-100'
      case 'category': return 'text-green-600 bg-green-100'
      case 'service': return 'text-purple-600 bg-purple-100'
      case 'page': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getResultCounts = () => {
    const counts: Record<string, number> = { all: results.length }
    results.forEach(result => {
      counts[result.type] = (counts[result.type] || 0) + 1
    })
    return counts
  }

  const resultCounts = getResultCounts()

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">نتائج البحث</h1>
        {query && (
          <p className="text-gray-600">
            نتائج البحث عن: <span className="font-semibold">"{query}"</span>
          </p>
        )}
      </div>

      {/* Filters and Controls */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
          {/* Type Filters */}
          <div className="flex flex-wrap gap-2">
            {resultTypes.map(type => {
              const Icon = type.icon
              const count = resultCounts[type.id] || 0
              return (
                <button
                  key={type.id}
                  onClick={() => setSelectedType(type.id)}
                  disabled={count === 0 && type.id !== 'all'}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedType === type.id
                      ? 'bg-primary-600 text-white'
                      : count > 0 || type.id === 'all'
                      ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {type.label}
                  {count > 0 && (
                    <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                      selectedType === type.id ? 'bg-white text-primary-600' : 'bg-gray-200 text-gray-600'
                    }`}>
                      {count}
                    </span>
                  )}
                </button>
              )
            })}
          </div>

          {/* Sort Options */}
          <div className="flex items-center gap-2 lg:mr-auto">
            <span className="text-sm text-gray-600">ترتيب حسب:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
            >
              <option value="relevance">الصلة</option>
              <option value="name">الاسم</option>
              <option value="price-low">السعر: من الأقل للأعلى</option>
              <option value="price-high">السعر: من الأعلى للأقل</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results */}
      {isLoading ? (
        <div className="text-center py-12">
          <div className="w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">جاري البحث...</p>
        </div>
      ) : filteredResults.length > 0 ? (
        <div className="space-y-4">
          <p className="text-gray-600 mb-4">
            عرض {filteredResults.length} من أصل {results.length} نتيجة
          </p>
          
          {filteredResults.map((result) => (
            <div key={`${result.type}-${result.id}`} className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start gap-4">
                {/* Type Icon */}
                <div className={`p-3 rounded-lg ${getTypeColor(result.type)}`}>
                  {getTypeIcon(result.type)}
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Link 
                      href={result.url}
                      className="text-xl font-semibold text-gray-900 hover:text-primary-600 transition-colors"
                    >
                      {result.title}
                    </Link>
                    <span className={`text-xs px-2 py-1 rounded-full ${getTypeColor(result.type)}`}>
                      {getTypeLabel(result.type)}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-3 line-clamp-2">{result.description}</p>
                  
                  {/* Price for products/services */}
                  {(result.type === 'product' || result.type === 'service') && result.price !== undefined && (
                    <div className="mb-3">
                      <PriceDisplay
                        retailPrice={result.price}
                        wholesalePrice={result.wholesalePrice || result.price}
                        currency="جنيه"
                      />
                    </div>
                  )}
                  
                  <Link 
                    href={result.url}
                    className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                  >
                    عرض التفاصيل ←
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : query ? (
        <div className="text-center py-12">
          <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">لم يتم العثور على نتائج</h3>
          <p className="text-gray-600 mb-4">لم نجد أي نتائج مطابقة لـ "{query}"</p>
          <div className="space-y-2 text-sm text-gray-500">
            <p>جرب:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>التحقق من الإملاء</li>
              <li>استخدام كلمات أخرى</li>
              <li>البحث بكلمات أقل</li>
              <li>استخدام مصطلحات أعم</li>
            </ul>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">ابدأ البحث</h3>
          <p className="text-gray-600">استخدم شريط البحث أعلاه للعثور على المنتجات والخدمات</p>
        </div>
      )}
    </div>
  )
}
