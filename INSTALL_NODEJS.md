# تثبيت Node.js وتشغيل متجر كوبرا

## 🚀 خطوات التثبيت والتشغيل

### 1️⃣ تثبيت Node.js

#### الطريقة الأولى: التحميل المباشر (الأسهل)
1. اذهب إلى الموقع الرسمي: https://nodejs.org/
2. حمل النسخة **LTS** (الموصى بها)
3. شغل ملف التثبيت واتبع التعليمات
4. أعد تشغيل الكمبيوتر بعد التثبيت

#### الطريقة الثانية: باستخدام Chocolatey
```powershell
# تثبيت Chocolatey أولاً (إذا لم يكن مثبت)
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# تثبيت Node.js
choco install nodejs
```

#### الطريقة الثالثة: باستخدام Winget
```powershell
winget install OpenJS.NodeJS
```

### 2️⃣ التحقق من التثبيت
```bash
# افتح Command Prompt أو PowerShell جديد واكتب:
node --version
npm --version

# يجب أن تظهر أرقام الإصدارات
# مثال:
# v18.17.0
# 9.6.7
```

### 3️⃣ تثبيت مكتبات المشروع
```bash
# انتقل لمجلد المشروع
cd "C:\Users\<USER>\Documents\augment-projects\Cobra"

# ثبت المكتبات
npm install
```

### 4️⃣ تشغيل المشروع
```bash
# تشغيل في وضع التطوير
npm run dev
```

### 5️⃣ فتح الموقع
افتح المتصفح واذهب إلى: **http://localhost:3000**

---

## 🔧 حل المشاكل الشائعة

### مشكلة: "npm is not recognized"
**الحل:**
1. أعد تشغيل Command Prompt/PowerShell
2. أعد تشغيل الكمبيوتر
3. تأكد من تثبيت Node.js بشكل صحيح

### مشكلة: "execution policy"
**الحل:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### مشكلة: بطء التحميل
**الحل:**
```bash
# استخدم مرآة صينية أسرع
npm config set registry https://registry.npmmirror.com/

# أو استخدم yarn بدلاً من npm
npm install -g yarn
yarn install
yarn dev
```

### مشكلة: خطأ في الذاكرة
**الحل:**
```bash
# زيادة حد الذاكرة
set NODE_OPTIONS=--max_old_space_size=4096
npm run dev
```

---

## 📱 بعد التشغيل الناجح

### ستجد الموقع يعمل على:
- **الرابط المحلي**: http://localhost:3000
- **الرابط الشبكي**: http://[your-ip]:3000

### الصفحات المتاحة:
- `/` - الصفحة الرئيسية
- `/products` - المنتجات
- `/services` - الخدمات
- `/cart` - سلة التسوق
- `/about` - من نحن
- `/contact` - اتصل بنا
- `/orders` - الطلبات (بعد تسجيل الدخول)
- `/profile` - الملف الشخصي (بعد تسجيل الدخول)

### اختبار الميزات:
1. **تسجيل الدخول**: اضغط على "تسجيل الدخول" في الأعلى
2. **إضافة منتجات**: اذهب للمنتجات واضغط "أضف للسلة"
3. **تحديد الموقع**: سيطلب منك تحديد موقعك عند أول زيارة
4. **طلب خدمة**: اذهب للخدمات واضغط "اطلب الخدمة"

---

## 🎯 الأوامر المفيدة

```bash
# تشغيل في وضع التطوير
npm run dev

# بناء للإنتاج
npm run build

# تشغيل الإنتاج
npm start

# فحص الأخطاء
npm run lint

# فحص TypeScript
npm run type-check
```

---

## 🆘 إذا واجهت مشاكل

### تواصل معنا:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 50 123 4567

### أو راجع الملفات:
- `README.md` - دليل شامل
- `QUICK_START.md` - بدء سريع
- `DEVELOPER_GUIDE.md` - دليل المطور

---

## ✅ قائمة التحقق

- [ ] تثبيت Node.js
- [ ] التحقق من `node --version`
- [ ] التحقق من `npm --version`
- [ ] تشغيل `npm install`
- [ ] تشغيل `npm run dev`
- [ ] فتح http://localhost:3000
- [ ] اختبار الصفحات والميزات

---

**بعد اتباع هذه الخطوات، ستحصل على موقع متجر كوبرا يعمل بكامل ميزاته! 🎉**
