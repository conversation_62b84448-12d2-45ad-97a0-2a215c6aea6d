'use client'

import { useState, useRef, useEffect } from 'react'
import { MessageCircle, X, Send, Minimize2, Maximize2, Bo<PERSON>, User } from 'lucide-react'

interface Message {
  id: string
  text: string
  sender: 'user' | 'agent' | 'bot'
  timestamp: Date
  senderName?: string
  typing?: boolean
}

interface LiveChatProps {
  isOpen: boolean
  onToggle: () => void
  className?: string
}

export default function LiveChat({ isOpen, onToggle, className = '' }: LiveChatProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'مرحباً بك في مركز البدوي! اسم له تاريخ وأصالة. كيف يمكنني مساعدتك اليوم؟',
      sender: 'bot',
      timestamp: new Date(),
      senderName: 'مساعد البدوي'
    }
  ])
  const [inputText, setInputText] = useState('')
  const [isMinimized, setIsMinimized] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [isConnected, setIsConnected] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen && !isMinimized) {
      inputRef.current?.focus()
    }
  }, [isOpen, isMinimized])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const sendMessage = () => {
    if (!inputText.trim()) return

    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, newMessage])
    setInputText('')
    setIsTyping(true)

    // Simulate bot response
    setTimeout(() => {
      const botResponse = getBotResponse(inputText.trim())
      setMessages(prev => [...prev, {
        id: (Date.now() + 1).toString(),
        text: botResponse,
        sender: 'bot',
        timestamp: new Date(),
        senderName: 'مساعد البدوي'
      }])
      setIsTyping(false)
    }, 1000 + Math.random() * 2000)
  }

  const getBotResponse = (userMessage: string): string => {
    const message = userMessage.toLowerCase()
    
    if (message.includes('سعر') || message.includes('كم') || message.includes('تكلفة')) {
      return 'يمكنك العثور على أسعار جميع منتجاتنا في صفحة المنتجات. هل تبحث عن منتج معين؟'
    }
    
    if (message.includes('توصيل') || message.includes('شحن')) {
      return 'نوفر توصيل مجاني للطلبات أكثر من 200 ريال. التوصيل عادة خلال 24-48 ساعة داخل المدن الرئيسية.'
    }
    
    if (message.includes('ضمان') || message.includes('كفالة')) {
      return 'جميع منتجاتنا تأتي مع ضمان الوكيل. مدة الضمان تختلف حسب نوع المنتج.'
    }
    
    if (message.includes('دفع') || message.includes('طريقة')) {
      return 'نقبل جميع طرق الدفع: فيزا، ماستركارد، مدى، أبل باي، والدفع عند الاستلام.'
    }
    
    if (message.includes('مرحبا') || message.includes('السلام')) {
      return 'أهلاً وسهلاً بك! كيف يمكنني مساعدتك اليوم؟'
    }
    
    if (message.includes('شكرا') || message.includes('شكراً')) {
      return 'العفو! سعداء بخدمتك. هل تحتاج لأي مساعدة أخرى؟'
    }

    return 'شكراً لك على تواصلك معنا. سيقوم أحد ممثلي خدمة العملاء بالرد عليك قريباً. هل يمكنني مساعدتك في شيء آخر؟'
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const quickReplies = [
    'أسعار المنتجات',
    'طرق الدفع',
    'التوصيل والشحن',
    'الضمان والكفالة',
    'تتبع الطلب'
  ]

  if (!isOpen) {
    return (
      <button
        onClick={onToggle}
        className={`
          fixed bottom-6 left-6 z-50
          w-14 h-14 bg-primary-600 hover:bg-primary-700
          text-white rounded-full shadow-lg
          flex items-center justify-center
          transition-all duration-300 hover:scale-110
          ${className}
        `}
      >
        <MessageCircle className="w-6 h-6" />
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
      </button>
    )
  }

  return (
    <div className={`
      fixed bottom-6 left-6 z-50
      w-80 bg-white rounded-lg shadow-2xl border border-gray-200
      flex flex-col overflow-hidden
      transition-all duration-300
      ${isMinimized ? 'h-14' : 'h-96'}
      ${className}
    `}>
      {/* Header */}
      <div className="bg-primary-600 text-white p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <MessageCircle className="w-4 h-4" />
          </div>
          <div>
            <h3 className="font-medium text-sm">دردشة مباشرة</h3>
            <div className="flex items-center gap-1 text-xs opacity-90">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
              {isConnected ? 'متصل' : 'غير متصل'}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="text-white hover:bg-white hover:bg-opacity-20 p-1 rounded transition-colors"
          >
            {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
          </button>
          <button
            onClick={onToggle}
            className="text-white hover:bg-white hover:bg-opacity-20 p-1 rounded transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`
                  max-w-xs px-3 py-2 rounded-lg text-sm
                  ${message.sender === 'user'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                  }
                `}>
                  {message.sender !== 'user' && (
                    <div className="flex items-center gap-2 mb-1">
                      {message.sender === 'bot' ? (
                        <Bot className="w-3 h-3" />
                      ) : (
                        <User className="w-3 h-3" />
                      )}
                      <span className="text-xs font-medium">{message.senderName}</span>
                    </div>
                  )}
                  <p>{message.text}</p>
                  <div className={`text-xs mt-1 ${
                    message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {formatTime(message.timestamp)}
                  </div>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-900 max-w-xs px-3 py-2 rounded-lg text-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <Bot className="w-3 h-3" />
                    <span className="text-xs font-medium">مساعد كوبرا</span>
                  </div>
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Replies */}
          {messages.length === 1 && (
            <div className="px-4 pb-2">
              <div className="text-xs text-gray-500 mb-2">اختر موضوع:</div>
              <div className="flex flex-wrap gap-1">
                {quickReplies.map((reply, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setInputText(reply)
                      setTimeout(sendMessage, 100)
                    }}
                    className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded transition-colors"
                  >
                    {reply}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Input */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex gap-2">
              <input
                ref={inputRef}
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="اكتب رسالتك..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                disabled={!isConnected}
              />
              <button
                onClick={sendMessage}
                disabled={!inputText.trim() || !isConnected}
                className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
