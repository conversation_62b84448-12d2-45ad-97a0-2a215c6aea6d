'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  Search, 
  Filter, 
  Clock, 
  Star, 
  CheckCircle,
  Wrench, 
  Truck, 
  Home, 
  Smartphone, 
  Car, 
  Paintbrush,
  Calendar,
  MapPin
} from 'lucide-react'
import toast from 'react-hot-toast'

interface Service {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  price: string
  duration: string
  rating: number
  reviews: number
  features: string[]
  popular: boolean
  category: string
  availability: string
}

const services: Service[] = [
  {
    id: '1',
    name: 'صيانة الأجهزة الإلكترونية',
    description: 'خدمة صيانة شاملة للهواتف والحاسوب واللابتوب مع ضمان الجودة',
    icon: <Smartphone className="w-8 h-8" />,
    price: 'يبدأ من 250 جنيه',
    duration: '2-4 ساعات',
    rating: 4.9,
    reviews: 234,
    features: ['تشخيص مجاني', '<PERSON><PERSON><PERSON> 6 أشهر', 'قطع غيار أصلية', 'فنيين معتمدين'],
    popular: true,
    category: 'إلكترونيات',
    availability: 'متاح يومياً'
  },
  {
    id: '2',
    name: 'توصيل وتركيب',
    description: 'خدمة توصيل وتركيب الأجهزة والأثاث مع فريق متخصص',
    icon: <Truck className="w-8 h-8" />,
    price: 'يبدأ من 120 جنيه',
    duration: '1-3 ساعات',
    rating: 4.8,
    reviews: 189,
    features: ['توصيل سريع', 'تركيب احترافي', 'ضمان التركيب', 'تنظيف الموقع'],
    popular: false,
    category: 'توصيل',
    availability: 'متاح يومياً'
  },
  {
    id: '3',
    name: 'خدمات منزلية',
    description: 'تنظيف، صيانة، وإصلاحات منزلية متنوعة بأيدي خبيرة',
    icon: <Home className="w-8 h-8" />,
    price: 'يبدأ من 200 جنيه',
    duration: '2-6 ساعات',
    rating: 4.7,
    reviews: 156,
    features: ['فريق محترف', 'أدوات متطورة', 'خدمة شاملة', 'تأمين شامل'],
    popular: true,
    category: 'منزلية',
    availability: 'متاح يومياً'
  },
  {
    id: '4',
    name: 'صيانة السيارات',
    description: 'خدمة صيانة وإصلاح السيارات في الموقع أو في المركز',
    icon: <Car className="w-8 h-8" />,
    price: 'يبدأ من 150 ريال',
    duration: '1-4 ساعات',
    rating: 4.6,
    reviews: 98,
    features: ['خدمة في الموقع', 'فنيين معتمدين', 'قطع غيار أصلية', 'ضمان الخدمة'],
    popular: false,
    category: 'سيارات',
    availability: 'السبت - الخميس'
  },
  {
    id: '5',
    name: 'أعمال الدهان والديكور',
    description: 'دهان الجدران وأعمال الديكور الداخلي والخارجي',
    icon: <Paintbrush className="w-8 h-8" />,
    price: 'يبدأ من 200 ريال',
    duration: '4-8 ساعات',
    rating: 4.8,
    reviews: 76,
    features: ['ألوان عالية الجودة', 'تصاميم عصرية', 'ضمان سنتين', 'استشارة مجانية'],
    popular: false,
    category: 'ديكور',
    availability: 'حسب الطلب'
  },
  {
    id: '6',
    name: 'صيانة عامة',
    description: 'خدمات صيانة متنوعة للمنزل والمكتب والمحلات التجارية',
    icon: <Wrench className="w-8 h-8" />,
    price: 'يبدأ من 75 ريال',
    duration: '1-3 ساعات',
    rating: 4.5,
    reviews: 134,
    features: ['استجابة سريعة', 'أسعار منافسة', 'خدمة موثوقة', 'متاح 24/7'],
    popular: false,
    category: 'عامة',
    availability: 'متاح دائماً'
  }
]

const categories = ['الكل', 'إلكترونيات', 'توصيل', 'منزلية', 'سيارات', 'ديكور', 'عامة']

export default function ServicesPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('الكل')
  const [sortBy, setSortBy] = useState('popular')
  const [filteredServices, setFilteredServices] = useState(services)

  // Filter and sort services
  useEffect(() => {
    let filtered = services

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'الكل') {
      filtered = filtered.filter(service => service.category === selectedCategory)
    }

    // Sort services
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating
        case 'reviews':
          return b.reviews - a.reviews
        case 'name':
          return a.name.localeCompare(b.name, 'ar')
        case 'popular':
        default:
          return b.popular ? 1 : -1
      }
    })

    setFilteredServices(filtered)
  }, [searchQuery, selectedCategory, sortBy])

  const handleRequestService = (serviceId: string) => {
    // In a real app, this would navigate to a service request form
    toast.success('سيتم توجيهك لصفحة طلب الخدمة')
    // For now, just show a success message
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">خدماتنا</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          نقدم مجموعة شاملة من الخدمات المهنية لتلبية جميع احتياجاتكم
        </p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="ابحث عن الخدمات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="popular">الأكثر طلباً</option>
            <option value="rating">التقييم</option>
            <option value="reviews">عدد التقييمات</option>
            <option value="name">الاسم</option>
          </select>
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">
          عرض {filteredServices.length} من أصل {services.length} خدمة
        </p>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {filteredServices.map((service) => (
          <div
            key={service.id}
            className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-all duration-300 relative"
          >
            {/* Popular Badge */}
            {service.popular && (
              <div className="absolute -top-2 -right-2 bg-yellow-400 text-gray-800 px-3 py-1 rounded-full text-sm font-bold">
                الأكثر طلباً
              </div>
            )}

            {/* Service Icon */}
            <div className="flex items-center justify-center w-16 h-16 bg-primary-100 text-primary-600 rounded-lg mb-4">
              {service.icon}
            </div>

            {/* Service Info */}
            <h3 className="text-xl font-semibold text-gray-800 mb-2">{service.name}</h3>
            <p className="text-gray-600 mb-4 line-clamp-2">{service.description}</p>

            {/* Rating */}
            <div className="flex items-center gap-2 mb-3">
              <div className="flex">{renderStars(service.rating)}</div>
              <span className="text-sm text-gray-600">({service.reviews} تقييم)</span>
            </div>

            {/* Price and Duration */}
            <div className="flex justify-between items-center mb-4">
              <span className="text-lg font-bold text-primary-600">{service.price}</span>
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <Clock size={14} />
                <span>{service.duration}</span>
              </div>
            </div>

            {/* Availability */}
            <div className="flex items-center gap-1 text-sm text-gray-500 mb-4">
              <Calendar size={14} />
              <span>{service.availability}</span>
            </div>

            {/* Features */}
            <div className="mb-4">
              <h4 className="font-semibold text-gray-800 mb-2 text-sm">مميزات الخدمة:</h4>
              <ul className="space-y-1">
                {service.features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                    <CheckCircle size={12} className="text-green-500 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <button
                onClick={() => handleRequestService(service.id)}
                className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-lg transition-colors duration-200"
              >
                اطلب الخدمة
              </button>
              <Link
                href={`/services/${service.id}`}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors duration-200"
              >
                التفاصيل
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredServices.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">لم يتم العثور على خدمات مطابقة للبحث</p>
        </div>
      )}

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white rounded-lg p-8 text-center">
        <h3 className="text-2xl font-bold mb-4">هل تحتاج خدمة مخصصة؟</h3>
        <p className="text-blue-100 mb-6">
          تواصل معنا للحصول على استشارة مجانية وعرض سعر مخصص لاحتياجاتك
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/contact"
            className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-bold py-3 px-8 rounded-lg transition-colors duration-200"
          >
            تواصل معنا
          </Link>
          <Link
            href="/services/custom"
            className="bg-transparent border-2 border-white hover:bg-white hover:text-primary-600 font-bold py-3 px-8 rounded-lg transition-colors duration-200"
          >
            طلب خدمة مخصصة
          </Link>
        </div>
      </div>
    </div>
  )
}
