'use client'

import { useState } from 'react'
import { Phone, Mail, MapPin, Send, MessageCircle } from 'lucide-react'
import toast from 'react-hot-toast'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً')
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      })
    } catch (error) {
      toast.error('حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
      {/* Contact Info */}
      <div>
        <h2 className="text-3xl font-bold text-gray-800 mb-6">تواصل معنا</h2>
        <p className="text-gray-600 mb-8 leading-relaxed">
          نحن هنا لمساعدتك! تواصل معنا عبر أي من الطرق التالية أو املأ النموذج وسنتواصل معك في أقرب وقت ممكن.
        </p>

        {/* Contact Methods */}
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg">
              <Phone size={20} />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">الهاتف</h3>
              <p className="text-gray-600">+966 50 123 4567</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg">
              <Mail size={20} />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">البريد الإلكتروني</h3>
              <p className="text-gray-600"><EMAIL></p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg">
              <MapPin size={20} />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">العنوان</h3>
              <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg">
              <MessageCircle size={20} />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">الدردشة المباشرة</h3>
              <p className="text-gray-600">متاح 24/7 لخدمتكم</p>
            </div>
          </div>
        </div>

        {/* Working Hours */}
        <div className="mt-8 p-6 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-4">ساعات العمل</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">السبت - الخميس</span>
              <span className="text-gray-800">9:00 ص - 10:00 م</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">الجمعة</span>
              <span className="text-gray-800">2:00 م - 10:00 م</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">خدمة العملاء</span>
              <span className="text-gray-800">24/7</span>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Form */}
      <div className="bg-white rounded-lg shadow-md p-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-6">أرسل لنا رسالة</h3>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                الاسم الكامل *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="input-field"
                placeholder="أدخل اسمك الكامل"
              />
            </div>
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                رقم الهاتف *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
                className="input-field"
                placeholder="05xxxxxxxx"
              />
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              البريد الإلكتروني *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="input-field"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
              الموضوع *
            </label>
            <select
              id="subject"
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              required
              className="input-field"
            >
              <option value="">اختر الموضوع</option>
              <option value="product">استفسار عن منتج</option>
              <option value="service">طلب خدمة</option>
              <option value="complaint">شكوى</option>
              <option value="suggestion">اقتراح</option>
              <option value="other">أخرى</option>
            </select>
          </div>

          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
              الرسالة *
            </label>
            <textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              required
              rows={5}
              className="input-field resize-none"
              placeholder="اكتب رسالتك هنا..."
            />
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الإرسال...
              </>
            ) : (
              <>
                <Send size={18} />
                إرسال الرسالة
              </>
            )}
          </button>
        </form>
      </div>
    </div>
  )
}
