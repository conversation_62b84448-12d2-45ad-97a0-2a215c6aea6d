<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر كوبرا - معاينة الموقع</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        /* Tailwind CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', system-ui, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Utility Classes */
        .bg-primary { background-color: #2563eb; }
        .bg-primary-50 { background-color: #eff6ff; }
        .bg-primary-100 { background-color: #dbeafe; }
        .bg-primary-600 { background-color: #2563eb; }
        .bg-primary-700 { background-color: #1d4ed8; }
        .text-primary-600 { color: #2563eb; }
        .text-white { color: white; }
        .text-gray-50 { color: #f9fafb; }
        .text-gray-300 { color: #d1d5db; }
        .text-gray-600 { color: #4b5563; }
        .text-gray-700 { color: #374151; }
        .text-gray-800 { color: #1f2937; }
        .text-gray-900 { color: #111827; }
        .text-yellow-300 { color: #fcd34d; }
        .text-yellow-400 { color: #fbbf24; }
        .text-blue-100 { color: #dbeafe; }
        .text-red-500 { color: #ef4444; }
        .text-green-500 { color: #10b981; }

        .bg-white { background-color: white; }
        .bg-gray-50 { background-color: #f9fafb; }
        .bg-gray-200 { background-color: #e5e7eb; }
        .bg-gray-800 { background-color: #1f2937; }
        .bg-gray-900 { background-color: #111827; }
        .bg-yellow-400 { background-color: #fbbf24; }
        .bg-yellow-500 { background-color: #f59e0b; }
        .bg-red-500 { background-color: #ef4444; }
        .bg-green-500 { background-color: #10b981; }

        /* Layout */
        .flex { display: flex; }
        .grid { display: grid; }
        .hidden { display: none; }
        .block { display: block; }
        .inline-block { display: inline-block; }

        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }
        .text-center { text-align: center; }

        .grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
        .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

        .gap-2 { gap: 0.5rem; }
        .gap-3 { gap: 0.75rem; }
        .gap-4 { gap: 1rem; }
        .gap-6 { gap: 1.5rem; }
        .gap-8 { gap: 2rem; }
        .gap-12 { gap: 3rem; }

        /* Spacing */
        .p-2 { padding: 0.5rem; }
        .p-3 { padding: 0.75rem; }
        .p-4 { padding: 1rem; }
        .p-6 { padding: 1.5rem; }
        .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
        .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
        .px-4 { padding-left: 1rem; padding-right: 1rem; }
        .px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
        .px-8 { padding-left: 2rem; padding-right: 2rem; }
        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
        .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
        .py-4 { padding-top: 1rem; padding-bottom: 1rem; }
        .py-12 { padding-top: 3rem; padding-bottom: 3rem; }
        .py-16 { padding-top: 4rem; padding-bottom: 4rem; }
        .py-20 { padding-top: 5rem; padding-bottom: 5rem; }

        .m-2 { margin: 0.5rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-3 { margin-bottom: 0.75rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mb-8 { margin-bottom: 2rem; }
        .mb-12 { margin-bottom: 3rem; }
        .mr-2 { margin-right: 0.5rem; }
        .mx-auto { margin-left: auto; margin-right: auto; }

        /* Typography */
        .text-sm { font-size: 0.875rem; }
        .text-lg { font-size: 1.125rem; }
        .text-xl { font-size: 1.25rem; }
        .text-2xl { font-size: 1.5rem; }
        .text-3xl { font-size: 1.875rem; }
        .text-4xl { font-size: 2.25rem; }
        .text-6xl { font-size: 3.75rem; }

        .font-medium { font-weight: 500; }
        .font-semibold { font-weight: 600; }
        .font-bold { font-weight: 700; }

        .leading-relaxed { line-height: 1.625; }
        .line-through { text-decoration: line-through; }

        /* Borders and Radius */
        .border { border: 1px solid #d1d5db; }
        .border-t { border-top: 1px solid #d1d5db; }
        .border-gray-800 { border-color: #1f2937; }
        .rounded-lg { border-radius: 0.5rem; }
        .rounded-full { border-radius: 9999px; }
        .overflow-hidden { overflow: hidden; }

        /* Shadows */
        .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }

        /* Sizing */
        .w-5 { width: 1.25rem; }
        .w-16 { width: 4rem; }
        .w-full { width: 100%; }
        .h-5 { height: 1.25rem; }
        .h-16 { height: 4rem; }
        .h-48 { height: 12rem; }
        .max-w-3xl { max-width: 48rem; }

        /* Positioning */
        .relative { position: relative; }
        .absolute { position: absolute; }
        .fixed { position: fixed; }
        .top-2 { top: 0.5rem; }
        .top-4 { top: 1rem; }
        .right-2 { right: 0.5rem; }
        .right-4 { right: 1rem; }
        .-top-2 { top: -0.5rem; }
        .-right-2 { right: -0.5rem; }

        /* Transforms */
        .translate-x-full { transform: translateX(100%); }

        /* Transitions */
        .transition-transform { transition: transform 0.3s; }
        .duration-300 { transition-duration: 0.3s; }

        /* Hover Effects */
        button:hover, .hover\:bg-primary-700:hover { background-color: #1d4ed8; }
        .hover\:bg-yellow-500:hover { background-color: #f59e0b; }
        .hover\:text-primary-600:hover { color: #2563eb; }

        /* Responsive */
        @media (min-width: 768px) {
            .md\:flex { display: flex; }
            .md\:hidden { display: none; }
            .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
            .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        }

        @media (min-width: 1024px) {
            .lg\:text-6xl { font-size: 3.75rem; }
            .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
            .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        }

        @media (min-width: 640px) {
            .sm\:flex-row { flex-direction: row; }
            .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        }

        .flex-col { flex-direction: column; }

        /* Custom Styles */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        button {
            cursor: pointer;
            transition: all 0.2s;
        }

        input, textarea {
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            padding: 0.75rem;
            width: 100%;
            font-family: 'Cairo', sans-serif;
        }

        input:focus, textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .space-x-8 > * + * { margin-left: 2rem; }
        .space-x-reverse > * + * { margin-left: 0; margin-right: 2rem; }
        .space-y-2 > * + * { margin-top: 0.5rem; }
        .space-y-4 > * + * { margin-top: 1rem; }
    </style>

</head>
<body class="bg-gray-50 font-arabic">
    <!-- Header -->
    <header class="bg-white shadow-lg">
        <div class="bg-primary-600 text-white py-2">
            <div class="container mx-auto px-4 flex justify-between items-center text-sm">
                <div class="flex items-center gap-4">
                    <span>📞 +966 50 123 4567</span>
                    <span>🚚 توصيل مجاني للطلبات أكثر من 200 ريال</span>
                </div>
                <span>مرحباً بكم في متجر كوبرا</span>
            </div>
        </div>
        
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="bg-primary-600 text-white px-4 py-2 rounded-lg font-bold text-xl">
                    كوبرا
                </div>
                
                <nav class="hidden md:flex items-center space-x-8 space-x-reverse">
                    <a href="#" class="text-gray-700 hover:text-primary-600 font-medium">الرئيسية</a>
                    <a href="#products" class="text-gray-700 hover:text-primary-600 font-medium">المنتجات</a>
                    <a href="#services" class="text-gray-700 hover:text-primary-600 font-medium">الخدمات</a>
                    <a href="#about" class="text-gray-700 hover:text-primary-600 font-medium">من نحن</a>
                    <a href="#contact" class="text-gray-700 hover:text-primary-600 font-medium">اتصل بنا</a>
                </nav>
                
                <div class="flex items-center gap-4">
                    <button class="relative">
                        🛒
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                    </button>
                    <button class="bg-primary-600 text-white px-4 py-2 rounded-lg">تسجيل الدخول</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl lg:text-6xl font-bold mb-6">
                مرحباً بكم في
                <span class="block text-yellow-300">متجر كوبرا</span>
            </h1>
            <p class="text-xl mb-8 text-blue-100">
                أفضل المنتجات والخدمات مع توصيل سريع وآمن إلى باب منزلك
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-bold py-3 px-8 rounded-lg">
                    🛍️ تسوق الآن
                </button>
                <button class="bg-transparent border-2 border-white hover:bg-white hover:text-primary-600 font-bold py-3 px-8 rounded-lg">
                    🔧 اطلب خدمة
                </button>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        🚚
                    </div>
                    <h3 class="font-semibold mb-2">توصيل سريع</h3>
                    <p class="text-gray-600">توصيل في نفس اليوم</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        🛡️
                    </div>
                    <h3 class="font-semibold mb-2">دفع آمن</h3>
                    <p class="text-gray-600">حماية كاملة للبيانات</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        ⏰
                    </div>
                    <h3 class="font-semibold mb-2">خدمة 24/7</h3>
                    <p class="text-gray-600">دعم فني متواصل</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        ✅
                    </div>
                    <h3 class="font-semibold mb-2">منتجات أصلية</h3>
                    <p class="text-gray-600">ضمان الجودة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">المنتجات المميزة</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Product 1 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="h-48 bg-gray-200 flex items-center justify-center">
                        📱
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold mb-2">هاتف ذكي متطور</h3>
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-600 mr-2">(124)</span>
                        </div>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="text-lg font-bold text-primary-600">2500 ريال</span>
                            <span class="text-sm text-gray-500 line-through">3000 ريال</span>
                        </div>
                        <button class="w-full bg-primary-600 text-white py-2 rounded-lg">أضف للسلة</button>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="h-48 bg-gray-200 flex items-center justify-center">
                        💻
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold mb-2">لابتوب عالي الأداء</h3>
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-600 mr-2">(89)</span>
                        </div>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="text-lg font-bold text-primary-600">4500 ريال</span>
                            <span class="text-sm text-gray-500 line-through">5200 ريال</span>
                        </div>
                        <button class="w-full bg-primary-600 text-white py-2 rounded-lg">أضف للسلة</button>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="h-48 bg-gray-200 flex items-center justify-center">
                        ⌚
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold mb-2">ساعة ذكية رياضية</h3>
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-600 mr-2">(156)</span>
                        </div>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="text-lg font-bold text-primary-600">800 ريال</span>
                            <span class="text-sm text-gray-500 line-through">1000 ريال</span>
                        </div>
                        <button class="w-full bg-primary-600 text-white py-2 rounded-lg">أضف للسلة</button>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="h-48 bg-gray-200 flex items-center justify-center">
                        🎧
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold mb-2">سماعات لاسلكية</h3>
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-600 mr-2">(203)</span>
                        </div>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="text-lg font-bold text-primary-600">350 ريال</span>
                            <span class="text-sm text-gray-500 line-through">450 ريال</span>
                        </div>
                        <button class="w-full bg-primary-600 text-white py-2 rounded-lg">أضف للسلة</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">خدماتنا</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6 border rounded-lg">
                    <div class="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        🔧
                    </div>
                    <h3 class="text-xl font-semibold mb-3">صيانة الأجهزة</h3>
                    <p class="text-gray-600 mb-4">صيانة شاملة للهواتف والحاسوب</p>
                    <p class="text-primary-600 font-bold mb-4">يبدأ من 100 ريال</p>
                    <button class="bg-primary-600 text-white px-6 py-2 rounded-lg">اطلب الخدمة</button>
                </div>

                <div class="text-center p-6 border rounded-lg">
                    <div class="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        🏠
                    </div>
                    <h3 class="text-xl font-semibold mb-3">خدمات منزلية</h3>
                    <p class="text-gray-600 mb-4">تنظيف وصيانة وإصلاحات منزلية</p>
                    <p class="text-primary-600 font-bold mb-4">يبدأ من 80 ريال</p>
                    <button class="bg-primary-600 text-white px-6 py-2 rounded-lg">اطلب الخدمة</button>
                </div>

                <div class="text-center p-6 border rounded-lg">
                    <div class="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        🚚
                    </div>
                    <h3 class="text-xl font-semibold mb-3">توصيل وتركيب</h3>
                    <p class="text-gray-600 mb-4">توصيل وتركيب الأجهزة والأثاث</p>
                    <p class="text-primary-600 font-bold mb-4">يبدأ من 50 ريال</p>
                    <button class="bg-primary-600 text-white px-6 py-2 rounded-lg">اطلب الخدمة</button>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center max-w-3xl mx-auto">
                <h2 class="text-3xl font-bold mb-6">من نحن</h2>
                <p class="text-gray-600 text-lg leading-relaxed mb-8">
                    متجر كوبرا هو منصة إلكترونية رائدة في المملكة العربية السعودية، نقدم أفضل المنتجات والخدمات 
                    مع التزامنا بأعلى معايير الجودة والخدمة. نسعى لتوفير تجربة تسوق استثنائية لعملائنا.
                </p>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-600 mb-2">10,000+</div>
                        <div class="text-gray-600">عميل راضٍ</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-600 mb-2">5</div>
                        <div class="text-gray-600">سنوات خبرة</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-600 mb-2">24/7</div>
                        <div class="text-gray-600">خدمة العملاء</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-600 mb-2">50+</div>
                        <div class="text-gray-600">مدينة نخدمها</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">تواصل معنا</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <h3 class="text-xl font-semibold mb-6">معلومات الاتصال</h3>
                    <div class="space-y-4">
                        <div class="flex items-center gap-3">
                            <span class="text-primary-600">📞</span>
                            <span>+966 50 123 4567</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <span class="text-primary-600">✉️</span>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center gap-3">
                            <span class="text-primary-600">📍</span>
                            <span>الرياض، المملكة العربية السعودية</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <form class="space-y-4">
                        <input type="text" placeholder="الاسم الكامل" class="w-full p-3 border rounded-lg">
                        <input type="email" placeholder="البريد الإلكتروني" class="w-full p-3 border rounded-lg">
                        <input type="tel" placeholder="رقم الهاتف" class="w-full p-3 border rounded-lg">
                        <textarea placeholder="الرسالة" rows="4" class="w-full p-3 border rounded-lg"></textarea>
                        <button type="submit" class="w-full bg-primary-600 text-white py-3 rounded-lg">إرسال الرسالة</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="bg-primary-600 text-white px-4 py-2 rounded-lg font-bold text-xl mb-4 inline-block">
                        كوبرا
                    </div>
                    <p class="text-gray-300">متجر إلكتروني احترافي يوفر أفضل المنتجات والخدمات</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">روابط سريعة</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#">الرئيسية</a></li>
                        <li><a href="#">المنتجات</a></li>
                        <li><a href="#">الخدمات</a></li>
                        <li><a href="#">من نحن</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">خدمة العملاء</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#">مركز المساعدة</a></li>
                        <li><a href="#">الشحن والتوصيل</a></li>
                        <li><a href="#">الإرجاع والاستبدال</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">تواصل معنا</h4>
                    <div class="space-y-2 text-gray-300">
                        <div>📞 +966 50 123 4567</div>
                        <div>✉️ <EMAIL></div>
                        <div>📍 الرياض، السعودية</div>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-300">
                <p>© 2024 كوبرا. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Notification -->
    <div id="notification" class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300">
        هذه معاينة للموقع. للحصول على النسخة الكاملة، يرجى تثبيت Node.js وتشغيل المشروع.
    </div>

    <script>
        // Show notification
        setTimeout(() => {
            document.getElementById('notification').classList.remove('translate-x-full');
        }, 1000);

        // Hide notification after 5 seconds
        setTimeout(() => {
            document.getElementById('notification').classList.add('translate-x-full');
        }, 6000);

        // Add click handlers for buttons
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', (e) => {
                if (button.textContent.includes('أضف للسلة')) {
                    alert('تم إضافة المنتج لسلة التسوق! 🛒');
                } else if (button.textContent.includes('اطلب الخدمة')) {
                    alert('سيتم توجيهك لصفحة طلب الخدمة! 🔧');
                } else if (button.textContent.includes('تسجيل الدخول')) {
                    alert('سيتم فتح نافذة تسجيل الدخول! 👤');
                }
            });
        });
    </script>
</body>
</html>
