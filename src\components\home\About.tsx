import { Users, Award, Clock, Globe } from 'lucide-react'

export default function About() {
  const stats = [
    {
      icon: <Users className="w-8 h-8" />,
      number: '10,000+',
      label: 'عميل راضٍ'
    },
    {
      icon: <Award className="w-8 h-8" />,
      number: '5',
      label: 'سنوات خبرة'
    },
    {
      icon: <Clock className="w-8 h-8" />,
      number: '24/7',
      label: 'خدمة العملاء'
    },
    {
      icon: <Globe className="w-8 h-8" />,
      number: '50+',
      label: 'مدينة نخدمها'
    }
  ]

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      {/* Content */}
      <div>
        <h2 className="text-3xl font-bold text-gray-800 mb-6">من نحن</h2>
        <p className="text-gray-600 mb-6 leading-relaxed">
          متجر كوبرا هو منصة إلكترونية رائدة في المملكة العربية السعودية، نقدم أفضل المنتجات والخدمات 
          مع التزامنا بأعلى معايير الجودة والخدمة. نسعى لتوفير تجربة تسوق استثنائية لعملائنا من خلال 
          منتجات أصلية وخدمات احترافية.
        </p>
        <p className="text-gray-600 mb-8 leading-relaxed">
          فريقنا المتخصص يعمل على مدار الساعة لضمان رضاكم وتقديم أفضل الحلول لاحتياجاتكم. 
          نؤمن بأن النجاح يأتي من خلال بناء علاقات طويلة الأمد مع عملائنا الكرام.
        </p>

        {/* Features */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
            <span className="text-gray-700">منتجات أصلية 100%</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
            <span className="text-gray-700">توصيل سريع وآمن</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
            <span className="text-gray-700">خدمة عملاء متميزة</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
            <span className="text-gray-700">أسعار تنافسية</span>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-primary-100 text-primary-600 rounded-lg mx-auto mb-4">
              {stat.icon}
            </div>
            <div className="text-2xl font-bold text-gray-800 mb-2">{stat.number}</div>
            <div className="text-gray-600">{stat.label}</div>
          </div>
        ))}
      </div>
    </div>
  )
}
