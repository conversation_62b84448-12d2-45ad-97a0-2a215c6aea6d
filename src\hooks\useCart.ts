'use client'

import { useState, useEffect } from 'react'

export interface CartItem {
  id: string
  name: string
  price: number
  image: string
  quantity: number
}

export function useCart() {
  const [items, setItems] = useState<CartItem[]>([])

  useEffect(() => {
    // Load cart from localStorage
    const savedCart = localStorage.getItem('cart')
    if (savedCart) {
      setItems(JSON.parse(savedCart))
    }
  }, [])

  const saveCart = (cartItems: CartItem[]) => {
    setItems(cartItems)
    localStorage.setItem('cart', JSON.stringify(cartItems))
  }

  const addItem = (item: Omit<CartItem, 'quantity'> & { quantity?: number }) => {
    const existingItem = items.find(i => i.id === item.id)
    
    if (existingItem) {
      const updatedItems = items.map(i =>
        i.id === item.id
          ? { ...i, quantity: i.quantity + (item.quantity || 1) }
          : i
      )
      saveCart(updatedItems)
    } else {
      const newItem: CartItem = {
        ...item,
        quantity: item.quantity || 1
      }
      saveCart([...items, newItem])
    }
  }

  const removeItem = (id: string) => {
    const updatedItems = items.filter(item => item.id !== id)
    saveCart(updatedItems)
  }

  const updateQuantity = (id: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(id)
      return
    }

    const updatedItems = items.map(item =>
      item.id === id ? { ...item, quantity } : item
    )
    saveCart(updatedItems)
  }

  const clearCart = () => {
    saveCart([])
  }

  const getTotalPrice = () => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0)
  }

  return {
    items,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getTotalPrice,
    getTotalItems
  }
}
