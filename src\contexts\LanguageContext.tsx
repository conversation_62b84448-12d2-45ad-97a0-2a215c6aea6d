'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

export interface Language {
  code: string
  name: string
  nativeName: string
  flag: string
  dir: 'ltr' | 'rtl'
}

export const languages: Language[] = [
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇪🇬',
    dir: 'rtl'
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    dir: 'ltr'
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    dir: 'ltr'
  }
]

export interface Translations {
  // Navigation
  home: string
  products: string
  services: string
  about: string
  contact: string
  cart: string
  profile: string
  admin: string
  login: string
  register: string
  logout: string

  // Common
  search: string
  loading: string
  error: string
  success: string
  cancel: string
  save: string
  edit: string
  delete: string
  add: string
  update: string
  confirm: string
  close: string
  back: string
  next: string
  previous: string
  submit: string
  reset: string
  clear: string

  // Hero Section
  welcomeMessage: string
  brandSlogan: string
  brandDescription: string
  mainDescription: string
  searchPlaceholder: string
  shopNow: string
  requestService: string

  // Stats
  productsAvailable: string
  deliveryAllGovernorates: string
  support247: string

  // Features
  fastDelivery: string
  securePayment: string
  originalProducts: string
  fastDeliveryDesc: string
  securePaymentDesc: string
  originalProductsDesc: string
  whyChooseUs: string

  // Theme Settings
  themeSettings: string
  darkMode: string
  lightMode: string
  customTheme: string
  resetTheme: string
  appearance: string

  // Language Settings
  languageSettings: string
  selectLanguage: string

  // Products
  price: string
  addToCart: string
  outOfStock: string
  inStock: string
  category: string
  brand: string
  rating: string
  reviews: string
  originalPrice: string
  salePrice: string
  discount: string
  viewDetails: string
  addToFavorites: string
  removeFromFavorites: string
  compareProducts: string
  sortBy: string
  filterBy: string
  showResults: string
  noProductsFound: string
  productAdded: string
  productRemoved: string

  // Services
  duration: string
  requestThisService: string
  serviceDetails: string
  serviceFeatures: string
  availability: string
  mostPopular: string
  customService: string
  freeConsultation: string
  getQuote: string

  // Cart
  cartEmpty: string
  total: string
  checkout: string
  quantity: string
  removeItem: string
  updateQuantity: string
  continueShopping: string
  proceedToCheckout: string

  // Header
  searchProducts: string
  voiceSearch: string
  notifications: string
  userMenu: string
  mobileMenu: string

  // Contact Info
  phone: string
  whatsapp: string
  emailAddress: string
  address: string
  workingHours: string

  // Footer
  quickLinks: string
  customerService: string
  contactUs: string
  allRightsReserved: string
  followUs: string
  companyInfo: string
  socialMedia: string
  footerDescription: string

  // Settings
  settings: string
  siteSettings: string
  contactInfo: string
  paymentInfo: string
  businessInfo: string
  addNew: string
  edit: string
  delete: string

  // Contact Types
  phone: string

  // Payment Types
  bank: string
  wallet: string
  cash: string

  // Social Platforms
  facebook: string
  instagram: string
  twitter: string
  youtube: string
  linkedin: string
  tiktok: string
  newsletter: string
  subscribeNewsletter: string
  enterEmail: string
  subscribe: string

  // Forms
  name: string
  email: string
  message: string
  subject: string
  phoneNumber: string
  required: string
  optional: string

  // Notifications
  itemAddedToCart: string
  itemRemovedFromCart: string
  itemAddedToFavorites: string
  itemRemovedFromFavorites: string
  settingsSaved: string
  errorOccurred: string

  // Time
  today: string
  yesterday: string
  tomorrow: string
  thisWeek: string
  thisMonth: string

  // Status
  available: string
  unavailable: string
  comingSoon: string
  soldOut: string
  new: string
  featured: string

  // Actions
  viewAll: string
  showMore: string
  showLess: string
  readMore: string
  readLess: string
  learnMore: string
  getStarted: string

  // Company Info
  companyName: string
  companySlogan: string
  companyDescription: string
  aboutCompany: string
  ourMission: string
  ourVision: string
  ourValues: string
}

const translations: Record<string, Translations> = {
  ar: {
    // Navigation
    home: 'الرئيسية',
    products: 'المنتجات',
    services: 'الخدمات',
    about: 'من نحن',
    contact: 'اتصل بنا',
    cart: 'السلة',
    profile: 'الملف الشخصي',
    admin: 'الإدارة',
    login: 'تسجيل الدخول',
    register: 'إنشاء حساب',
    logout: 'تسجيل الخروج',

    // Common
    search: 'بحث',
    loading: 'جاري التحميل...',
    error: 'حدث خطأ',
    success: 'تم بنجاح',
    cancel: 'إلغاء',
    save: 'حفظ',
    edit: 'تعديل',
    delete: 'حذف',
    add: 'إضافة',
    update: 'تحديث',
    confirm: 'تأكيد',
    close: 'إغلاق',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    submit: 'إرسال',
    reset: 'إعادة تعيين',
    clear: 'مسح',
    
    // Hero Section
    welcomeMessage: 'مرحباً بكم في مركز البدوي',
    brandSlogan: 'اسم له تاريخ',
    brandDescription: 'الاسم يعني الثقة والجودة',
    mainDescription: 'أفضل المنتجات والخدمات مع توصيل سريع وآمن إلى باب منزلك في جميع أنحاء مصر',
    searchPlaceholder: 'ابحث في أكثر من 1000 منتج وخدمة...',
    shopNow: 'تسوق الآن',
    requestService: 'اطلب خدمة',
    
    // Stats
    productsAvailable: '+1000 منتج متاح',
    deliveryAllGovernorates: 'توصيل لجميع المحافظات',
    support247: 'دعم فني 24/7',
    
    // Features
    fastDelivery: 'توصيل سريع',
    securePayment: 'دفع آمن',
    originalProducts: 'منتجات أصلية',
    fastDeliveryDesc: 'توصيل في نفس اليوم داخل المدينة',
    securePaymentDesc: 'حماية كاملة لبياناتك المالية',
    originalProductsDesc: 'ضمان الجودة والأصالة',
    whyChooseUs: 'لماذا مركز البدوي؟',

    // Theme Settings
    themeSettings: 'إعدادات الثيم',
    darkMode: 'الوضع الداكن',
    lightMode: 'الوضع الفاتح',
    customTheme: 'ثيم مخصص',
    resetTheme: 'إعادة تعيين الثيم',
    appearance: 'المظهر',

    // Language Settings
    languageSettings: 'إعدادات اللغة',
    selectLanguage: 'اختر اللغة',

    // Products
    price: 'السعر',
    addToCart: 'أضف للسلة',
    outOfStock: 'غير متوفر',
    inStock: 'متوفر',
    category: 'التصنيف',
    brand: 'الماركة',
    rating: 'التقييم',
    reviews: 'تقييم',
    originalPrice: 'السعر الأصلي',
    salePrice: 'سعر البيع',
    discount: 'خصم',
    viewDetails: 'عرض التفاصيل',
    addToFavorites: 'أضف للمفضلة',
    removeFromFavorites: 'إزالة من المفضلة',
    compareProducts: 'مقارنة المنتجات',
    sortBy: 'ترتيب حسب',
    filterBy: 'فلترة حسب',
    showResults: 'عرض النتائج',
    noProductsFound: 'لم يتم العثور على منتجات',
    productAdded: 'تم إضافة المنتج',
    productRemoved: 'تم إزالة المنتج',

    // Services
    duration: 'المدة',
    requestThisService: 'اطلب هذه الخدمة',
    serviceDetails: 'تفاصيل الخدمة',
    serviceFeatures: 'مميزات الخدمة',
    availability: 'التوفر',
    mostPopular: 'الأكثر طلباً',
    customService: 'خدمة مخصصة',
    freeConsultation: 'استشارة مجانية',
    getQuote: 'احصل على عرض سعر',

    // Cart
    cartEmpty: 'السلة فارغة',
    total: 'المجموع',
    checkout: 'إتمام الشراء',
    quantity: 'الكمية',
    removeItem: 'إزالة العنصر',
    updateQuantity: 'تحديث الكمية',
    continueShopping: 'متابعة التسوق',
    proceedToCheckout: 'المتابعة للدفع',

    // Header
    searchProducts: 'ابحث في المنتجات والخدمات',
    voiceSearch: 'البحث الصوتي',
    notifications: 'الإشعارات',
    userMenu: 'قائمة المستخدم',
    mobileMenu: 'القائمة',

    // Contact Info
    phone: 'الهاتف',
    whatsapp: 'واتساب',
    emailAddress: 'البريد الإلكتروني',
    address: 'العنوان',
    workingHours: 'ساعات العمل',

    // Footer
    quickLinks: 'روابط سريعة',
    customerService: 'خدمة العملاء',
    contactUs: 'تواصل معنا',
    allRightsReserved: 'جميع الحقوق محفوظة',
    followUs: 'تابعنا',
    companyInfo: 'معلومات الشركة',
    socialMedia: 'وسائل التواصل الاجتماعي',
    footerDescription: 'مركز البدوي - وجهتك المثالية للتسوق والخدمات المتميزة',

    // Settings
    settings: 'الإعدادات',
    siteSettings: 'إعدادات الموقع',
    contactInfo: 'معلومات الاتصال',
    paymentInfo: 'معلومات الدفع',
    businessInfo: 'معلومات الأعمال',
    addNew: 'إضافة جديد',
    edit: 'تعديل',
    delete: 'حذف',

    // Contact Types
    phone: 'هاتف',

    // Payment Types
    bank: 'حساب بنكي',
    wallet: 'محفظة إلكترونية',
    cash: 'نقدي',

    // Social Platforms
    facebook: 'فيسبوك',
    instagram: 'إنستغرام',
    twitter: 'تويتر',
    youtube: 'يوتيوب',
    linkedin: 'لينكد إن',
    tiktok: 'تيك توك',
    newsletter: 'النشرة الإخبارية',
    subscribeNewsletter: 'اشترك في النشرة',
    enterEmail: 'أدخل بريدك الإلكتروني',
    subscribe: 'اشتراك',

    // Forms
    name: 'الاسم',
    email: 'البريد الإلكتروني',
    message: 'الرسالة',
    subject: 'الموضوع',
    phoneNumber: 'رقم الهاتف',
    required: 'مطلوب',
    optional: 'اختياري',

    // Notifications
    itemAddedToCart: 'تم إضافة العنصر للسلة',
    itemRemovedFromCart: 'تم إزالة العنصر من السلة',
    itemAddedToFavorites: 'تم إضافة العنصر للمفضلة',
    itemRemovedFromFavorites: 'تم إزالة العنصر من المفضلة',
    settingsSaved: 'تم حفظ الإعدادات',
    errorOccurred: 'حدث خطأ',

    // Time
    today: 'اليوم',
    yesterday: 'أمس',
    tomorrow: 'غداً',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',

    // Status
    available: 'متاح',
    unavailable: 'غير متاح',
    comingSoon: 'قريباً',
    soldOut: 'نفد المخزون',
    new: 'جديد',
    featured: 'مميز',

    // Actions
    viewAll: 'عرض الكل',
    showMore: 'عرض المزيد',
    showLess: 'عرض أقل',
    readMore: 'اقرأ المزيد',
    readLess: 'اقرأ أقل',
    learnMore: 'تعلم المزيد',
    getStarted: 'ابدأ الآن',

    // Company Info
    companyName: 'مركز البدوي',
    companySlogan: 'اسم له تاريخ',
    companyDescription: 'الاسم يعني الثقة والجودة',
    aboutCompany: 'عن الشركة',
    ourMission: 'رسالتنا',
    ourVision: 'رؤيتنا',
    ourValues: 'قيمنا'
  },
  
  en: {
    // Navigation
    home: 'Home',
    products: 'Products',
    services: 'Services',
    about: 'About',
    contact: 'Contact',
    cart: 'Cart',
    profile: 'Profile',
    admin: 'Admin',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',

    // Common
    search: 'Search',
    loading: 'Loading...',
    error: 'Error occurred',
    success: 'Success',
    cancel: 'Cancel',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    update: 'Update',
    confirm: 'Confirm',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    reset: 'Reset',
    clear: 'Clear',
    
    // Hero Section
    welcomeMessage: 'Welcome to Al-Badawi Center',
    brandSlogan: 'A Name with History',
    brandDescription: 'The name means trust and quality',
    mainDescription: 'Best products and services with fast and secure delivery to your doorstep across Egypt',
    searchPlaceholder: 'Search over 1000 products and services...',
    shopNow: 'Shop Now',
    requestService: 'Request Service',
    
    // Stats
    productsAvailable: '+1000 Products Available',
    deliveryAllGovernorates: 'Delivery to All Governorates',
    support247: '24/7 Technical Support',
    
    // Features
    fastDelivery: 'Fast Delivery',
    securePayment: 'Secure Payment',
    originalProducts: 'Original Products',
    fastDeliveryDesc: 'Same day delivery within the city',
    securePaymentDesc: 'Complete protection for your financial data',
    originalProductsDesc: 'Quality and authenticity guarantee',
    whyChooseUs: 'Why Choose Al-Badawi Center?',

    // Theme Settings
    themeSettings: 'Theme Settings',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    customTheme: 'Custom Theme',
    resetTheme: 'Reset Theme',
    appearance: 'Appearance',

    // Language Settings
    languageSettings: 'Language Settings',
    selectLanguage: 'Select Language',

    // Products
    price: 'Price',
    addToCart: 'Add to Cart',
    outOfStock: 'Out of Stock',
    inStock: 'In Stock',
    category: 'Category',
    brand: 'Brand',
    rating: 'Rating',
    reviews: 'reviews',
    originalPrice: 'Original Price',
    salePrice: 'Sale Price',
    discount: 'Discount',
    viewDetails: 'View Details',
    addToFavorites: 'Add to Favorites',
    removeFromFavorites: 'Remove from Favorites',
    compareProducts: 'Compare Products',
    sortBy: 'Sort By',
    filterBy: 'Filter By',
    showResults: 'Show Results',
    noProductsFound: 'No Products Found',
    productAdded: 'Product Added',
    productRemoved: 'Product Removed',

    // Services
    duration: 'Duration',
    requestThisService: 'Request This Service',
    serviceDetails: 'Service Details',
    serviceFeatures: 'Service Features',
    availability: 'Availability',
    mostPopular: 'Most Popular',
    customService: 'Custom Service',
    freeConsultation: 'Free Consultation',
    getQuote: 'Get Quote',

    // Cart
    cartEmpty: 'Cart is Empty',
    total: 'Total',
    checkout: 'Checkout',
    quantity: 'Quantity',
    removeItem: 'Remove Item',
    updateQuantity: 'Update Quantity',
    continueShopping: 'Continue Shopping',
    proceedToCheckout: 'Proceed to Checkout',

    // Header
    searchProducts: 'Search products and services',
    voiceSearch: 'Voice Search',
    notifications: 'Notifications',
    userMenu: 'User Menu',
    mobileMenu: 'Menu',

    // Contact Info
    phone: 'Phone',
    whatsapp: 'WhatsApp',
    emailAddress: 'Email',
    address: 'Address',
    workingHours: 'Working Hours',

    // Footer
    quickLinks: 'Quick Links',
    customerService: 'Customer Service',
    contactUs: 'Contact Us',
    allRightsReserved: 'All Rights Reserved',
    followUs: 'Follow Us',
    companyInfo: 'Company Info',
    socialMedia: 'Social Media',
    footerDescription: 'Al-Badawi Center - Your ideal destination for shopping and premium services',

    // Settings
    settings: 'Settings',
    siteSettings: 'Site Settings',
    contactInfo: 'Contact Information',
    paymentInfo: 'Payment Information',
    businessInfo: 'Business Information',
    addNew: 'Add New',
    edit: 'Edit',
    delete: 'Delete',

    // Contact Types
    phone: 'Phone',

    // Payment Types
    bank: 'Bank Account',
    wallet: 'Digital Wallet',
    cash: 'Cash',

    // Social Platforms
    facebook: 'Facebook',
    instagram: 'Instagram',
    twitter: 'Twitter',
    youtube: 'YouTube',
    linkedin: 'LinkedIn',
    tiktok: 'TikTok',
    newsletter: 'Newsletter',
    subscribeNewsletter: 'Subscribe to Newsletter',
    enterEmail: 'Enter your email',
    subscribe: 'Subscribe',

    // Forms
    name: 'Name',
    email: 'Email',
    message: 'Message',
    subject: 'Subject',
    phoneNumber: 'Phone Number',
    required: 'Required',
    optional: 'Optional',

    // Notifications
    itemAddedToCart: 'Item added to cart',
    itemRemovedFromCart: 'Item removed from cart',
    itemAddedToFavorites: 'Item added to favorites',
    itemRemovedFromFavorites: 'Item removed from favorites',
    settingsSaved: 'Settings saved',
    errorOccurred: 'An error occurred',

    // Time
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week',
    thisMonth: 'This Month',

    // Status
    available: 'Available',
    unavailable: 'Unavailable',
    comingSoon: 'Coming Soon',
    soldOut: 'Sold Out',
    new: 'New',
    featured: 'Featured',

    // Actions
    viewAll: 'View All',
    showMore: 'Show More',
    showLess: 'Show Less',
    readMore: 'Read More',
    readLess: 'Read Less',
    learnMore: 'Learn More',
    getStarted: 'Get Started',

    // Company Info
    companyName: 'Al-Badawi Center',
    companySlogan: 'A Name with History',
    companyDescription: 'The name means trust and quality',
    aboutCompany: 'About Company',
    ourMission: 'Our Mission',
    ourVision: 'Our Vision',
    ourValues: 'Our Values'
  },
  
  fr: {
    // Navigation
    home: 'Accueil',
    products: 'Produits',
    services: 'Services',
    about: 'À propos',
    contact: 'Contact',
    cart: 'Panier',
    profile: 'Profil',
    admin: 'Admin',
    login: 'Connexion',
    register: 'S\'inscrire',
    logout: 'Déconnexion',

    // Common
    search: 'Rechercher',
    loading: 'Chargement...',
    error: 'Erreur survenue',
    success: 'Succès',
    cancel: 'Annuler',
    save: 'Sauvegarder',
    edit: 'Modifier',
    delete: 'Supprimer',
    add: 'Ajouter',
    update: 'Mettre à jour',
    confirm: 'Confirmer',
    close: 'Fermer',
    back: 'Retour',
    next: 'Suivant',
    previous: 'Précédent',
    submit: 'Soumettre',
    reset: 'Réinitialiser',
    clear: 'Effacer',

    // Hero Section
    welcomeMessage: 'Bienvenue au Centre Al-Badawi',
    brandSlogan: 'Un Nom avec Histoire',
    brandDescription: 'Le nom signifie confiance et qualité',
    mainDescription: 'Meilleurs produits et services avec livraison rapide et sécurisée à votre porte en Égypte',
    searchPlaceholder: 'Rechercher parmi plus de 1000 produits et services...',
    shopNow: 'Acheter Maintenant',
    requestService: 'Demander un Service',
    
    // Stats
    productsAvailable: '+1000 Produits Disponibles',
    deliveryAllGovernorates: 'Livraison dans Tous les Gouvernorats',
    support247: 'Support Technique 24/7',
    
    // Features
    fastDelivery: 'Livraison Rapide',
    securePayment: 'Paiement Sécurisé',
    originalProducts: 'Produits Originaux',
    fastDeliveryDesc: 'Livraison le jour même dans la ville',
    securePaymentDesc: 'Protection complète de vos données financières',
    originalProductsDesc: 'Garantie de qualité et authenticité',
    whyChooseUs: 'Pourquoi choisir le Centre Al-Badawi?',

    // Theme Settings
    themeSettings: 'Paramètres du Thème',
    darkMode: 'Mode Sombre',
    lightMode: 'Mode Clair',
    customTheme: 'Thème Personnalisé',
    resetTheme: 'Réinitialiser le Thème',
    appearance: 'Apparence',

    // Language Settings
    languageSettings: 'Paramètres de Langue',
    selectLanguage: 'Sélectionner la Langue',
    
    // Products
    price: 'Prix',
    addToCart: 'Ajouter au Panier',
    outOfStock: 'Rupture de Stock',
    inStock: 'En Stock',
    category: 'Catégorie',
    brand: 'Marque',
    rating: 'Évaluation',
    reviews: 'avis',
    originalPrice: 'Prix original',
    salePrice: 'Prix de vente',
    discount: 'Remise',
    viewDetails: 'Voir les détails',
    addToFavorites: 'Ajouter aux favoris',
    removeFromFavorites: 'Retirer des favoris',
    compareProducts: 'Comparer les produits',
    sortBy: 'Trier par',
    filterBy: 'Filtrer par',
    showResults: 'Afficher les résultats',
    noProductsFound: 'Aucun produit trouvé',
    productAdded: 'Produit ajouté',
    productRemoved: 'Produit retiré',

    // Services
    duration: 'Durée',
    requestThisService: 'Demander ce Service',
    serviceDetails: 'Détails du Service',
    serviceFeatures: 'Caractéristiques du service',
    availability: 'Disponibilité',
    mostPopular: 'Le plus populaire',
    customService: 'Service personnalisé',
    freeConsultation: 'Consultation gratuite',
    getQuote: 'Obtenir un devis',

    // Cart
    cartEmpty: 'Panier Vide',
    total: 'Total',
    checkout: 'Commander',
    quantity: 'Quantité',
    removeItem: 'Retirer l\'article',
    updateQuantity: 'Mettre à jour la quantité',
    continueShopping: 'Continuer les achats',
    proceedToCheckout: 'Procéder au paiement',

    // Header
    searchProducts: 'Rechercher produits et services',
    voiceSearch: 'Recherche vocale',
    notifications: 'Notifications',
    userMenu: 'Menu utilisateur',
    mobileMenu: 'Menu',

    // Contact Info
    phone: 'Téléphone',
    whatsapp: 'WhatsApp',
    emailAddress: 'Email',
    address: 'Adresse',
    workingHours: 'Heures de travail',

    // Footer
    quickLinks: 'Liens Rapides',
    customerService: 'Service Client',
    contactUs: 'Nous Contacter',
    allRightsReserved: 'Tous Droits Réservés',
    followUs: 'Suivez-nous',
    companyInfo: 'Informations Entreprise',
    socialMedia: 'Réseaux Sociaux',
    footerDescription: 'Centre Al-Badawi - Votre destination idéale pour le shopping et les services premium',

    // Settings
    settings: 'Paramètres',
    siteSettings: 'Paramètres du Site',
    contactInfo: 'Informations de Contact',
    paymentInfo: 'Informations de Paiement',
    businessInfo: 'Informations Entreprise',
    addNew: 'Ajouter Nouveau',
    edit: 'Modifier',
    delete: 'Supprimer',

    // Contact Types
    phone: 'Téléphone',

    // Payment Types
    bank: 'Compte Bancaire',
    wallet: 'Portefeuille Numérique',
    cash: 'Espèces',

    // Social Platforms
    facebook: 'Facebook',
    instagram: 'Instagram',
    twitter: 'Twitter',
    youtube: 'YouTube',
    linkedin: 'LinkedIn',
    tiktok: 'TikTok',
    newsletter: 'Newsletter',
    subscribeNewsletter: 'S\'abonner à la newsletter',
    enterEmail: 'Entrez votre email',
    subscribe: 'S\'abonner',

    // Forms
    name: 'Nom',
    email: 'Email',
    message: 'Message',
    subject: 'Sujet',
    phoneNumber: 'Numéro de téléphone',
    required: 'Requis',
    optional: 'Optionnel',

    // Notifications
    itemAddedToCart: 'Article ajouté au panier',
    itemRemovedFromCart: 'Article retiré du panier',
    itemAddedToFavorites: 'Article ajouté aux favoris',
    itemRemovedFromFavorites: 'Article retiré des favoris',
    settingsSaved: 'Paramètres sauvegardés',
    errorOccurred: 'Une erreur s\'est produite',

    // Time
    today: 'Aujourd\'hui',
    yesterday: 'Hier',
    tomorrow: 'Demain',
    thisWeek: 'Cette semaine',
    thisMonth: 'Ce mois',

    // Status
    available: 'Disponible',
    unavailable: 'Indisponible',
    comingSoon: 'Bientôt disponible',
    soldOut: 'Épuisé',
    new: 'Nouveau',
    featured: 'En vedette',

    // Actions
    viewAll: 'Voir tout',
    showMore: 'Afficher plus',
    showLess: 'Afficher moins',
    readMore: 'Lire plus',
    readLess: 'Lire moins',
    learnMore: 'En savoir plus',
    getStarted: 'Commencer',

    // Company Info
    companyName: 'Centre Al-Badawi',
    companySlogan: 'Un nom avec une histoire',
    companyDescription: 'Le nom signifie confiance et qualité',
    aboutCompany: 'À propos de l\'entreprise',
    ourMission: 'Notre mission',
    ourVision: 'Notre vision',
    ourValues: 'Nos valeurs'
  }
}

interface LanguageContextType {
  currentLanguage: Language
  setLanguage: (code: string) => void
  t: (key: keyof Translations) => string
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [currentLanguageCode, setCurrentLanguageCode] = useState('ar')

  // Load language from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language')
    if (savedLanguage && languages.find(l => l.code === savedLanguage)) {
      setCurrentLanguageCode(savedLanguage)
    }
  }, [])

  // Apply language direction to document
  useEffect(() => {
    const language = getCurrentLanguage()
    document.documentElement.dir = language.dir
    document.documentElement.lang = language.code
  }, [currentLanguageCode])

  const getCurrentLanguage = (): Language => {
    return languages.find(l => l.code === currentLanguageCode) || languages[0]
  }

  const setLanguage = (code: string) => {
    if (languages.find(l => l.code === code)) {
      setCurrentLanguageCode(code)
      localStorage.setItem('language', code)
    }
  }

  const t = (key: keyof Translations): string => {
    return translations[currentLanguageCode]?.[key] || translations['ar'][key] || key
  }

  const value: LanguageContextType = {
    currentLanguage: getCurrentLanguage(),
    setLanguage,
    t,
    isRTL: getCurrentLanguage().dir === 'rtl'
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
