'use client'

import { useState } from 'react'
import { Plus, Edit, Trash2, Eye, Search, Filter } from 'lucide-react'

interface Category {
  id: string
  name: string
  description: string
  image: string
  parentId?: string
  productsCount: number
  isActive: boolean
  createdAt: string
}

export default function CategoryManager() {
  const [categories, setCategories] = useState<Category[]>([
    {
      id: '1',
      name: 'الإلكترونيات',
      description: 'جميع الأجهزة الإلكترونية والتقنية',
      image: '/images/electronics.jpg',
      productsCount: 45,
      isActive: true,
      createdAt: '2024-01-01'
    },
    {
      id: '2',
      name: 'الهواتف الذكية',
      description: 'هواتف ذكية من جميع الماركات',
      image: '/images/phones.jpg',
      parentId: '1',
      productsCount: 25,
      isActive: true,
      createdAt: '2024-01-02'
    },
    {
      id: '3',
      name: 'أجه<PERSON>ة الكمبيوتر',
      description: 'لابتوب وديسكتوب وإكسسوارات',
      image: '/images/computers.jpg',
      parentId: '1',
      productsCount: 20,
      isActive: true,
      createdAt: '2024-01-03'
    },
    {
      id: '4',
      name: 'الأزياء والملابس',
      description: 'ملابس رجالية ونسائية وأطفال',
      image: '/images/fashion.jpg',
      productsCount: 78,
      isActive: true,
      createdAt: '2024-01-04'
    },
    {
      id: '5',
      name: 'المنزل والحديقة',
      description: 'أدوات منزلية ومعدات الحديقة',
      image: '/images/home.jpg',
      productsCount: 32,
      isActive: false,
      createdAt: '2024-01-05'
    }
  ])

  const [showAddModal, setShowAddModal] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all')

  const [newCategory, setNewCategory] = useState({
    name: '',
    description: '',
    parentId: '',
    image: '',
    isActive: true
  })

  const filteredCategories = categories.filter(category => {
    const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         category.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'active' && category.isActive) ||
                         (filterStatus === 'inactive' && !category.isActive)
    
    return matchesSearch && matchesFilter
  })

  const parentCategories = categories.filter(cat => !cat.parentId)

  const handleAddCategory = () => {
    const category: Category = {
      id: Date.now().toString(),
      name: newCategory.name,
      description: newCategory.description,
      image: newCategory.image || '/images/default-category.jpg',
      parentId: newCategory.parentId || undefined,
      productsCount: 0,
      isActive: newCategory.isActive,
      createdAt: new Date().toISOString().split('T')[0]
    }

    setCategories([...categories, category])
    setNewCategory({ name: '', description: '', parentId: '', image: '', isActive: true })
    setShowAddModal(false)
  }

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
    setNewCategory({
      name: category.name,
      description: category.description,
      parentId: category.parentId || '',
      image: category.image,
      isActive: category.isActive
    })
    setShowAddModal(true)
  }

  const handleUpdateCategory = () => {
    if (!editingCategory) return

    const updatedCategories = categories.map(cat =>
      cat.id === editingCategory.id
        ? {
            ...cat,
            name: newCategory.name,
            description: newCategory.description,
            parentId: newCategory.parentId || undefined,
            image: newCategory.image,
            isActive: newCategory.isActive
          }
        : cat
    )

    setCategories(updatedCategories)
    setEditingCategory(null)
    setNewCategory({ name: '', description: '', parentId: '', image: '', isActive: true })
    setShowAddModal(false)
  }

  const handleDeleteCategory = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
      setCategories(categories.filter(cat => cat.id !== id))
    }
  }

  const toggleCategoryStatus = (id: string) => {
    setCategories(categories.map(cat =>
      cat.id === id ? { ...cat, isActive: !cat.isActive } : cat
    ))
  }

  const getCategoryPath = (category: Category): string => {
    if (!category.parentId) return category.name
    
    const parent = categories.find(cat => cat.id === category.parentId)
    return parent ? `${parent.name} > ${category.name}` : category.name
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة التصنيفات</h2>
          <p className="text-gray-600">إدارة تصنيفات المنتجات والخدمات</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          إضافة تصنيف جديد
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في التصنيفات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">جميع التصنيفات</option>
              <option value="active">نشط</option>
              <option value="inactive">غير نشط</option>
            </select>
          </div>
        </div>
      </div>

      {/* Categories Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التصنيف</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوصف</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المسار</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد المنتجات</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الإنشاء</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredCategories.map((category) => (
                <tr key={category.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span className="text-xs text-gray-500">صورة</span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{category.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">{category.description}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600">{getCategoryPath(category)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{category.productsCount}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => toggleCategoryStatus(category.id)}
                      className={`px-2 py-1 text-xs font-medium rounded-full ${
                        category.isActive
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      }`}
                    >
                      {category.isActive ? 'نشط' : 'غير نشط'}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {category.createdAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center gap-2">
                      <button className="text-blue-600 hover:text-blue-800">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditCategory(category)}
                        className="text-green-600 hover:text-green-800"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCategory(category.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              {editingCategory ? 'تعديل التصنيف' : 'إضافة تصنيف جديد'}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">اسم التصنيف</label>
                <input
                  type="text"
                  value={newCategory.name}
                  onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="أدخل اسم التصنيف"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                <textarea
                  value={newCategory.description}
                  onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  rows={3}
                  placeholder="أدخل وصف التصنيف"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">التصنيف الأب</label>
                <select
                  value={newCategory.parentId}
                  onChange={(e) => setNewCategory({ ...newCategory, parentId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">تصنيف رئيسي</option>
                  {parentCategories.map((cat) => (
                    <option key={cat.id} value={cat.id}>{cat.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">رابط الصورة</label>
                <input
                  type="url"
                  value={newCategory.image}
                  onChange={(e) => setNewCategory({ ...newCategory, image: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="https://example.com/image.jpg"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={newCategory.isActive}
                  onChange={(e) => setNewCategory({ ...newCategory, isActive: e.target.checked })}
                  className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                />
                <label htmlFor="isActive" className="mr-2 text-sm text-gray-700">
                  تصنيف نشط
                </label>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={editingCategory ? handleUpdateCategory : handleAddCategory}
                className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg"
              >
                {editingCategory ? 'تحديث' : 'إضافة'}
              </button>
              <button
                onClick={() => {
                  setShowAddModal(false)
                  setEditingCategory(null)
                  setNewCategory({ name: '', description: '', parentId: '', image: '', isActive: true })
                }}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded-lg"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
