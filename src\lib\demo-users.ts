// Demo users for testing
export const demoUsers = [
  {
    id: '1',
    name: 'أحمد محمد',
    email: '<EMAIL>',
    password: 'password123',
    phone: '+20 ************',
    userType: 'regular',
    isWholesaleApproved: false,
    createdAt: '2024-01-15T10:00:00.000Z'
  },
  {
    id: '2',
    name: 'محمد التاجر',
    email: '<EMAIL>',
    password: 'password123',
    phone: '+20 ************',
    userType: 'merchant',
    isWholesaleApproved: true,
    createdAt: '2024-01-10T08:30:00.000Z'
  },
  {
    id: '3',
    name: 'فاطمة أحمد',
    email: '<EMAIL>',
    password: 'password123',
    phone: '+20 ************',
    userType: 'regular',
    isWholesaleApproved: false,
    createdAt: '2024-01-20T14:15:00.000Z'
  }
]

// Initialize demo users in localStorage if not exists
export function initializeDemoUsers() {
  if (typeof window !== 'undefined') {
    const existingUsers = localStorage.getItem('users')
    if (!existingUsers) {
      localStorage.setItem('users', JSON.stringify(demoUsers))
    }
  }
}
