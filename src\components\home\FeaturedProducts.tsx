'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Star, ShoppingCart, Heart } from 'lucide-react'
import { useCart } from '@/hooks/useCart'
import toast from 'react-hot-toast'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  rating: number
  reviews: number
  category: string
  inStock: boolean
}

// Sample data - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
const sampleProducts: Product[] = [
  {
    id: '1',
    name: 'هاتف ذكي متطور',
    price: 2500,
    originalPrice: 3000,
    image: '/images/phone.jpg',
    rating: 4.8,
    reviews: 124,
    category: 'إلكترونيات',
    inStock: true
  },
  {
    id: '2',
    name: 'لابتوب عالي الأداء',
    price: 4500,
    originalPrice: 5200,
    image: '/images/laptop.jpg',
    rating: 4.9,
    reviews: 89,
    category: 'إلكترونيات',
    inStock: true
  },
  {
    id: '3',
    name: 'ساعة ذكية رياضية',
    price: 800,
    originalPrice: 1000,
    image: '/images/watch.jpg',
    rating: 4.6,
    reviews: 156,
    category: 'إكسسوارات',
    inStock: true
  },
  {
    id: '4',
    name: 'سماعات لاسلكية',
    price: 350,
    originalPrice: 450,
    image: '/images/headphones.jpg',
    rating: 4.7,
    reviews: 203,
    category: 'إلكترونيات',
    inStock: true
  }
]

export default function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>([])
  const [favorites, setFavorites] = useState<string[]>([])
  const { addItem } = useCart()

  useEffect(() => {
    // Simulate API call
    setProducts(sampleProducts)
    
    // Load favorites from localStorage
    const savedFavorites = localStorage.getItem('favorites')
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites))
    }
  }, [])

  const toggleFavorite = (productId: string) => {
    const newFavorites = favorites.includes(productId)
      ? favorites.filter(id => id !== productId)
      : [...favorites, productId]
    
    setFavorites(newFavorites)
    localStorage.setItem('favorites', JSON.stringify(newFavorites))
    
    toast.success(
      favorites.includes(productId) 
        ? 'تم إزالة المنتج من المفضلة' 
        : 'تم إضافة المنتج للمفضلة'
    )
  }

  const handleAddToCart = (product: Product) => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    })
    toast.success('تم إضافة المنتج لسلة التسوق')
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ))
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {products.map((product) => (
        <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
          {/* Product Image */}
          <div className="relative">
            <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">صورة المنتج</span>
            </div>
            
            {/* Discount Badge */}
            {product.originalPrice && (
              <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-bold">
                {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% خصم
              </div>
            )}
            
            {/* Favorite Button */}
            <button
              onClick={() => toggleFavorite(product.id)}
              className="absolute top-2 left-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50"
            >
              <Heart
                size={18}
                className={favorites.includes(product.id) ? 'text-red-500 fill-current' : 'text-gray-400'}
              />
            </button>
          </div>

          {/* Product Info */}
          <div className="p-4">
            <div className="text-sm text-gray-500 mb-1">{product.category}</div>
            <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2">{product.name}</h3>
            
            {/* Rating */}
            <div className="flex items-center gap-1 mb-2">
              <div className="flex">{renderStars(product.rating)}</div>
              <span className="text-sm text-gray-600">({product.reviews})</span>
            </div>
            
            {/* Price */}
            <div className="flex items-center gap-2 mb-3">
              <span className="text-lg font-bold text-primary-600">{product.price} ريال</span>
              {product.originalPrice && (
                <span className="text-sm text-gray-500 line-through">{product.originalPrice} ريال</span>
              )}
            </div>
            
            {/* Actions */}
            <div className="flex gap-2">
              <button
                onClick={() => handleAddToCart(product)}
                disabled={!product.inStock}
                className="flex-1 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <ShoppingCart size={16} />
                {product.inStock ? 'أضف للسلة' : 'غير متوفر'}
              </button>
              <Link
                href={`/products/${product.id}`}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors duration-200"
              >
                عرض
              </Link>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
