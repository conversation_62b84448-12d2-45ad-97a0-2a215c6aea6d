'use client'

import { useState } from 'react'
import { MapPin, X, Navigation } from 'lucide-react'
import toast from 'react-hot-toast'

interface LocationModalProps {
  isOpen: boolean
  onClose: () => void
}

interface Location {
  city: string
  district: string
  coordinates?: {
    lat: number
    lng: number
  }
}

const saudiCities = [
  'الرياض',
  'جدة',
  'مكة المكرمة',
  'المدينة المنورة',
  'الدمام',
  'الخبر',
  'الظهران',
  'تبوك',
  'بريدة',
  'خميس مشيط',
  'حائل',
  'نجران',
  'الطائف',
  'الجبيل',
  'ينبع'
]

export default function LocationModal({ isOpen, onClose }: LocationModalProps) {
  const [location, setLocation] = useState<Location>({
    city: '',
    district: ''
  })
  const [isGettingLocation, setIsGettingLocation] = useState(false)

  if (!isOpen) return null

  const handleSaveLocation = () => {
    if (!location.city) {
      toast.error('يرجى اختيار المدينة')
      return
    }

    localStorage.setItem('userLocation', JSON.stringify(location))
    toast.success('تم حفظ موقعك بنجاح')
    onClose()
  }

  const getCurrentLocation = () => {
    setIsGettingLocation(true)
    
    if (!navigator.geolocation) {
      toast.error('المتصفح لا يدعم تحديد الموقع')
      setIsGettingLocation(false)
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords
        
        // In a real app, you would use reverse geocoding to get the address
        // For now, we'll just save the coordinates
        setLocation({
          ...location,
          coordinates: {
            lat: latitude,
            lng: longitude
          }
        })
        
        toast.success('تم تحديد موقعك الحالي')
        setIsGettingLocation(false)
      },
      (error) => {
        toast.error('فشل في تحديد الموقع. يرجى اختيار المدينة يدوياً')
        setIsGettingLocation(false)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-800">تحديد الموقع</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="space-y-6">
          <div className="text-center">
            <MapPin className="w-16 h-16 text-primary-600 mx-auto mb-4" />
            <p className="text-gray-600">
              حدد موقعك للحصول على أفضل تجربة تسوق وتوصيل سريع
            </p>
          </div>

          {/* Current Location Button */}
          <button
            onClick={getCurrentLocation}
            disabled={isGettingLocation}
            className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            {isGettingLocation ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري تحديد الموقع...
              </>
            ) : (
              <>
                <Navigation size={18} />
                استخدام الموقع الحالي
              </>
            )}
          </button>

          <div className="text-center text-gray-500">أو</div>

          {/* Manual Location Selection */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المدينة *
              </label>
              <select
                value={location.city}
                onChange={(e) => setLocation({ ...location, city: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">اختر المدينة</option>
                {saudiCities.map((city) => (
                  <option key={city} value={city}>
                    {city}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الحي (اختياري)
              </label>
              <input
                type="text"
                value={location.district}
                onChange={(e) => setLocation({ ...location, district: e.target.value })}
                placeholder="أدخل اسم الحي"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors duration-200"
            >
              تخطي
            </button>
            <button
              onClick={handleSaveLocation}
              className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-lg transition-colors duration-200"
            >
              حفظ الموقع
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
