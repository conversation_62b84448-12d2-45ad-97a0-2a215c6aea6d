'use client'

import { useState } from 'react'
import { useCart } from '@/contexts/CartContext'
import { useAuth } from '@/contexts/AuthContext'
import EgyptianPaymentMethods from '@/components/payment/EgyptianPaymentMethods'
import { MapPin, User, Phone, Mail, CreditCard, Package, Truck } from 'lucide-react'

export default function CheckoutPage() {
  const { items, totalPrice, clearCart } = useCart()
  const { user } = useAuth()
  
  const [step, setStep] = useState(1)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<any>(null)
  const [orderPlaced, setOrderPlaced] = useState(false)
  
  const [shippingInfo, setShippingInfo] = useState({
    fullName: user?.name || '',
    phone: '',
    email: user?.email || '',
    governorate: '',
    city: '',
    address: '',
    landmark: '',
    notes: ''
  })

  const egyptianGovernorates = [
    'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'الشرقية', 'القليوبية',
    'كفر الشيخ', 'الغربية', 'المنوفية', 'البحيرة', 'دمياط', 'بورسعيد',
    'الإسماعيلية', 'السويس', 'شمال سيناء', 'جنوب سيناء', 'المنيا',
    'بني سويف', 'الفيوم', 'أسيوط', 'سوهاج', 'قنا', 'الأقصر', 'أسوان',
    'البحر الأحمر', 'الوادي الجديد', 'مطروح'
  ]

  const shippingCost = 50 // جنيه مصري
  const finalTotal = totalPrice + shippingCost

  const handleShippingSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateShippingInfo()) {
      setStep(2)
    }
  }

  const validateShippingInfo = () => {
    return shippingInfo.fullName && 
           shippingInfo.phone && 
           shippingInfo.governorate && 
           shippingInfo.city && 
           shippingInfo.address
  }

  const handleOrderSubmit = () => {
    if (!selectedPaymentMethod) return

    // Simulate order placement
    const orderId = 'ORD-' + Date.now()
    
    // Here you would typically send the order to your backend
    console.log('Order placed:', {
      orderId,
      items,
      total: finalTotal,
      shippingInfo,
      paymentMethod: selectedPaymentMethod,
      timestamp: new Date().toISOString()
    })

    setOrderPlaced(true)
    clearCart()
  }

  if (orderPlaced) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="w-8 h-8 text-green-600" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">تم تأكيد طلبك بنجاح!</h1>
              <p className="text-gray-600 mb-6">
                شكراً لك على ثقتك في مركز البدوي. سيتم التواصل معك قريباً لتأكيد الطلب.
              </p>
              
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="font-semibold mb-2">تفاصيل الطلب:</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>المبلغ الإجمالي: {finalTotal.toLocaleString()} جنيه</div>
                  <div>طريقة الدفع: {selectedPaymentMethod?.name}</div>
                  <div>العنوان: {shippingInfo.governorate}, {shippingInfo.city}</div>
                </div>
              </div>

              {selectedPaymentMethod?.id !== 'cash_on_delivery' && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-blue-900 mb-2">خطوات إتمام الدفع:</h4>
                  <ol className="text-sm text-blue-800 text-right space-y-1">
                    {selectedPaymentMethod?.instructions?.map((instruction: string, index: number) => (
                      <li key={index} className="list-decimal list-inside">{instruction}</li>
                    ))}
                  </ol>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => window.location.href = '/'}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg"
                >
                  العودة للرئيسية
                </button>
                <button
                  onClick={() => window.location.href = '/orders'}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg"
                >
                  متابعة الطلبات
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-center">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step >= 1 ? 'bg-primary-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>
                  1
                </div>
                <span className="mr-2 text-sm font-medium">معلومات الشحن</span>
              </div>
              
              <div className={`w-16 h-1 mx-4 ${step >= 2 ? 'bg-primary-600' : 'bg-gray-300'}`} />
              
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step >= 2 ? 'bg-primary-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>
                  2
                </div>
                <span className="mr-2 text-sm font-medium">طريقة الدفع</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {step === 1 && (
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                    <Truck className="w-5 h-5" />
                    معلومات الشحن والتوصيل
                  </h2>
                  
                  <form onSubmit={handleShippingSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          الاسم الكامل *
                        </label>
                        <div className="relative">
                          <User className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                          <input
                            type="text"
                            required
                            value={shippingInfo.fullName}
                            onChange={(e) => setShippingInfo({...shippingInfo, fullName: e.target.value})}
                            className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            placeholder="أدخل اسمك الكامل"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          رقم الهاتف *
                        </label>
                        <div className="relative">
                          <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                          <input
                            type="tel"
                            required
                            value={shippingInfo.phone}
                            onChange={(e) => setShippingInfo({...shippingInfo, phone: e.target.value})}
                            className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            placeholder="01xxxxxxxxx"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          البريد الإلكتروني
                        </label>
                        <div className="relative">
                          <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                          <input
                            type="email"
                            value={shippingInfo.email}
                            onChange={(e) => setShippingInfo({...shippingInfo, email: e.target.value})}
                            className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            placeholder="<EMAIL>"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          المحافظة *
                        </label>
                        <select
                          required
                          value={shippingInfo.governorate}
                          onChange={(e) => setShippingInfo({...shippingInfo, governorate: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        >
                          <option value="">اختر المحافظة</option>
                          {egyptianGovernorates.map((gov) => (
                            <option key={gov} value={gov}>{gov}</option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          المدينة/المنطقة *
                        </label>
                        <input
                          type="text"
                          required
                          value={shippingInfo.city}
                          onChange={(e) => setShippingInfo({...shippingInfo, city: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="أدخل اسم المدينة أو المنطقة"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          علامة مميزة
                        </label>
                        <input
                          type="text"
                          value={shippingInfo.landmark}
                          onChange={(e) => setShippingInfo({...shippingInfo, landmark: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="مثل: بجوار مسجد النور"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        العنوان التفصيلي *
                      </label>
                      <div className="relative">
                        <MapPin className="absolute right-3 top-3 text-gray-400 w-4 h-4" />
                        <textarea
                          required
                          value={shippingInfo.address}
                          onChange={(e) => setShippingInfo({...shippingInfo, address: e.target.value})}
                          className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          rows={3}
                          placeholder="أدخل العنوان التفصيلي (الشارع، رقم المبنى، الدور، الشقة)"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        ملاحظات إضافية
                      </label>
                      <textarea
                        value={shippingInfo.notes}
                        onChange={(e) => setShippingInfo({...shippingInfo, notes: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        rows={2}
                        placeholder="أي ملاحظات خاصة للتوصيل"
                      />
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-lg font-medium"
                    >
                      متابعة إلى الدفع
                    </button>
                  </form>
                </div>
              )}

              {step === 2 && (
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    اختيار طريقة الدفع
                  </h2>
                  
                  <EgyptianPaymentMethods
                    amount={finalTotal}
                    onPaymentSelect={setSelectedPaymentMethod}
                    selectedMethod={selectedPaymentMethod?.id}
                  />

                  <div className="flex gap-4 mt-6">
                    <button
                      onClick={() => setStep(1)}
                      className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 py-3 rounded-lg font-medium"
                    >
                      العودة
                    </button>
                    <button
                      onClick={handleOrderSubmit}
                      disabled={!selectedPaymentMethod}
                      className="flex-1 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-3 rounded-lg font-medium"
                    >
                      تأكيد الطلب
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">ملخص الطلب</h3>
                
                <div className="space-y-3 mb-4">
                  {items.map((item) => (
                    <div key={item.id} className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span className="text-xs text-gray-500">صورة</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">{item.name}</h4>
                        <p className="text-sm text-gray-600">الكمية: {item.quantity}</p>
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {(item.price * item.quantity).toLocaleString()} جنيه
                      </div>
                    </div>
                  ))}
                </div>

                <div className="border-t border-gray-200 pt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>المجموع الفرعي:</span>
                    <span>{totalPrice.toLocaleString()} جنيه</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>رسوم الشحن:</span>
                    <span>{shippingCost.toLocaleString()} جنيه</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                    <span>الإجمالي:</span>
                    <span>{finalTotal.toLocaleString()} جنيه</span>
                  </div>
                </div>

                {step === 1 && (
                  <div className="mt-4 text-sm text-gray-600">
                    <p>سيتم حساب رسوم الشحن حسب المنطقة</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
