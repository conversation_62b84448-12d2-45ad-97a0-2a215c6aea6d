'use client'

import { useState } from 'react'
import { Search, TrendingUp, Package, Zap, Star, ArrowRight } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function QuickSearch() {
  const router = useRouter()
  
  const popularSearches = [
    { term: 'هواتف ذكية', count: '150+ منتج', icon: '📱' },
    { term: 'لابتوب', count: '80+ منتج', icon: '💻' },
    { term: 'ساعات ذكية', count: '45+ منتج', icon: '⌚' },
    { term: 'سماعات', count: '60+ منتج', icon: '🎧' },
    { term: 'كاميرات', count: '35+ منتج', icon: '📷' },
    { term: 'إكسسوارات', count: '200+ منتج', icon: '🔌' }
  ]

  const quickCategories = [
    { name: 'الإلكترونيات', url: '/products?category=electronics', color: 'bg-blue-500', icon: '⚡' },
    { name: 'الهواتف الذكية', url: '/products?category=phones', color: 'bg-green-500', icon: '📱' },
    { name: 'أجهزة الكمبيوتر', url: '/products?category=computers', color: 'bg-purple-500', icon: '💻' },
    { name: 'الإكسسوارات', url: '/products?category=accessories', color: 'bg-orange-500', icon: '🎯' }
  ]

  const featuredServices = [
    { name: 'التوصيل السريع', description: 'خلال 24 ساعة', icon: '🚚', url: '/services/delivery' },
    { name: 'الصيانة الفنية', description: 'خبراء معتمدون', icon: '🔧', url: '/services/maintenance' },
    { name: 'التركيب المنزلي', description: 'في منزلك', icon: '🏠', url: '/services/installation' }
  ]

  const handleQuickSearch = (term: string) => {
    router.push(`/search?q=${encodeURIComponent(term)}`)
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            ابحث واكتشف
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            اكتشف أحدث المنتجات والخدمات في مركز البدوي. ابحث بسهولة أو تصفح التصنيفات المختلفة
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Popular Searches */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <div className="flex items-center gap-2 mb-6">
                <TrendingUp className="w-6 h-6 text-red-500" />
                <h3 className="text-xl font-bold text-gray-800">الأكثر بحثاً</h3>
              </div>
              
              <div className="space-y-3">
                {popularSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickSearch(search.term)}
                    className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{search.icon}</span>
                      <div className="text-right">
                        <div className="font-medium text-gray-800 group-hover:text-primary-600">
                          {search.term}
                        </div>
                        <div className="text-sm text-gray-500">{search.count}</div>
                      </div>
                    </div>
                    <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-primary-600 transform group-hover:translate-x-1 transition-all" />
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Categories */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <div className="flex items-center gap-2 mb-6">
                <Package className="w-6 h-6 text-blue-500" />
                <h3 className="text-xl font-bold text-gray-800">التصنيفات الرئيسية</h3>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                {quickCategories.map((category, index) => (
                  <Link
                    key={index}
                    href={category.url}
                    className="group p-4 rounded-xl border-2 border-gray-100 hover:border-primary-200 transition-all hover:shadow-md"
                  >
                    <div className="text-center">
                      <div className={`w-12 h-12 ${category.color} rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform`}>
                        <span className="text-2xl">{category.icon}</span>
                      </div>
                      <div className="font-medium text-gray-800 group-hover:text-primary-600 text-sm">
                        {category.name}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Featured Services */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <div className="flex items-center gap-2 mb-6">
                <Zap className="w-6 h-6 text-yellow-500" />
                <h3 className="text-xl font-bold text-gray-800">خدمات مميزة</h3>
              </div>
              
              <div className="space-y-4">
                {featuredServices.map((service, index) => (
                  <Link
                    key={index}
                    href={service.url}
                    className="block p-4 rounded-lg border border-gray-100 hover:border-primary-200 hover:shadow-md transition-all group"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span className="text-2xl">{service.icon}</span>
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-800 group-hover:text-primary-600">
                          {service.name}
                        </div>
                        <div className="text-sm text-gray-500">{service.description}</div>
                      </div>
                      <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-primary-600 transform group-hover:translate-x-1 transition-all" />
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-12 bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="group">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Search className="w-8 h-8" />
              </div>
              <h4 className="text-xl font-bold mb-2">بحث متقدم</h4>
              <p className="text-blue-100">ابحث في أكثر من 1000 منتج وخدمة</p>
            </div>
            
            <div className="group">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Star className="w-8 h-8" />
              </div>
              <h4 className="text-xl font-bold mb-2">منتجات مميزة</h4>
              <p className="text-blue-100">اكتشف أحدث المنتجات والعروض الخاصة</p>
            </div>
            
            <div className="group">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Package className="w-8 h-8" />
              </div>
              <h4 className="text-xl font-bold mb-2">خدمات شاملة</h4>
              <p className="text-blue-100">من التوصيل إلى الصيانة والدعم الفني</p>
            </div>
          </div>
        </div>

        {/* Search Tips */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center gap-2 bg-white rounded-full px-6 py-3 shadow-md">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600">
              💡 نصيحة: استخدم البحث الصوتي للعثور على ما تريد بسرعة
            </span>
          </div>
        </div>
      </div>
    </section>
  )
}
